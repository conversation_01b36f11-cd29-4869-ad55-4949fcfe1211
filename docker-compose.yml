x-common-environments: &a1
  ENVIRONMENT: dev
  PROFILE: dev
  SERVER_PORT: 8080
  DEBUG: "true"
  IP_HOST: host.docker.internal
  MENCACHE_HOST: host.docker.internal:11211
  BRANCH_BD_TESTE: hotfix/ia-300
  POSTGRES_API_URL: http://host.docker.internal:8432
  DISCOVERY_URL: http://host.docker.internal:8101

services:
  crm-ms:
    build: .
    image: registry.gitlab.com/plataformazw/flash/crm-ms:master
    environment:
      <<: *a1
    restart: on-failure
    ports:
        - 8110:8110
  postgres:
    image: registry.gitlab.com/plataformazw/docker-pacto/postgres:9.4
    environment:
      <<: *a1
      BDZILLYON_VERSION: 2178
      POSTGRES_MAX_CONNECTIONS: 1000
    restart: always
    ports:
      - 5432:5432
      - 8432:8080
    extra_hosts:
      - host.docker.internal:host-gateway
    command:
      - postgres
      - -c
      - log_statement=all
      - -c
      - max_connections=2000
