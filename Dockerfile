FROM registry.gitlab.com/plataformazw/docker-pacto/maven:3-jdk-8 AS dependencies

WORKDIR /app
COPY pom.xml .
RUN mvn dependency:go-offline

FROM dependencies AS build

COPY docker docker
COPY src src
RUN mvn package

FROM openjdk:8-slim

WORKDIR /app

ENV TZ=America/Sao_Paulo
ENV DEBUG=${DEBUG:-"false"}
ENV PROFILE=${PROFILE:-"hml"}
ENV HEALTHCHECK_KEY=${HEALTHCHECK_KEY:-""}
ENV DISCOVERY_URL=${DISCOVERY_URL:-"http://host.docker.internal:8101"}
ENV SERVER_PORT=${SERVER_PORT:-"8080"}

RUN apt-get update && apt-get install -y curl && rm -rf /var/cache/apt/archives /var/lib/apt/lists/*
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

COPY docker/certs/gd_bundle-g2-g1.crt $JAVA_HOME/lib/security/gd_bundle-g2-g1.crt
COPY docker/bin/*.sh /bin/
RUN chmod +x /bin/*.sh
COPY docker/keys/* /keys/

RUN cd $JAVA_HOME/lib/security/ \
    && keytool -import -alias gd_bundle-g2-g1 -file gd_bundle-g2-g1.crt -keystore cacerts -trustcacerts -storepass changeit

COPY --from=build /app/target/crm-ms.jar .

HEALTHCHECK --interval=1m --timeout=1m --retries=3 --start-period=1m \
  CMD curl -f http://localhost:$SERVER_PORT/crm-ms/v1/health/$HEALTHCHECK_KEY

ENTRYPOINT ["bash", "/bin/entrypoint.sh"]
