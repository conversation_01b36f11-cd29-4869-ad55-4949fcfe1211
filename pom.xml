<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.1</version>
	</parent>
	<groupId>br.com.pacto</groupId>
	<artifactId>crm-ms</artifactId>
	<version>1.1.1</version>
	<name>crm-ms</name>
	<description>CRM microservice</description>

	<properties>
		<artifact.name>crm-ms</artifact.name>
		<version>${project.parent.version}</version>
		<java.version>1.8</java.version>
		<spring-cloud.version>2021.0.1</spring-cloud.version>
		<junit.version>5.8.2</junit.version>
		<org.json.version>20220320</org.json.version>
		<modelmapper.version>3.1.0</modelmapper.version>
		<handlebars.version>4.1.2</handlebars.version>
		<openapi.version>1.8.0</openapi.version>
		<discovery.vendas-online-url>https://vendas.online.sistemapacto.com.br</discovery.vendas-online-url>
		<conversas.ai.url></conversas.ai.url>
	</properties>


	<developers>
		<developer>
			<name>Wellington de Barros Santos</name>
			<email><EMAIL></email>
		</developer>
	</developers>

	<!-- start nexus configuration -->
	<repositories>
		<repository>
			<id>nexusLocal</id>
			<name>Pacto Maven Repository</name>
			<url>https://mussum.ath.cx/nexus/content/groups/public/</url>
			<layout>default</layout>
		</repository>
		<repository>
			<id>nexusSolutioIn</id>
			<name>Solutio-in Maven Repository</name>
			<url>https://mussum.ath.cx/nexus/content/groups/public/</url>
			<layout>default</layout>
		</repository>

	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>pacto solucoes</id>
			<url>https://mussum.ath.cx/nexus/content/groups/public/</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-snapshots</id>
			<url>https://repo.spring.io/snapshot</url>
		</pluginRepository>
		<pluginRepository>
			<id>spring-milestones</id>
			<url>https://repo.spring.io/milestone</url>
		</pluginRepository>
	</pluginRepositories>
	<!-- finish nexus configuration -->

	<dependencies>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.19.1</version>
		</dependency>

		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-okhttp</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
		</dependency>

		<dependency>
			<groupId>br.com.pactosolucoes</groupId>
			<artifactId>pacto-commons</artifactId>
			<version>1.0.18</version>
		</dependency>

		<dependency>
			<groupId>br.com.pactosolucoes</groupId>
			<artifactId>pacto-utils</artifactId>
			<version>0.0.7</version>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.34</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>
				spring-cloud-starter-circuitbreaker-reactor-resilience4j
			</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-loadbalancer</artifactId>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter-engine</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.2</version> <!--$NO-MVN-MAN-VER$ -->
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.2</version> <!--$NO-MVN-MAN-VER$ -->
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-hateoas</artifactId>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- start Swagger configuration -->
		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-webmvc-core</artifactId>
			<version>${openapi.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-ui</artifactId>
			<version>${openapi.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springdoc</groupId>
			<artifactId>springdoc-openapi-hateoas</artifactId>
			<version>${openapi.version}</version>
		</dependency>

		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>${org.json.version}</version>
		</dependency>

		<dependency>
			<groupId>org.modelmapper</groupId>
			<artifactId>modelmapper</artifactId>
			<version>${modelmapper.version}</version>
		</dependency>

		<dependency>
			<groupId>com.github.jknack</groupId>
			<artifactId>handlebars</artifactId>
			<version>${handlebars.version}</version>
		</dependency>


		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>

		<dependency>
			<groupId>javax.validation</groupId>
			<artifactId>validation-api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
		</dependency>

		<dependency>
			<groupId>net.spy</groupId>
			<artifactId>spymemcached</artifactId>
			<version>2.12.1</version>
		</dependency>

		<dependency>
			<groupId>net.spy</groupId>
			<artifactId>spymemcached</artifactId>
			<version>2.12.1</version>
		</dependency>

        <!-- finish Swagger configuration -->
	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.junit</groupId>
				<artifactId>junit-bom</artifactId>
				<version>${junit.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<finalName>${project.name}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${project.parent.version}</version>
				<configuration>
					<executable>true</executable>
					<fork>true</fork>
					<excludes>
						<exclude>
							<groupId>org.projectlombok</groupId>
							<artifactId>lombok</artifactId>
						</exclude>
					</excludes>
				</configuration>
				<executions>
					<execution>
						<id>build-info</id>
						<goals>
							<goal>build-info</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>


	<profiles>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<properties>
				<profile.active>dev</profile.active>
			</properties>
		</profile>
		<profile>
			<id>hml</id>
			<properties>
				<profile.active>hml</profile.active>
			</properties>
		</profile>
		<profile>
			<id>prd</id>

			<properties>
				<profile.active>prd</profile.active>
				<server.port>28105</server.port>
				<sufixoPorta>32</sufixoPorta>
				<spring.datasource.oamd.jdbc-url>******************************${sufixoPorta}/OAMD</spring.datasource.oamd.jdbc-url>
				<spring.r2dbc.url>r2dbc:pool:postgres://localhost:5432/OAMD</spring.r2dbc.url>
			</properties>
		</profile>


		<profile>
			<id>scp</id>
			<properties>
				<sufixoInfra></sufixoInfra>
				<host>zw${sufixoInfra}.pactosolucoes.com.br</host>
				<sshUser>root</sshUser>
				<sshPort>22${sufixoInfra}</sshPort>
				<sshPwd></sshPwd>
				<keyfile>/root/.ssh/id_rsa</keyfile>
			</properties>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-antrun-plugin</artifactId>
						<version>1.8</version>
						<dependencies>
							<dependency>
								<groupId>org.apache.ant</groupId>
								<artifactId>ant-jsch</artifactId>
								<version>1.9.4</version>
							</dependency>
							<dependency>
								<groupId>com.jcraft</groupId>
								<artifactId>jsch</artifactId>
								<version>0.1.54</version>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>scp-exec</id>
								<phase>pre-integration-test</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target name="task-scp">
										<tstamp>
											<format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
										</tstamp>
										<echo message="${now} Transferindo ${sshUser}@${host}:/opt/${artifact.name}/${artifact.name}.jar..."/>
										<sshexec port="${sshPort}" verbose="false"
												 trust="true" host="${host}"
												 username="${sshUser}"
												 passphrase="${sshPwd}"
												 keyfile="${keyfile}"
												 command="mkdir -p /opt/${artifact.name}"/>
										<scp trust="true" file="${basedir}/target/${artifact.name}.jar"
											 port="${sshPort}"
											 verbose="false"
											 passphrase="${sshPwd}"
											 keyfile="${keyfile}"
											 todir="${sshUser}@${host}:/opt/${artifact.name}"/>

										<sshexec port="${sshPort}" verbose="false"
												 trust="true" host="${host}"
												 username="${sshUser}"
												 passphrase="${sshPwd}"
												 keyfile="${keyfile}"
												 command="chmod +x /opt/${artifact.name}/${artifact.name}.jar &amp;&amp; chown glassfish -R /opt/${artifact.name}"/>

										<tstamp>
											<format property="end" pattern="yyyy-MM-dd HH:mm:ss"/>
										</tstamp>
										<echo message="${end} Transferencia concluida!"/>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

		<profile>
			<id>redeploy</id>
			<build>
				<finalName>${artifact.name}</finalName>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-antrun-plugin</artifactId>
						<version>1.8</version>
						<dependencies>
							<dependency>
								<groupId>org.apache.ant</groupId>
								<artifactId>ant-jsch</artifactId>
								<version>1.9.4</version>
							</dependency>
							<dependency>
								<groupId>com.jcraft</groupId>
								<artifactId>jsch</artifactId>
								<version>0.1.54</version>
							</dependency>
							<dependency>
								<groupId>ant-contrib</groupId>
								<artifactId>ant-contrib</artifactId>
								<version>1.0b3</version>
								<exclusions>
									<exclusion>
										<artifactId>ant</artifactId>
										<groupId>ant</groupId>
									</exclusion>
								</exclusions>
							</dependency>
						</dependencies>
						<executions>
							<execution>
								<id>redeploy-exec</id>
								<phase>post-integration-test</phase>
								<goals>
									<goal>run</goal>
								</goals>
								<configuration>
									<target name="task-redeploy">
										<tstamp>
											<format property="now" pattern="yyyy-MM-dd HH:mm:ss"/>
										</tstamp>
										<echo message="${now} Implantar Serviço..."/>
										<sshexec port="${sshPort}" verbose="false"
												 trust="true" host="${host}"
												 username="${sshUser}"
												 passphrase="${sshPwd}"
												 keyfile="${keyfile}"
											 	 command="ln -sf /opt/${artifact.name}/${artifact.name}.jar /etc/init.d/${artifact.name} &amp;&amp; chmod a+x /etc/init.d/${artifact.name} &amp;&amp; /etc/init.d/${artifact.name} force-stop  &amp;&amp; /etc/init.d/${artifact.name} start"/>
										<echo message="${end} OK!"/>
									</target>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>

	</profiles>

</project>
