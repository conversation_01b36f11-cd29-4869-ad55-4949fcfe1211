# CRM-MS

Realizará os contatos em grupo e individual.

## Documentação dos Endpoints

[Swagger Local](http://localhost:8110/crm-ms/swagger)

----------
### An<PERSON> de Iniciar, algumas configurações e leituras são importantes:****

### Configurações:

No aplication.yml localizado dentro de *SRC/MAIN/RESOURCES* é necessário colocar o nome do pacote principal na tag `app.config.root-package` para realizar a leiturda do banco de dados, pois sem essa configuração não será possível realizar a leitura do banco de dados dinâmica.

Exemplo: br.com.ms.teste


Dentro do application-ENVIROMENT há algumas configurações que são necessárias:
- `app.config.security` onde serão liberadas as URLs autorizadas a acessarem o caminho do MS ou da API.
- `queues.NEGOCIO.main` ou `queues.NEGOCIO.sagas` caso esteja trabalhando com o Rabbit MQ é necessário realizar a configuração aqui. Atende-se para a fila de ida (main) e a de rollback / volta (sagas) que deverão ficar cada uma em sua respectiva fila. O mais importante caso esteja trabalhando com o Rabbit MQ é colocar as informações do `spring.rabbitmq`
- `resilience4j.bulkhead` ou `resilience4j.ratelimiter` ou `resilience4j.retry` deverão ser configurados também de acordo com a necessidade. Há a possibilidade de deixar os valores "default", mas lembre-se de que nem tudo que é padrão é bom!

### Configurando o IntelliJ

Leia em [Configurando o Devtools no intellij](https://omonteirox.medium.com/como-utilizar-o-devtools-no-intellij-f50344b5ec93)

----------

## Leituras:

Algumas leituras importantes:

- [Microserviço](https://microservices.io/patterns/microservices.html)
- [Padrões de Projetos](https://refactoring.guru/pt-br/design-patterns/catalog)
- [Padrão de Projeto - Domain Event](https://microservices.io/patterns/data/domain-event.html)
- [BFF](https://microservices.io/patterns/apigateway.html)
- [SAGA](https://microservices.io/patterns/data/saga.html)
- [API Gateway](https://microservices.io/patterns/apigateway.html)
- [Open API](https://swagger.io/specification/)
- [Nível Richardson de Maturidade](https://martinfowler.com/articles/richardsonMaturityModel.html)
- [CQRS](https://martinfowler.com/bliki/CQRS.html)
- [API Design - Sensedia](https://content.sensedia.com/hubfs/API%20Quick%20Reference%20Guide.pdf)
- [API Design - Swagger.io](https://swagger.io/resources/articles/best-practices-in-api-design/)

----------
## SpringBoot application para microservices, API Gateway (BFF).

Nesta estrutura, é utilizado o Spring boot para inicializar o projeto e utiliza o tomcat como servidor.

O projeto está estruturado em "pacto-commons" onde estão as configurações iniciais, configurações do OAMD, Configurações que vem do token, os responses e a exceptions; além de alguns utilitários.

No projeto principal estão as configurações do OpenAPI, configuração do RabbitMQ e feignClient.
 
 
----------
## Estrutura dos Pacotes do Projeto 

Para o Projeto **"pacto-commons"** a estrutura de pacote está assim:

- `br.com.pactosolucoes.commons`: Pacote Principal
- `config`: Configurações de Beans, como: ModelMapper, Gson, Messages, Properties
- `data`: Configurações de banco de dados, entities, repositórios, VO e DTOs. 
- `data.connection`: Configuraçõe de banco de dados, como: conexão para o OAMD e para os clientes (empresas) de acordo com a chave do token (JWT)
- `data.domain`: Onde estão as entities (mapeamento das tabelas)
- `data.repository`: Onde estão os repositórios para os Data Access Objects (DAO) para realizar as buscas no banco
- `data.dto`: Data Transfer Objects que são os inputs (entradas) dos dados
- `data.vo`: Value Object (VO) são os outputs (saídas) dos dados
- `enums.message`: Onde estão os ENUMs de Mensagens, são utilzadas para realizar as exceptions.
- `exception`: Onde estão as Exceptions customizadas para alguns tipos, como: Dados não encontrados (DataNotFoundException)
- `util`: Onde se encontram alguns utilitários, como: FileUtils, StringUtils
- `util.annotation`: Onde se encontram as annotations de Validação e Conversões. Nas validações temos: AllowedValues, CpfCnpj, EmailValidation e PhoneValidation. Alguns utilitários, como: LogExecution, ObjectMapper para converter as Entities do domain para um VO. **DICA**: Para utilizar de uma forma simples o ObjectMapper, implemente a classe `br.com.pactosolucoes.commons.data.ModelData` na Entity e também no VO, ou utilize generics <T> Object.
- `web`: Configurações e utilitário de Controller para facilitar o desenvolvimento.
- `web.config`: Configuração de CORS onde realiza a primeira validação do JWT para depois liberar acesso aos endpoints.
- `web.controller`: Há o base controller para facilitar e padronizar o retorno através do ResponseVO.
- `web.handler`: Onde estão os handlers das exceptions, para não ficar realizando try/catch para tratamento no controller.
- `web.security`: Onde está a validação do token JWT e também da tansação com thread safe das configurações que vieram no token.
- `worker`: Schedules e queue consumers para executar processos em background.

Para este projeto, seja Microserviço ou API, está na seguinte estrutura:

- `pacote principal`, será de acordo com o escolhido na criação do projeto.
- `config.web.swwager`: mantem a mesma estrutura do pacto-commons, porém há a necessidade de estar neste projeto.
- `business01`: Aqui é apenas um exemplo, mas se seguirmos a ideia do 'clean code', do uncle bob, 

> "O que grita a arquitetura da sua aplicação? Quando você olha a estrutura de diretórios de nível mais alto e os arquivos-fonte no pacote de nivel mais alto, eles gritam 'Sistema de Saúde', 'Sistema de Contabilidade'? Ou gritam 'Rails', 'Spring/Hibernate' ou 'ASP'?" - Robert C. Martin - Arquitetura Limpa: O guia do artesão para estrutura e design de software.

- `data`: seguindo a mesma estrutura do **pacto-commons**, exceto o `proxy` e `queue` onde estão as chamadas do feign client e as filas respectivamente.
- `data.inputs`: Data Transfer Objects que são os inputs (entradas) dos dados
- `data.outputs`: Value Object (VO) são os outputs (saídas) dos dados- 
- `data.proxy`: Onde estão configurados os proxies para o feignClient. As configurações de URL deverão estar dentro dos yml's respectivos conforme o ambiente (lcl, hml, prd) e estão no diretorio *SRC/MAIN/RESOURCES/CONFIG/PROXY*. Os responses, deverão estar dentro do `data.proxy.response`
- `data.proxy.response`: onde estão os mapeamentos de respostas dos endpoints chamados.
- `data.queue.config`: Configuração de Senders.
- `data.queue.consumer`: Configurações das escutas da fila para receber mensagens.
- `data.queue.consumer.saga`: Configurações das escutas das fila dos sagas para receber mensagens.
- `data.queue.producer`: Configurações das escutas da fila  para enviar mensagens.
- `data.queue.producer.saga`: Configurações das escutas das fila dos sagas para enviar mensagens.
- `data.repository`: Segue o mesmo conceito do projeto **pacto-commons**
- `service`: Onde estão as classes onde as lógicas de negócio serão executadas.
- `service.contract`: Utilizando o conceito de programação para interfaces, os contratos são os contratos a serem seguidos.
- `service.impl`: Onde são implementadas as lógicas de negócio de acordo com o contrato escolhido.
- `service.exception`: Caso queira lançar alguma exceção de negócio, esta deverá estar dentro do pacote.
- `web`: Onde estão os controles e handlers das exceptions lançadas pelo `service.exception`.
- `web.controller.v1`: Seguindo o conceito de versionar as APIs, teremos aqui todas as nossas primeiras versões. Caso seja necessário criar uma nova versão da API, deve-se criar o pacote v2.
- `web.handler`: Onde estarão as implementações da exception Handler e também os ENUMs das mensagens.
- `web.validator`: Onde estão as implementações de validators de inputs/DTOs.

----------
## Tecnologias Utilizadas

- [Open API](https://springdoc.org/)
- [FeignClient](https://www.baeldung.com/spring-cloud-openfeign)
- [RabbitMQ](https://www.rabbitmq.com/getstarted.html)
- [Actuator](https://www.baeldung.com/spring-boot-actuators)
- [Spring Web](https://spring.io/guides/gs/spring-boot/)
- [Spring Data JPA](https://www.objectdb.com/java/jpa/query/jpql/literal)
- [Spring Cache](https://www.baeldung.com/spring-cache-tutorial)
- [Spring HATEOAS](https://howtodoinjava.com/spring5/hateoas/spring-hateoas-tutorial/)
- [Lombok](https://projectlombok.org/)
- [Resillience4j](https://resilience4j.readme.io/docs/getting-started)
- [CircuiteBreaker](https://spring.io/projects/spring-cloud-circuitbreaker)
- [Retry](https://resilience4j.readme.io/docs/retry)

----------
## Ambientes (Enviroment)

Neste projeto está configurado em 3 ambientes, sendo eles:

- **DEV** = LOCAL quando estamos utilizando em **localhost** e ele é o **PADRÃO**
- **HML** = Para quando for realizar os testes através de UAT.
- **PRD** = Para quando o ambiente for de Produção.


----------
## Empacotando o Projeto

Para realizar o pacto do projeto utilize o comanod **mvn clean package** e adicione o **-P** para escolher o ambiente que deseja realizar o pacote.
Por examplo: Caso queira escolher o ambiente de homologação, utilize o comando: `mvn clean package -P hml`

----------
## Classe para iniciar o projeto:

- `br.com.pacto.ms.contato.SpringbootApplication`
 
----------

usuario e senha: crm-ms

## TESTANDO OS ENDPOINTS NO POSTMAN

Para testar os endpoints no postman, em caso de ambiente local, deve-se "simular" os dados que deseja. Para isso, basta adicionar no header os seguintes items:

- **Authorization**: Não tem um valor definido, pois não será utilizado.
- **empresaId**: O ID da empresa que se deja realizar o teste, neste caso é o código recuperado no banco de dados em questão.
- **empresaChave**: A chave da empresa que está cadastrado no OAMD
- **zwId**: 
- **zwProfile**:
- **username**: Nome do usuário que se deseja realizar o teste.
