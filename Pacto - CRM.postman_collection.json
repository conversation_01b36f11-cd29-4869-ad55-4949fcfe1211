{"info": {"_postman_id": "93fc8737-9ddf-4e84-8e30-d1d2980be6d2", "name": "Pacto - CRM", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "4658185"}, "item": [{"name": "SimularChamada", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "qualquer_coisa", "description": "JWT <PERSON>, não precisa em DESENV", "type": "text"}, {"key": "empresaId", "value": "1", "description": "Id da empresa ou filial (não funciona em PRD)", "type": "text"}, {"key": "empresaChave", "value": "boa", "description": "Chave da empresa no BD (não funciona em PRD)", "type": "text"}, {"key": "zwId", "value": "1", "description": "(não funciona em PRD)", "type": "text"}, {"key": "zwProfile", "value": "1", "description": "(não funciona em PRD)", "type": "text"}, {"key": "username", "value": "teste", "description": "Nome do usuário (não funciona em PRD)", "type": "text"}, {"key": "Accept-Language", "value": "en-US", "description": "Lingaguem de tradução", "type": "text"}], "url": {"raw": "http://localhost:9080/archetype/v1/example/opened-target?page=0&size=5", "protocol": "http", "host": ["localhost"], "port": "9080", "path": ["archetype", "v1", "example", "opened-target"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "5"}]}}, "response": []}]}