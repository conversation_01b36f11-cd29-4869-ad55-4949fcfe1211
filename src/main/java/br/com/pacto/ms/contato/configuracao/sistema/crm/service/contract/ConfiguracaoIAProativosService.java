package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.NotificacacoesDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ProativoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ScheduleNotificationsDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;

public interface ConfiguracaoIAProativosService {
    ResponsePactoConversasVO<?> enviarNotificacaco(ProativoDTO dto);
    ResponsePactoConversasVO<ScheduleNotificationsDTO> obterNotificacoes(Integer codigoEmpresa);
}
