package br.com.pacto.ms.contato.core.data.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum TipoFaseCRMEnum {
    VENDAS(1, "Relacionamento de Vendas", "Vendas"),
    RETENCAO(2, "Relacionamento de Fidelização", "Fidelização"),
    ESTUDIO(3, "Relacionamento de Vendas - Agenda Estúdio", "Studio"),
    INDICADORES(4, "Outros", "Outros"),
    CRMEXTRA(5, "Meta Extra", "Meta Extra"),
    LEAD(6, "Leads", "Leads"),
    CRM_IA(7, "Crm IA", "Crm IA");

    private int codigo;
    private String descricao;
    private String descricaoCurta;
}
