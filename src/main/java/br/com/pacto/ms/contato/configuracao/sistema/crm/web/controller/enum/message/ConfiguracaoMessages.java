package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller;

import br.com.pactosolucoes.utils.enums.APIMessages;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum ConfiguracaoMessages implements APIMessages {
    EMAIL_FECHAMENTO_META_REGISTRADO(1, "email.fechamento.meta.excluido", NO_CONTENT),
    TERMO_SPAN_EXCLUIDO(2, "termo.span.excluido", NO_CONTENT);

    private Integer code;
    private String message;
    private HttpStatus statusCode;
}
