package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.EmpresaEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface EmpresaRepository extends PagingAndSortingRepository<EmpresaEntity, Integer> {

    @Query("SELECT new br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO(e.codigo, e.nome) " +
            "FROM EmpresaEntity e " +
            "WHERE  e.ativa IS TRUE")
    List<ResumoEmpresaVO> consultarEmpresasAtivas();


}
