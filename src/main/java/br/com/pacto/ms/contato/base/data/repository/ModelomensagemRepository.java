package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.ModelomensagemEntity;
import br.com.pacto.ms.contato.base.data.domain.QuestionarioEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ModelomensagemRepository <T> extends PagingAndSortingRepository<ModelomensagemEntity, Integer> {

    Optional<List<ModelomensagemEntity>> findModelomensagemEntitiesBymeiodeenvio(short meio);
    Optional<List<ModelomensagemEntity>> findModelomensagemEntitiesBycodigo(int codigo);
}
