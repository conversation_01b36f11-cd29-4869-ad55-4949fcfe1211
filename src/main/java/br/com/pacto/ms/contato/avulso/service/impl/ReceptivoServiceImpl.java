package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.PassivoEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ReceptivoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ReceptivoFiltro;
import br.com.pacto.ms.contato.avulso.data.pojo.output.PassivoVO;
import br.com.pacto.ms.contato.avulso.data.repository.PassivoRepository;
import br.com.pacto.ms.contato.avulso.service.contract.ReceptivoService;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.exception.DataValidateException;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RequiredArgsConstructor
public class ReceptivoServiceImpl implements ReceptivoService {

    private final ModelMapper modelMapper;
    private final PassivoRepository passivoRepository;
    private final RequestService requestService;

    @Override
    public Page<PassivoVO> buscar(ReceptivoFiltro receptivoFiltro) {
        Specification<PassivoEntity> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (receptivoFiltro.getDia() != null) {
                Date date = Date
                        .from(receptivoFiltro.getDia().toInstant().atZone(ZoneId.systemDefault())
                        .toLocalDate()
                        .atStartOfDay(ZoneId.systemDefault())
                    .toInstant());
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("dia"), date));
                predicates.add(criteriaBuilder.lessThan(root.get("dia"), Date.from(date.toInstant().plus(24, ChronoUnit.HOURS))));
            }

            if (receptivoFiltro.getObjecao() != null) {
                predicates.add(criteriaBuilder.equal(root.get("objecao"), receptivoFiltro.getObjecao()));
            }
            if (receptivoFiltro.getColaboradorResponsavel() != null) {
                predicates.add(criteriaBuilder.equal(root.get("colaboradorresponsavel"), receptivoFiltro.getColaboradorResponsavel()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };

        PageRequest pageable = PageRequest.of(receptivoFiltro.getPagina(), receptivoFiltro.getQuantidade());
        Page<PassivoVO> passivos = passivoRepository.findAll(spec, pageable).map(passivo -> modelMapper.map(passivo, PassivoVO.class));

        return passivos;
    }

    @Override
    public PassivoVO cadastrarReceptivo(ReceptivoDTO receptivoDTO) {
        validarReceptivo(receptivoDTO);

        PassivoEntity passivo = modelMapper.map(receptivoDTO, PassivoEntity.class);
        passivo.setDia(Timestamp.from(Instant.now()));
        passivo.setResponsavelcadastro(this.requestService.getCurrentConfiguration().getZwId());

        passivoRepository.save(passivo);

        return modelMapper.map(passivo, PassivoVO.class);
    }

    @Override
    public PassivoVO obter(Integer id) {
        PassivoEntity passivo = passivoRepository.findById(id).orElseThrow(DataNotFoundException::new);
        return modelMapper.map(passivo, PassivoVO.class);
    }

    private void validarReceptivo(ReceptivoDTO receptivoDTO) {
        if (Objects.isNull(receptivoDTO)) {
            throw new DataValidateException("Necessario Receptivo");
        }

        if (Objects.isNull(receptivoDTO.getNome())) {
            throw new DataValidateException("O campo NOME não pode ser vazio!");
        }

        if (semTelefones(receptivoDTO)) {
            throw new DataValidateException("Informe pelo menos um número de telefone!");
        }
    }

    private boolean semTelefones(ReceptivoDTO receptivoDTO) {
        return (Objects.isNull(receptivoDTO) || receptivoDTO.getTelefonecelular().isEmpty())
                && (Objects.isNull(receptivoDTO.getTelefoneresidencial()) || receptivoDTO.getTelefoneresidencial().isEmpty())
                && (Objects.isNull(receptivoDTO.getTelefonetrabalho()) || receptivoDTO.getTelefonetrabalho().isEmpty());
    }

}
