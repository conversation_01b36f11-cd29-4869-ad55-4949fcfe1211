package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.avulso.data.pojo.output.MailingagendamentoVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.MailingfiltroVO;
import br.com.pacto.ms.contato.base.data.domain.MailingagendamentoEntity;
import br.com.pacto.ms.contato.base.data.domain.MailingfiltrosEntity;
import br.com.pacto.ms.contato.base.data.pojo.input.MailingAgendamentoDTO;
import br.com.pacto.ms.contato.base.data.pojo.input.MailingfiltrosDTO;
import br.com.pacto.ms.contato.base.data.repository.MailingAgendamentoRepository;
import br.com.pacto.ms.contato.base.data.repository.MailingfiltrosRepository;
import br.com.pacto.ms.contato.base.service.contract.MailingAgendamentoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Date;

@AllArgsConstructor

public @Service class MailingAgendamentoServiceImpl  <T> implements MailingAgendamentoService {

    private ModelMapper mapper;
    private MailingAgendamentoRepository hcRepository;
    private MailingfiltrosRepository hcFintroRepository;

    @Override
    @LogExecution
    @ObjectMapper(MailingagendamentoVO.class)
    public T salvar(MailingAgendamentoDTO hcDTO) {
        return (T) hcRepository.save(mapper.map(hcDTO, MailingagendamentoEntity.class));
    }

    @Override
    @LogExecution
    @ObjectMapper(MailingfiltroVO.class)
    public T salvarFiltros(MailingfiltrosDTO hcDTO) {
        return (T) hcFintroRepository.save(mapper.map(hcDTO, MailingfiltrosEntity.class));
    }

    @Override
    public String obterCron(Date data)  {
        Calendar cal = Calendar.getInstance();
        cal.setTime(data);

        Integer horaInicio = 0;
        Integer horaFim = 23;

        String hora = "(" + horaInicio + "-" + horaFim + ")";
        Integer dia = cal.get(Calendar.DAY_OF_MONTH);
        String diaSemana = "*";
        Integer meses = cal.get(Calendar.MONTH) + 1;
        return "H\tH" + hora + "\t" + dia + "\t" + meses + "\t" + diaSemana;
    }
}
