package br.com.pacto.ms.contato.ia.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
public class ContextoContatoVO {
    @Schema(description = "Dia do contato.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date dia;

    @Schema(description = "Observação do contato.")
    private String observacao;

    @Schema(description = "Fase do contato.")
    private ContextoFaseCRMVO fase;
}
