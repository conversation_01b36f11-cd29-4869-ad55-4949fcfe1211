package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

@DynamicUpdate
@DynamicInsert
@Data
@NoArgsConstructor
@Entity
@Table(name = "situacaoclientesinteticodw", schema = "public")
public class SituacaoclientesinteticodwEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "dia", nullable = true)
    private Timestamp dia;
    @Basic
    @Column(name = "codigocliente", nullable = true)
    private Integer codigocliente;
    @Basic
    @Column(name = "matricula", nullable = true)
    private Integer matricula;
    @Basic
    @Column(name = "nomecliente", nullable = true, length = 80)
    private String nomecliente;
    @Basic
    @Column(name = "datanascimento", nullable = true)
    private Timestamp datanascimento;
    @Basic
    @Column(name = "idade", nullable = true)
    private Integer idade;
    @Basic
    @Column(name = "profissao", nullable = true, length = 50)
    private String profissao;
    @Basic
    @Column(name = "colaboradores", nullable = true, length = -1)
    private String colaboradores;
    @Basic
    @Column(name = "codigocontrato", nullable = true)
    private Integer codigocontrato;
    @Basic
    @Column(name = "situacao", nullable = true, length = 20)
    private String situacao;
    @Basic
    @Column(name = "duracaocontratomeses", nullable = true)
    private Integer duracaocontratomeses;
    @Basic
    @Column(name = "mnemonicocontrato", nullable = true, length = 50)
    private String mnemonicocontrato;
    @Basic
    @Column(name = "nomeplano", nullable = true, length = 50)
    private String nomeplano;
    @Basic
    @Column(name = "valorfaturadocontrato", nullable = true, precision = 0)
    private Float valorfaturadocontrato;
    @Basic
    @Column(name = "valorpagocontrato", nullable = true, precision = 0)
    private Float valorpagocontrato;
    @Basic
    @Column(name = "valorparcabertocontrato", nullable = true, precision = 0)
    private Float valorparcabertocontrato;
    @Basic
    @Column(name = "saldocontacorrentecliente", nullable = true, precision = 0)
    private Float saldocontacorrentecliente;
    @Basic
    @Column(name = "datavigenciade", nullable = true)
    private Timestamp datavigenciade;
    @Basic
    @Column(name = "datavigenciaate", nullable = true)
    private Timestamp datavigenciaate;
    @Basic
    @Column(name = "datavigenciaateajustada", nullable = true)
    private Timestamp datavigenciaateajustada;
    @Basic
    @Column(name = "datalancamentocontrato", nullable = true)
    private Timestamp datalancamentocontrato;
    @Basic
    @Column(name = "datarenovacaocontrato", nullable = true)
    private Timestamp datarenovacaocontrato;
    @Basic
    @Column(name = "datarematriculacontrato", nullable = true)
    private Timestamp datarematriculacontrato;
    @Basic
    @Column(name = "dataultimobv", nullable = true)
    private Timestamp dataultimobv;
    @Basic
    @Column(name = "datamatricula", nullable = true)
    private Timestamp datamatricula;
    @Basic
    @Column(name = "dataultimarematricula", nullable = true)
    private Timestamp dataultimarematricula;
    @Basic
    @Column(name = "diasassiduidadeultrematriculaatehoje", nullable = true)
    private Integer diasassiduidadeultrematriculaatehoje;
    @Basic
    @Column(name = "diasacessosemanapassada", nullable = true)
    private Integer diasacessosemanapassada;
    @Basic
    @Column(name = "dataultimoacesso", nullable = true)
    private Timestamp dataultimoacesso;
    @Basic
    @Column(name = "faseatualcrm", nullable = true, length = 50)
    private String faseatualcrm;
    @Basic
    @Column(name = "dataultimocontatocrm", nullable = true)
    private Timestamp dataultimocontatocrm;
    @Basic
    @Column(name = "responsavelultimocontatocrm", nullable = true, length = 50)
    private String responsavelultimocontatocrm;
    @Basic
    @Column(name = "codigoultimocontatocrm", nullable = true)
    private Integer codigoultimocontatocrm;
    @Basic
    @Column(name = "situacaocontrato", nullable = true, length = 20)
    private String situacaocontrato;
    @Basic
    @Column(name = "tipoperiodoacesso", nullable = true, length = 20)
    private String tipoperiodoacesso;
    @Basic
    @Column(name = "datainicioperiodoacesso", nullable = true)
    private Timestamp datainicioperiodoacesso;
    @Basic
    @Column(name = "datafimperiodoacesso", nullable = true)
    private Timestamp datafimperiodoacesso;
    @Basic
    @Column(name = "diasacessosemana2", nullable = true)
    private Integer diasacessosemana2;
    @Basic
    @Column(name = "diasacessosemana3", nullable = true)
    private Integer diasacessosemana3;
    @Basic
    @Column(name = "diasacessosemana4", nullable = true)
    private Integer diasacessosemana4;
    @Basic
    @Column(name = "vezesporsemana", nullable = true)
    private Integer vezesporsemana;
    @Basic
    @Column(name = "diasacessoultimomes", nullable = true)
    private Integer diasacessoultimomes;
    @Basic
    @Column(name = "diasacessomes2", nullable = true)
    private Integer diasacessomes2;
    @Basic
    @Column(name = "diasacessomes3", nullable = true)
    private Integer diasacessomes3;
    @Basic
    @Column(name = "diasacessomes4", nullable = true)
    private Integer diasacessomes4;
    @Basic
    @Column(name = "mediadiasacesso4meses", nullable = true)
    private Integer mediadiasacesso4Meses;
    @Basic
    @Column(name = "telcelcolab", nullable = true, length = -1)
    private String telcelcolab;
    @Basic
    @Column(name = "pesorisco", nullable = true)
    private Integer pesorisco;
    @Basic
    @Column(name = "enviosmsmarcadoclassif", nullable = true)
    private Boolean enviosmsmarcadoclassif;
    @Basic
    @Column(name = "smsrisco", nullable = true)
    private Integer smsrisco;
    @Basic
    @Column(name = "codigopessoa", nullable = true)
    private Integer codigopessoa;
    @Basic
    @Column(name = "codigousuariomovel", nullable = true)
    private Integer codigousuariomovel;
    @Basic
    @Column(name = "empresacliente", nullable = true)
    private Integer empresacliente;
    @Basic
    @Column(name = "sexocliente", nullable = true, length = 2)
    private String sexocliente;
    @Basic
    @Column(name = "telefonescliente", nullable = true, length = -1)
    private String telefonescliente;
    @Basic
    @Column(name = "situacaomatriculacontrato", nullable = true, length = 2)
    private String situacaomatriculacontrato;
    @Basic
    @Column(name = "nomeconsulta", nullable = true, length = 80)
    private String nomeconsulta;
    @Basic
    @Column(name = "cpf", nullable = true, length = 14)
    private String cpf;
    @Basic
    @Column(name = "codacessocliente", nullable = true, length = 20)
    private String codacessocliente;
    @Basic
    @Column(name = "modalidades", nullable = true, length = -1)
    private String modalidades;
    @Basic
    @Column(name = "frequenciasemanal", nullable = true)
    private Short frequenciasemanal;
    @Basic
    @Column(name = "saldocreditotreino", nullable = true)
    private Short saldocreditotreino;
    @Basic
    @Column(name = "validarsaldocreditotreino", nullable = true)
    private Boolean validarsaldocreditotreino;
    @Basic
    @Column(name = "quantidadediasextra", nullable = true)
    private Short quantidadediasextra;
    @Basic
    @Column(name = "nraulaexperimental", nullable = true)
    private Integer nraulaexperimental;
    @Basic
    @Column(name = "totalcreditotreino", nullable = true)
    private Short totalcreditotreino;
    @Basic
    @Column(name = "descricoesmodalidades", nullable = true, length = -1)
    private String descricoesmodalidades;
    @Basic
    @Column(name = "situacaocontratooperacao", nullable = true, length = 255)
    private String situacaocontratooperacao;
    @Basic
    @Column(name = "crossfit", nullable = true)
    private Boolean crossfit;
    @Basic
    @Column(name = "datasaidaacesso", nullable = true)
    private Timestamp datasaidaacesso;
    @Basic
    @Column(name = "statusbg", nullable = true, length = 1)
    private String statusbg;
    @Basic
    @Column(name = "datacadastro", nullable = true)
    private Timestamp datacadastro;
    @Basic
    @Column(name = "existeparcvencidacontrato", nullable = true)
    private Boolean existeparcvencidacontrato;
    @Basic
    @Column(name = "empresausafreepass", nullable = true)
    private Boolean empresausafreepass;
    @Basic
    @Column(name = "ultimavisita", nullable = true)
    private Timestamp ultimavisita;
    @Basic
    @Column(name = "cargo", nullable = true, length = -1)
    private String cargo;
    @Basic
    @Column(name = "freepass", nullable = true)
    private Boolean freepass;
    @Basic
    @Column(name = "endereco", nullable = true, length = -1)
    private String endereco;
    @Basic
    @Column(name = "cidade", nullable = true, length = -1)
    private String cidade;
    @Basic
    @Column(name = "bairro", nullable = true, length = -1)
    private String bairro;
    @Basic
    @Column(name = "estadocivil", nullable = true, length = -1)
    private String estadocivil;
    @Basic
    @Column(name = "rg", nullable = true, length = -1)
    private String rg;
    @Basic
    @Column(name = "uf", nullable = true, length = 2)
    private String uf;
    @Basic
    @Column(name = "telefonesconsulta", nullable = true, length = -1)
    private String telefonesconsulta;
    @Basic
    @Column(name = "cpfconsulta", nullable = true, length = 14)
    private String cpfconsulta;
}
