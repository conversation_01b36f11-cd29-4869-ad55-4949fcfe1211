package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ConfiguracaosistemaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Boolean acessochamada;

	private Boolean agruparremessascartaoedi;

	private Boolean agruparremessasgetnet;

	private Integer aliquotaproduto;

	private Integer aliquotaservico;

	private Boolean alteracaodatabasecontrato;

	private Boolean apresentarmarketplace;

	private Boolean ativarverificarcartao;

	private Boolean barrardevedorvendarapida;

	private Boolean bloquearacessoseparcelaaberta;

	private Boolean cancelarcontratonaunidadeorigemaotransferiraluno;

	private Integer carencia;

	private Integer carenciarenovacao;

	private String chaveprivadasesc;

	private String chavepublicasesc;

	private Integer coletorchamada;

	private Boolean controleacessomultiplasempresasporplano;

	private Boolean cpfvalidar;

	private Date dataexpiracao;

	private Timestamp datafimdesconsideraracessorisco;

	private Timestamp datainiciodesconsideraracessorisco;

	private Timestamp datasincronizacaooamd;

	private Timestamp dataultimarepescagem;

	private Boolean defaultenderecocorrespondecia;

	private Boolean definirdatainicioplanosrecorrencia;

	private Integer diaprorataarmario;

	private Integer diasparabloqueio;

	private Boolean ecfapenasplano;

	private Boolean ecfporpagamento;

	private String emailcontapagdigital;

	private String emailsfechamentoacessos;

	private Boolean enviarremessasremotamente;

	private Boolean enviarsmsautomatico;

	private Boolean exibirmodalusuariosinativos;

	private Boolean forcarcodigoalternativoacesso;

	private Boolean forcarutilizacaoplanoantigo;

	private Boolean habilitarcanalcliente;

	private Boolean habilitargestaoarmarios;

	private Boolean imprimirrecibopagtomatricial;

	private Boolean itemvendaavulsaautomatico;

	private float juroparcela;

	private Boolean justfit;

	private Boolean lancamentocontratosiguais;

	private Integer localacessochamada;

	private Boolean marcarpresencapeloacesso;

	private String mascaramatricula;

	private String mascaratelefone;

	private float multa;

	private Boolean nomearquivoremessapadraotivit;

	private Boolean nomedatanascvalidar;

	private String nomenclaturavendacredito;

	private Integer nrdiasavencer;

	private Integer nrdiasprorata;

	private Integer nrdiasvigentequestionarioprimeiracompra;

	private Integer nrdiasvigentequestionariorematricula;

	private Integer nrdiasvigentequestionarioretorno;

	private Integer nrdiasvigentequestionarioretornocompra;

	private Integer nrdiasvigentequestionariovisita;

	private String numerocielo;

	private Boolean permitirreplicarplanoredeempresa;

	private Boolean priorizarvendarapida;

	private Boolean propagaraassinaturadigital;

	private Integer qtddiaprimeiraparcelavencidaestornarcontrato;

	private Integer qtddiasestornoautomaticocontrato;

	private Integer qtddiasexpirarsenha;

	private Integer qtdfaltainiciopeso2;

	private Integer qtdfaltapeso1;

	private Integer qtdfaltapeso3;

	private Integer qtdfaltaterminopeso2;

	private Integer questionarioprimeiracompra;

	private Integer questionarioprimeiravisita;

	private Integer questionariorematricula;

	private Integer questionarioretorno;

	private Integer questionarioretornocompra;

	private Boolean rodarsqlsbancoinicial;

	private Integer seqnotafiscalfamilia;

	private Integer seqprocessoimportacao;

	private Integer sequencialarquivo;

	private Integer sequencialitem;

	private Boolean sesc;

	private String tokencontapagdigital;

	private String tokencontasms;

	private Integer toleranciadiascontratovencido;

	private Integer toleranciapagamento;

	private Boolean transferirautorizacaocobranca;

	private String urlgoogleagenda;

	private String urlrecorrencia;

	private Boolean usaaprovafacil;

	private Boolean usaecf;

	private Boolean usardigitalcomoassinatura;

	private Boolean usarnomeresponsavelnota;

	private Boolean usarsistemainternacional;

	private Boolean usarverificadorremessasrejeitadas;

	private Boolean utilizarformatommddyyydtnascimento;

	private Boolean utilizarservicosesisc;

	private Boolean utilizarsistemaparaclube;

	private Boolean utilizartipoplano;

	private Boolean validarcontatometa;

	private Boolean validarcpfduplicado;

	private Boolean validarcpfresponsaveis;

	private Integer vencimentocolaborador;

	private Integer versaocanalcliente;


}