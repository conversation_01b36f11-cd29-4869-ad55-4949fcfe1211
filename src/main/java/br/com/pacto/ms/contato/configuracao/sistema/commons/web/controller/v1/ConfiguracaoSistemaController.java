package br.com.pacto.ms.contato.configuracao.sistema.commons.web.controller.v1;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_COMUM;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_COMUM_DESCRICAO;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import br.com.pacto.ms.contato.configuracao.sistema.commons.service.contract.ConfiguracaosistemaService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@Tag(name = CONFIGURACAO_COMUM, description = CONFIGURACAO_COMUM_DESCRICAO)

@RestController
@RequestMapping(value = "/v1/comum")
public class ConfiguracaoSistemaController extends BaseController {

    @Autowired
    private ConfiguracaosistemaService<?> configuracaosistemaService;

    @Operation(summary = "Validação contato meta",
            description = "Verifica se tem o cadastrado o contato meta. Se não houver, lança uma exception.")
    @GetMapping("/temCadastroContatoMeta")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<?> verificarCadastroContatoMeta() {
        configuracaosistemaService.verificarCadastroContatoMeta();
        return super.finish();
    }
}
