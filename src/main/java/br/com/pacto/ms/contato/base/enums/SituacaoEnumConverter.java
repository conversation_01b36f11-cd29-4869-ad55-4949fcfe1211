package br.com.pacto.ms.contato.base.enums;

import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SituacaoEnumConverter implements AttributeConverter<SituacaoEnum, String> {

    @Override
    public String convertToDatabaseColumn(SituacaoEnum SituacaoEnum) {
        if(SituacaoEnum == null){
            return null;
        }
        return SituacaoEnum.getCodigo();
    }

    @Override
    public SituacaoEnum convertToEntityAttribute(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }
        return SituacaoEnum.findByCodigo(id);
    }
}
