package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.avulso.data.repository.EmpresaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoEmailFechamentoMetaEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoEmailFechamentoMetaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoEmailFechamentoMetaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoEmailFechamentoMetaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoEmailFechamentoMetaService;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@AllArgsConstructor
public class ConfiguracaoEmailFechamentoMetaServiceImpl<T, DTO> extends Object implements ConfiguracaoEmailFechamentoMetaService<T, DTO> {

    private ModelMapper mapper;
    private ConfiguracaoEmailFechamentoMetaRepository repository;
    private EmpresaRepository empresaRepository;


    @Override
    @ObjectMapper(ConfiguracaoEmailFechamentoMetaVO.class)
    public T consultar() {
        return (T) repository.findAll();
    }

    @Override
    @Transactional
    @ObjectMapper(ConfiguracaoEmailFechamentoMetaVO.class)
    public T alterarTodos(List<ConfiguracaoEmailFechamentoMetaDTO> configuracaoEmailFechamentoMetaDTOS) {
        this.repository.deleteAll();
        List<ConfiguracaoEmailFechamentoMetaEntity> entities = configuracaoEmailFechamentoMetaDTOS
                .stream()
                .map(dto -> mapper.map(dto, ConfiguracaoEmailFechamentoMetaEntity.class))
                .collect(java.util.stream.Collectors.toList());
        this.repository.saveAll(entities);

        return (T) entities;
    }

    @Override
    @ObjectMapper(ConfiguracaoEmailFechamentoMetaVO.class)
    public T incluir(ConfiguracaoEmailFechamentoMetaDTO configuracaoEmailFechamentoMetaDTO) {
        ConfiguracaoEmailFechamentoMetaEntity configuracaoEmailFechamentoMetaEntity = mapper.map(configuracaoEmailFechamentoMetaDTO, ConfiguracaoEmailFechamentoMetaEntity.class);
        configuracaoEmailFechamentoMetaEntity.setEmpresa(this.empresaRepository.findById(configuracaoEmailFechamentoMetaDTO.getEmpresa()).orElseThrow(DataNotMatchException::new));
        return (T) this.repository.save(configuracaoEmailFechamentoMetaEntity);
    }

    @Override
    public void excluir(Integer codigo) {
        ConfiguracaoEmailFechamentoMetaEntity configuracaoEmailFechamentoMetaEntity = this.repository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        this.repository.delete(configuracaoEmailFechamentoMetaEntity);
    }

    @Override
    @ObjectMapper(ConfiguracaoEmailFechamentoMetaVO.class)
    public T alterar(Integer codigo, ConfiguracaoEmailFechamentoMetaDTO configuracaoEmailFechamentoMetaDTO) {
        ConfiguracaoEmailFechamentoMetaEntity configuracaoEmailFechamentoMetaEntity = this.repository.findById(codigo).orElseThrow(DataNotMatchException::new);
        configuracaoEmailFechamentoMetaEntity.setEmpresa(this.empresaRepository.findById(configuracaoEmailFechamentoMetaDTO.getEmpresa()).orElseThrow(DataNotMatchException::new));
        this.mapper.map(configuracaoEmailFechamentoMetaDTO, configuracaoEmailFechamentoMetaEntity);
        return (T) this.repository.save(configuracaoEmailFechamentoMetaEntity);
    }
}
