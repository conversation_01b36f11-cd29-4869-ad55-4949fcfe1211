package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.domain.PerguntaEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class QuestionarioPerguntaVO {

    @Schema(description = "Código da pergunta do questionário, não nulo.")
    private Long codigo;

    @Schema(description = "Entidade da pergunta associada, não nula.")
    private PerguntaEntity pergunta;

    @Schema(description = "Código do questionário, não nulo.")
    private Integer questionario;

    @Schema(description = "Indica se a pergunta é obrigatória.")
    private Boolean obrigatoria;

    @Schema(description = "Número da questão no questionário.")
    private Integer nrQuestao;

}
