package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.TermoSpamDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.TermoSpamVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.TermoSpanService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.ConfiguracaoMessages;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de termos para identificação de spam")
@RestController
@RequestMapping(value = "/v1/configuracao/termo-span")
public class TermoSpanController extends BaseController {

    @Autowired
    private TermoSpanService<TermoSpamVO, TermoSpamDTO> service;
    @Autowired
    private RequestService requestService;

    @Operation(summary = "Consultar termos fiscalizados pelos programas anti-spam")
    @LogExecution
    @GetMapping
    public ResponseEntity<TermoSpamVO> consultar() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<TermoSpamVO>) super.finish(service.consultar());
    }

    @Operation(summary = "Excluir termo fiscalizados pelos programas anti-spam")
    @LogExecution
    @DeleteMapping("/{termo}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<?> excluir(@Parameter(name = "termo", description = "Termo cadastrado", example = "PROMOÇÃO")
                                     @PathVariable String termo){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        this.service.excluir(termo);
        return super.finish(ConfiguracaoMessages.TERMO_SPAN_EXCLUIDO);
    }

    @Operation(summary = "Cadastrar termo fiscalizados pelos programas anti-spam")
    @LogExecution
    @PostMapping
    public ResponseEntity<TermoSpamDTO> incluir(@RequestBody @Valid List<TermoSpamDTO> termos){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<TermoSpamDTO>) super.finish(service.incluir(termos));
    }
}
