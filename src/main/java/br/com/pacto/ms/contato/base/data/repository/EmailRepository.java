package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.EmailEntity;
import br.com.pacto.ms.contato.base.data.domain.PessoaEmail;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface EmailRepository extends PagingAndSortingRepository<EmailEntity, Integer> {

    @Query(  " select  new br.com.pacto.ms.contato.base.data.domain.PessoaEmail(p.nome, e.email)  "
            + " from EmailEntity e "
            + "		join ClientEntity c ON e.pessoa = c.pessoa"
            + " join PessoaEntity p on p.codigo = c.pessoa"
            + " where c.codigomatricula = :matricula  and e.emailcorrespondencia = true ")
    Optional<List<PessoaEmail>> buscarPorMatricula(int matricula);

    @Query(  " select  new br.com.pacto.ms.contato.base.data.domain.PessoaEmail(p.nome, e.email)  "
            + " from EmailEntity e "
            + "		join ClientEntity c ON e.pessoa = c.pessoa"
            + " join PessoaEntity p on p.codigo = c.pessoa"
            + " where c.codigo = :cliente  and e.emailcorrespondencia = true ")
    Optional<List<PessoaEmail>> buscarPorCliente(int cliente);
}
