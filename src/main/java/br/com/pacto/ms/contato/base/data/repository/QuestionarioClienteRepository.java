package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.QuestionarioClienteEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface QuestionarioClienteRepository extends PagingAndSortingRepository<QuestionarioClienteEntity, Integer> {

    @Query("select qc from QuestionarioClienteEntity qc where qc.cliente = :cliente")
    List<QuestionarioClienteEntity> consultarPorQuesrionarioCliente(Integer cliente);

}
