package br.com.pacto.ms.contato.log.data.listener;

import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import br.com.pacto.ms.contato.log.data.domain.LogEntity;
import br.com.pacto.ms.contato.log.data.pojo.output.CampoAlteradoVO;
import br.com.pacto.ms.contato.log.service.contract.LogService;
import br.com.pacto.ms.contato.log.service.contract.UsuarioService;
import br.com.pactosolucoes.commons.data.connection.ClientsDatabaseConfig;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.util.AnnotationUtils;
import br.com.pactosolucoes.commons.util.annotation.LogFieldIgnore;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.orm.jpa.EntityManagerFactoryUtils;
import org.springframework.stereotype.Component;

import javax.persistence.*;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static br.com.pactosolucoes.commons.util.AnnotationUtils.getLogFieldValueTransform;
import static br.com.pactosolucoes.commons.util.AnnotationUtils.getLogTagTransform;

@AllArgsConstructor
@NoArgsConstructor
@Component
public class LogListener {

    static private LogService logService;
    static private UsuarioService usuarioService;
    static private RequestService requestService;
    static private Logger logger = LoggerFactory.getLogger(LogListener.class);
    private ModelMapper modelMapper = new ModelMapper();

    @Autowired
    public void setUsuarioService(UsuarioService usuarioService){
        LogListener.usuarioService = usuarioService;
    }

    @Autowired
    public void setLogService(LogService logService){
        LogListener.logService = logService;
    }

    @Autowired
    public void setRequestService(RequestService requestService){
        LogListener.requestService = requestService;
    }

    public static <T> T cloneObject(T original) {
        try {
            Class<?> clazz = original.getClass();
            T clone = (T) clazz.getDeclaredConstructor().newInstance();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                if (!Modifier.isStatic(field.getModifiers())) {
                    field.setAccessible(true);
                    Object value = field.get(original);
                    field.set(clone, value);
                }
            }

            return clone;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    @PostPersist
    public void prePersist(Object entidade) {
        EntityManager entityManager = getEntityManager();

        Integer id = getId(entidade);
        if (entityManager != null && id != null) {
            List<CampoAlteradoVO> camposIncluidos = prepararCamposIncluidos(entidade);

            if (!camposIncluidos.isEmpty()) {
                registrarInclusao(entidade, camposIncluidos);
            }
        }
    }


    private List<CampoAlteradoVO> prepararCamposIncluidos(Object entidade) {
        List<CampoAlteradoVO> camposAlterados = new ArrayList<>();
        Field[] campos = entidade.getClass().getDeclaredFields();

        for (Field campo : campos) {
            if(campo.getName().equals("codigo") || campo.getName().equals("id")){
                continue;
            }

            if(AnnotationUtils.isNotationPresent(entidade, campo.getName(), LogFieldIgnore.class)){
                continue;
            }

            campo.setAccessible(true);

            try {
                String valorCampo = getValorCampo(entidade, campo);
                if(valorCampo != null){
                    CampoAlteradoVO campoAlteradoVO = CampoAlteradoVO.builder()
                            .campo(campo.getName())
                            .valorAlterado(valorCampo)
                            .build();
                    camposAlterados.add(campoAlteradoVO);
                }
            } catch (IllegalAccessException e) {
                logger.error("Não foi possível registrar o log de inclusão do atributo "+campo.getName()+" da entidade "+entidade.getClass());
            } catch (NoSuchFieldException e) {
                logger.error("Não foi possível registrar o log. Atributos da entidade não foram econtrados: "+e.getMessage());
            }
        }

        return camposAlterados;
    }

    private static String getValorCampo(Object entidade, Field campo) throws IllegalAccessException, NoSuchFieldException {
        Object valorOuObjetoRelacionado = campo.get(entidade);
        String valor = null;
        Field atributoEntidadeRelacionada = null;

        if(valorOuObjetoRelacionado != null || valorOuObjetoRelacionado instanceof Enum){

            for (Field campoEntidadeRelacionada : valorOuObjetoRelacionado.getClass().getDeclaredFields()) {
                if(campoEntidadeRelacionada.getName().equals("nome") || campoEntidadeRelacionada.getName().equals("descricao")){
                    atributoEntidadeRelacionada = campoEntidadeRelacionada;
                    break;
                }
            }
        }

        try {
            String valorTransform = getLogFieldValueTransform(entidade, campo.getName());
            if(valorTransform != null){
                return valorTransform;
            }
        } catch (InstantiationException e) {
            logger.error("Não foi possível registrar o log de inclusão do atributo. Houve um erro ao executar o valueTransform: "+e.getMessage());
        }

        if(atributoEntidadeRelacionada != null){
            atributoEntidadeRelacionada.setAccessible(true);
            Object valorDoAtributorelacionado = atributoEntidadeRelacionada.get(valorOuObjetoRelacionado);
            valor = valorDoAtributorelacionado != null ? valorDoAtributorelacionado.toString() : null;
        }else if(valorOuObjetoRelacionado != null){
            valor = valorOuObjetoRelacionado.toString();
        }

        return valor;
    }


    private void registrarInclusao(Object entidade, List<CampoAlteradoVO> camposIncluidos) {
        String chavePrimaria = Objects.requireNonNull(getId(entidade)).toString();
        String nomeEntidade = Objects.requireNonNull(AnnotationUtils.getTableName((entidade.getClass())));
        String nomeEntidadeDescricao =  AnnotationUtils.getEntityDescription(entidade);

        try {
            PessoaEntity pessoa = usuarioService.consultarPessoa(requestService.getCurrentConfiguration().getZwId());
            String tags = getLogTagTransform(entidade);
            camposIncluidos.forEach(campoAlteradoVO -> {

                String nomeCampo = campoAlteradoVO.getCampo();

                LogEntity logEntity = new LogEntity().builder()
                        .chavePrimaria(chavePrimaria)
                        .dataAlteracao(new java.util.Date())
                        .nomeEntidade(nomeEntidade)
                        .nomeEntidadeDescricao(nomeEntidadeDescricao)
                        .responsavelAlteracao(pessoa.getNome())
                        .pessoa(pessoa.getCodigo())
                        .nomeCampo(campoAlteradoVO.getCampo())
                        .nomeCampoDescricao(AnnotationUtils.getFieldDescription(entidade, nomeCampo))
                        .valorCampoAlterado(campoAlteradoVO.getValorAlterado())
                        .operacao("INCLUSAO")
                        .tags(tags)
                        .build();
                logService.incluir(logEntity);
            });
        }catch (DataNotFoundException | InstantiationException | IllegalAccessException e){
            logger.error("Não foi possível registrar o log de exlusão da entidade "+nomeEntidade+" pois não foi possível encontrar a pessoa do usuário logado");
        }
    }

    @PreRemove
    public void preRemove(Object entidade) {
        EntityManager entityManager = getEntityManager();

        Integer id = getId(entidade);
        if (entityManager != null && id != null) {
            List<CampoAlteradoVO> camposExcluidos = prepararCamposExcluidos(entidade);

            if (!camposExcluidos.isEmpty()) {
                registrarExclusao(entidade, camposExcluidos);
            }
        }
    }

    private void registrarExclusao(Object entidade, List<CampoAlteradoVO> camposExcluidos) {
        String chavePrimaria = Objects.requireNonNull(getId(entidade)).toString();
        String nomeEntidade = Objects.requireNonNull(AnnotationUtils.getTableName((entidade.getClass())));
        String nomeEntidadeDescricao =  AnnotationUtils.getEntityDescription(entidade);

        try {
            PessoaEntity pessoa = usuarioService.consultarPessoa(requestService.getCurrentConfiguration().getZwId());
            String tags = getLogTagTransform(entidade);
            camposExcluidos.forEach(campoAlteradoVO -> {

                String nomeCampo = campoAlteradoVO.getCampo();

                LogEntity logEntity = new LogEntity().builder()
                        .chavePrimaria(chavePrimaria)
                        .dataAlteracao(new java.util.Date())
                        .nomeEntidade(nomeEntidade)
                        .nomeEntidadeDescricao(nomeEntidadeDescricao)
                        .responsavelAlteracao(pessoa.getNome())
                        .pessoa(pessoa.getCodigo())
                        .nomeCampo(campoAlteradoVO.getCampo())
                        .nomeCampoDescricao(AnnotationUtils.getFieldDescription(entidade, nomeCampo))
                        .valorCampoAnterior(campoAlteradoVO.getValorAntigo())
                        .tags(tags)
                        .operacao("EXCLUSAO")
                        .build();
                logService.incluir(logEntity);
            });
        }catch (DataNotFoundException | InstantiationException | IllegalAccessException e){
            logger.error("Não foi possível registrar o log de exlusão da entidade "+nomeEntidade+" pois não foi possível encontrar a pessoa do usuário logado");
        }
    }
    @PreUpdate
    public void preUpdate(Object entidadeNova) {
        EntityManager entityManager = getEntityManager();

        Integer id = getId(entidadeNova);
        if (entityManager != null && id != null) {
            Object cloneEntidadeNova = cloneObject(entidadeNova);

            Object entidadeAntiga = entityManager.find(entidadeNova.getClass(), id);
            if (entidadeAntiga != null) {
                entityManager.refresh(entidadeAntiga);
                List<CampoAlteradoVO> camposAlterados = prepararCamposAlterados(entidadeAntiga, cloneEntidadeNova);

                if (!camposAlterados.isEmpty()) {
                    registrarAlteracao(entidadeAntiga, cloneEntidadeNova, camposAlterados);
                }
                this.modelMapper.map(cloneEntidadeNova, entidadeNova);
            }
        }
    }

    private void registrarAlteracao(Object entidadeAntiga, Object entidadeNova, List<CampoAlteradoVO> camposAlterados) {
        String chavePrimaria = Objects.requireNonNull(getId(entidadeNova)).toString();
        String nomeEntidade = Objects.requireNonNull(AnnotationUtils.getTableName((entidadeNova.getClass())));
        String nomeEntidadeDescricao =  AnnotationUtils.getEntityDescription(entidadeNova);

        try {
            PessoaEntity pessoa = usuarioService.consultarPessoa(requestService.getCurrentConfiguration().getZwId());
            String tags = getLogTagTransform(entidadeAntiga);
            camposAlterados.forEach(campoAlteradoVO -> {

                String nomeCampo = campoAlteradoVO.getCampo();

                LogEntity logEntity = new LogEntity().builder()
                        .chavePrimaria(chavePrimaria)
                        .dataAlteracao(new java.util.Date())
                        .nomeEntidade(nomeEntidade)
                        .nomeEntidadeDescricao(nomeEntidadeDescricao)
                        .responsavelAlteracao(pessoa.getNome())
                        .pessoa(pessoa.getCodigo())
                        .nomeCampo(campoAlteradoVO.getCampo())
                        .nomeCampoDescricao(AnnotationUtils.getFieldDescription(entidadeNova, nomeCampo))
                        .valorCampoAnterior(campoAlteradoVO.getValorAntigo())
                        .valorCampoAlterado(campoAlteradoVO.getValorAlterado())
                        .operacao("ALTERACAO")
                        .tags(tags)
                        .build();
                logService.incluir(logEntity);
            });
        }catch (DataNotFoundException | InstantiationException | IllegalAccessException e){
            logger.error("Não foi possível registrar o log de alteração da entidade "+nomeEntidade+" pois não foi possível encontrar a pessoa do usuário logado");
        }
    }

    private List<CampoAlteradoVO> prepararCamposExcluidos(Object entidadeAntiga) {
        List<CampoAlteradoVO> camposAlterados = new ArrayList<>();
        Field[] campos = entidadeAntiga.getClass().getDeclaredFields();

        for (Field campo : campos) {
            if(campo.getName().equals("codigo") || campo.getName().equals("id")){
                continue;
            }

            if(AnnotationUtils.isNotationPresent(entidadeAntiga, campo.getName(), LogFieldIgnore.class)){
                continue;
            }

            campo.setAccessible(true);

            try {
                String valorCampo = getValorCampo(entidadeAntiga, campo);
                if(valorCampo != null){
                    CampoAlteradoVO campoAlteradoVO = CampoAlteradoVO.builder()
                            .campo(campo.getName())
                            .valorAntigo(getValorCampo(entidadeAntiga, campo))
                            .build();
                    camposAlterados.add(campoAlteradoVO);
                }
            } catch (IllegalAccessException | NoSuchFieldException e) {
                logger.error("Não foi possível registrar o log de exclusão do atributo "+campo.getName()+" da entidade "+entidadeAntiga.getClass());
                logger.error(e.getMessage());
            }
        }

        return camposAlterados;
    }

    private List<CampoAlteradoVO> prepararCamposAlterados(Object entidadeAntiga, Object entidadeAtual) {
        List<CampoAlteradoVO> camposAlterados = new ArrayList<>();
        Field[] campos = entidadeAtual.getClass().getDeclaredFields();

        for (Field campo : campos) {

            if(AnnotationUtils.isNotationPresent(entidadeAntiga, campo.getName(), LogFieldIgnore.class)){
                continue;
            }

            campo.setAccessible(true);

            try {
                String valorAntigo = campo.get(entidadeAntiga) == null ? null : campo.get(entidadeAntiga).toString();
                String valorAlterado = campo.get(entidadeAtual) == null ? null : campo.get(entidadeAtual).toString();

                if (!Objects.equals(valorAntigo, valorAlterado)) {
                    CampoAlteradoVO campoAlteradoVO = CampoAlteradoVO.builder()
                            .campo(campo.getName())
                            .valorAntigo(getValorCampo(entidadeAntiga, campo))
                            .valorAlterado(getValorCampo(entidadeAtual, campo))
                            .build();
                    camposAlterados.add(campoAlteradoVO);
                }
            } catch (IllegalAccessException | NoSuchFieldException e) {
                logger.error("Não foi possível registrar o log de alteração do atributo "+campo.getName()+" da entidade "+entidadeAntiga.getClass());
                logger.error(e.getMessage());
            }
        }

        return camposAlterados;
    }

    private EntityManager getEntityManager() {
        return EntityManagerFactoryUtils.getTransactionalEntityManager(ClientsDatabaseConfig.getEntityManagerFactory());
    }

    private Integer getId(Object entidade) {
        try {
            Field idField = entidade.getClass().getDeclaredField("codigo");
            idField.setAccessible(true);
            return (Integer) idField.get(entidade);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            logger.error("O campo código não existe na entidade "+entidade.getClass());
            return 0;
        }
    }

}
