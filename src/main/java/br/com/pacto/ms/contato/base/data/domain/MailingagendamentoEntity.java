package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mailingagendamento", schema = "public")
public class MailingagendamentoEntity {
    private int codigo;
    private Integer ocorrencia;
    private String cron;
    private Timestamp ultimaexecucao;
    private Timestamp datainicial;
    private int maladireta;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo")
    public int getCodigo() {
        return codigo;
    }

    @Basic
    @Column(name = "ocorrencia")
    public Integer getOcorrencia() {
        return ocorrencia;
    }


    @Basic
    @Column(name = "cron")
    public String getCron() {
        return cron;
    }


    @Basic
    @Column(name = "ultimaexecucao")
    public Timestamp getUltimaexecucao() {
        return ultimaexecucao;
    }


    @Basic
    @Column(name = "datainicial")
    public Timestamp getDatainicial() {
        return datainicial;
    }


    @Column(name = "maladireta")
    public int getMaladireta() {
        return maladireta;
    }


}
