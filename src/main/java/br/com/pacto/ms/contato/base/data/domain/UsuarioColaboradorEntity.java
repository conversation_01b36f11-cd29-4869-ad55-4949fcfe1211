package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "usuario", schema = "public")
public class UsuarioColaboradorEntity {
    @Basic
    @Column(name = "colaborador")
    private Integer colaborador;

    @Basic
    @Column(name = "nome")
    private String nome;

    @Id
    @Column(name = "codigo")
    private int codigo;

}
