package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pactosolucoes.commons.util.annotation.LogTagTransformContract;

public class ConfiguracaoCrmDiasMetasTagTransform implements LogTagTransformContract<ConfiguracaoCrmDiasMetasEntity> {

    @Override
    public String transform(ConfiguracaoCrmDiasMetasEntity config) {
        if(config == null || config.getFase() == null){
            return null;
        }

        return config.getFase().getName();
    }
}
