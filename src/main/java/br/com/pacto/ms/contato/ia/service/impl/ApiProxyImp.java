package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pacto.ms.contato.ia.data.proxy.proxy.ApiProxy;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class ApiProxyImp implements ApiProxy {


    @Override
    public <T> T mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body, Class<T> responseType) {
        return mandarPost(urlCompleta, token, body, ParameterizedTypeReference.forType(responseType));
    }

    @Override
    public <T> T mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body, ParameterizedTypeReference<T> responseType) {
        HttpHeaders headers = new HttpHeaders();
        if (token != null) {
            if (!token.startsWith("Bearer ")) {
                token = "Bearer " + token;
            }
            headers.set("Authorization", token);
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        String uri = UriComponentsBuilder.fromHttpUrl(urlCompleta).toUriString();
        HttpEntity<Object> entidade = new HttpEntity<>(body, headers);
        RestTemplate restTemplate = new RestTemplate();

        try {
            ResponseEntity<T> response = restTemplate.exchange(
                    uri,
                    HttpMethod.POST,
                    entidade,
                    responseType
            );

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                return response.getBody();
            } else {
                throw new RuntimeException("Erro na requisição: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            throw new RuntimeException("Erro na requisição: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao processar a requisição POST", e);
        }
    }

    @Override
    public Object mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body) {
        return mandarPost(null, urlCompleta, token, body);
    }

    @Override
    public Object mandarPost(HttpHeaders headers, String urlCompleta, @Nullable String token, @Nullable Object body) {
        if (headers == null)
            headers = new HttpHeaders();
        if (token != null)
            headers.set("Authorization", "Bearer " + token);
        headers.set("Content-Type", "application/json");

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(urlCompleta);
        HttpEntity<?> entidade = new HttpEntity<>(body, headers);
        String uri = builder.toUriString();

        RestTemplate restTemplate = new RestTemplate();
        Object response = restTemplate.exchange(
                uri,
                HttpMethod.POST,
                entidade,
                Object.class);

        return response;

    }

    @Override
    public <T> T mandarGet(String urlCompleta, @Nullable String token, Class<T> responseType) {
        HttpHeaders headers = new HttpHeaders();
        if (token != null) {
            headers.set("Authorization", "Bearer " + token);
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        String uri = UriComponentsBuilder.fromHttpUrl(urlCompleta).toUriString();
        HttpEntity<Object> entidade = new HttpEntity<>(headers);
        RestTemplate restTemplate = new RestTemplate();
        try {
            ResponseEntity<T> response = restTemplate.exchange(
                    uri,
                    HttpMethod.GET,
                    entidade,
                    responseType
            );

            if (response.getStatusCode() == HttpStatus.OK) {
                return response.getBody();
            } else {
                throw new RuntimeException("Erro na requisição: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            throw new RuntimeException("Erro na requisição: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao processar a requisição GET", e);
        }
    }

    @Override
    public <T> T mandarPut(String urlCompleta, @Nullable String token, @Nullable Object body, Class<T> responseType) {
        HttpHeaders headers = new HttpHeaders();
        if (token != null) {
            if (!token.startsWith("Bearer ")) {
                token = "Bearer " + token;
            }
            headers.set("Authorization", token);
        }
        headers.setContentType(MediaType.APPLICATION_JSON);
        String uri = UriComponentsBuilder.fromHttpUrl(urlCompleta).toUriString();
        HttpEntity<Object> entidade = new HttpEntity<>(body, headers);
        RestTemplate restTemplate = new RestTemplate();
        try {
            ResponseEntity<T> response = restTemplate.exchange(
                    uri,
                    HttpMethod.PUT,
                    entidade,
                    responseType
            );

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                return response.getBody();
            } else {
                throw new RuntimeException("Erro na requisição: " + response.getStatusCode());
            }
        } catch (HttpClientErrorException | HttpServerErrorException e) {
            throw new RuntimeException("Erro na requisição: " + e.getStatusCode() + " - " + e.getResponseBodyAsString(), e);
        } catch (Exception e) {
            throw new RuntimeException("Erro ao processar a requisição PUT", e);
        }
    }

}
