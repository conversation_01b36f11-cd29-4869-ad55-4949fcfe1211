package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum TipoLeadEnum {
    RDSTATION(0, "RD Station"),
    BUZZLEAD(1, "<PERSON> Lead"),
    GENERICO(2, "Gen<PERSON>ric<PERSON>"),
    JOIN(3, "Join"),
    BITIRX24(5, "Bitrix24"),
    HUBSPOT(4, "Hubspot"),
    CONVERSAS_IA(6, "Conversas IA");

    private int id;
    private String descricao;
}
