package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;


import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoRedeIAEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface ConfiguracaoRedeIARepository extends PagingAndSortingRepository<ConfiguracaoRedeIAEntity, Integer> {

    List<ConfiguracaoRedeIAEntity> findAll();

}