package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.ModalidadeEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ModalidadeRepository  extends PagingAndSortingRepository<ModalidadeEntity, Integer> {
    Optional<List<ModalidadeEntity>> findAllByAtivoAndNomeContaining(boolean ativo, String nome);
    Optional<List<ModalidadeEntity>> findAllByAtivo(boolean ativo);
}
