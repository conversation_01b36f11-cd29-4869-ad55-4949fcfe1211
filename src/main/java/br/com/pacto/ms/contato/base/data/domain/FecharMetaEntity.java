package br.com.pacto.ms.contato.base.data.domain;

import java.sql.Timestamp;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.ForeignKey;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import br.com.pacto.ms.contato.avulso.data.domain.AberturametaEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import lombok.Data;

@Data
@Entity
@Table(name = "fecharmeta", indexes = {
        @Index(name = "fecharmeta_aberturameta_idx", columnList = "aberturameta"),
        @Index(name = "fecharmeta_identificadormeta", columnList = "identificadormeta"),
        @Index(name = "fecharmeta_maladireta_idx", columnList = "maladireta")
})
public class FecharMetaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @Column(name = "dataregistro")
    private Timestamp dataRegistro;

    @Column(name = "meta")
    private Double meta;

    @Column(name = "metaatingida")
    private Float metaAtingida;

    @Column(name = "porcentagem")
    private Double porcentagem;

    @Column(name = "justificativa", length = 200)
    private String justificativa;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "aberturameta", foreignKey = @ForeignKey(name = "fk_fecharmeta_aberturameta"))
    private AberturametaEntity aberturaMeta;

    @Column(name = "identificadormeta", length = 50)
    private String identificadorMeta;

    @Column(name = "repescagem")
    private Integer repescagem;

    @Column(name = "metacalculada")
    private Boolean metaCalculada;

    @Column(name = "nomemeta", length = 50)
    private String nomeMeta;

    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "maladireta", foreignKey = @ForeignKey(name = "fk_fecharmeta_maladireta"))
    private MaladiretaEntity malaDireta;

    public void calcularPorcentagem() {
        if (getMeta().intValue() == 0 || (getMetaAtingida()+getRepescagem() == 0)) {
            setPorcentagem(0.0);
        } else {
            setPorcentagem((((getMetaAtingida() + getRepescagem()) / getMeta()) * 100.0));
        }
    }



}
