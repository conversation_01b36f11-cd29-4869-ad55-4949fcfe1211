package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaosistemaCrmDiasPosVendasEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoSistemaCrmDiasPosVendasDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoSistemaCrmDiasPosVendasVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.TipoColaboradorVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaosistemaCrmDiasPosVendasRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmDiasPosVendasService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.exception.GenericException;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.CONFIGURACAO_POSVENDA_EXCLUSAO_NEGADA;

@Service
@AllArgsConstructor
public class ConfiguracaosistemCrmPosVendasServiceImpl<T, DTO> extends Object
    implements ConfiguracaoSistemaCrmDiasPosVendasService<T, DTO> {


    private ModelMapper mapper;
    private ConfiguracaosistemaCrmDiasPosVendasRepository repository;
    private ConfiguracaoSistemaCrmService configuracaosistemacrmService;

    @Override
    public List<ConfiguracaoSistemaCrmDiasPosVendasVO> consutlar() {
        List<ConfiguracaoSistemaCrmDiasPosVendasVO> configs = repository.findAllByOrderByCodigoAsc()
                .orElseThrow(DataNotFoundException::new)
                .stream()
                .map(c -> {
                    ConfiguracaoSistemaCrmDiasPosVendasVO vo = this.mapper.map(c, ConfiguracaoSistemaCrmDiasPosVendasVO.class);
                    vo.setTipoColaborador(this.mapper.map(TipoColaboradorEnum.getTipo(c.getSiglaResponsavelPeloContato()), TipoColaboradorVO.class));
                    return vo;
                })
                .collect(Collectors.toList());

        return configs;
    }

    @Override
    @ObjectMapper(ConfiguracaoSistemaCrmDiasPosVendasVO.class)
    public T incluir(ConfiguracaoSistemaCrmDiasPosVendasDTO dto) {
        ConfiguracaosistemaCrmDiasPosVendasEntity entity = mapper.map(dto, ConfiguracaosistemaCrmDiasPosVendasEntity.class);
        entity.setConfiguracaoSistemaCRM(configuracaosistemacrmService.consultarConfiguracaoAtual().getCodigo());
        return (T) this.repository.save(entity);
    }

    @Override
    public void excluir(Integer codigo) {

        if(this.repository.existeMetaConfiguracaoDiasPosVenda(codigo)){
            throw  new GenericException(new ExceptionMessageVO().message(CONFIGURACAO_POSVENDA_EXCLUSAO_NEGADA));
        }
        this.repository.deleteById(codigo);
    }

    @Override
    @ObjectMapper(TipoColaboradorVO.class)
    public T getTipoColaborador() {
        List<TipoColaboradorEnum> listaTipoColaborador = Arrays.asList(TipoColaboradorEnum.values());
        return (T) listaTipoColaborador;
    }

    @Override
    @ObjectMapper(ConfiguracaoSistemaCrmDiasPosVendasVO.class)
    public T alterar(Integer codigo, ConfiguracaoSistemaCrmDiasPosVendasDTO dto) {
        ConfiguracaosistemaCrmDiasPosVendasEntity configuracaosistemaCrmDiasPosVendasEntity = this.repository.findById(codigo)
                .orElseThrow(DataNotFoundException::new);

        this.mapper.map(dto, configuracaosistemaCrmDiasPosVendasEntity);
        return (T) this.repository.save(configuracaosistemaCrmDiasPosVendasEntity);
    }


}
