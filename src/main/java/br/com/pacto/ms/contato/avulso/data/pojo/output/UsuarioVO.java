package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Boolean administrador;

	private Timestamp dataalteracaosenha;

	private Date dataexibirmodalinativarusuers;

	private String linguagem;

	private String nome;

	private Boolean pedirsenhafuncionalidade;

	private Boolean permissaoalterarrps;

	private Boolean permitealterarpropriasenha;

	private Boolean permiteexecutarprocessos;

	private String pin;

	private String senha;

	private String servicesenha;

	private String serviceusuario;

	private Boolean showmodalinativar;

	private String tipousuario;

	private Date ultimoacesso;

	private String username;

	private List<AberturametaVO> aberturametas1;

	private List<AberturametaVO> aberturametas2;

	private List<AberturametaVO> aberturametas3;

	private List<AgendaVO> agendas1;

	private List<AgendaVO> agendas2;

	private List<AgendaVO> agendas3;

	private List<ClienteVO> clientes;

	private List<HistoricoContatoVO> historicocontatos;

	private List<MaladiretaVO> maladiretas;

	private List<PassivoVO> passivos1;

	private List<PassivoVO> passivos2;

	private ClienteVO clienteBean;

	private ColaboradorVO colaboradorBean;

}
