package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.ClienteEntity;
import br.com.pacto.ms.contato.avulso.data.domain.HistoricoContatoEntity;
import br.com.pacto.ms.contato.avulso.data.domain.SituacaoclientesinteticodwEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.avulso.data.repository.ClienteRepository;
import br.com.pacto.ms.contato.avulso.data.repository.HistoricoContatoRepository;
import br.com.pacto.ms.contato.avulso.data.repository.SituacaoclientesinteticodwRepository;
import br.com.pacto.ms.contato.base.data.repository.MalaDiretaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.MensagemEnviadaEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.MensagemEnviadaEntityRepository;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoContatoCRM;
import br.com.pacto.ms.contato.ia.data.pojo.input.HistoricoContatoAIDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.IndicadorDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
import br.com.pacto.ms.contato.ia.service.contract.ContextoService;
import br.com.pacto.ms.contato.ia.service.contract.ConversaService;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.exception.DataValidateException;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;

@Service
public class ConversaServiceImpl implements ConversaService {

    @Autowired
    private SituacaoclientesinteticodwRepository situacaoclientesinteticodwRepository;
    @Autowired
    private PactoConversasIAProxy pactoConversasIAProxy;
    @Autowired
    private ContextoService contextoService;
    @Autowired
    private RequestService requestService;
    @Autowired
    private HistoricoContatoRepository historicoContatoRepository;
    @Autowired
    private ClienteRepository clienteRepository;
    @Autowired
    private ModelMapper mapper;
    @Autowired
    private MensagemEnviadaEntityRepository mensagemEnviadaEntityRepository;
    @Autowired
    private MalaDiretaRepository malaDiretaRepository;
    @Autowired
    private PactoConversasUrlResolverService urlResolverService;

    private static final Logger log = LoggerFactory.getLogger(ConversaServiceImpl.class);

    @Override
    public ResponsePactoConversasVO<ContextosAlunoVO> inciarConversa(Integer cliente, ResumoEmpresaVO resumoEmpresaVO, String nomeFase, String nomeMetaExtra, Integer codigoMetaExtra) {
        Optional<SituacaoclientesinteticodwEntity> situacaoclientesinteticodwEntityOptional = this.situacaoclientesinteticodwRepository.findByCodigocliente(cliente);

        if (situacaoclientesinteticodwEntityOptional.isPresent()) {
            SituacaoclientesinteticodwEntity situacaoclientesinteticodwEntity = situacaoclientesinteticodwEntityOptional.get();
            log.info("Sintetico do cliente com matricula " + situacaoclientesinteticodwEntity.getMatricula() + " foi encontrado");
            ContextosAlunoVO contextosAlunoVO = this.contextoService.consultarContextosAluno(situacaoclientesinteticodwEntity.getEmpresacliente(), cliente, true, codigoMetaExtra);
            String identificadorEmpresa = this.contextoService.identificadorEmpresa(situacaoclientesinteticodwEntity.getEmpresacliente());
            log.info("Telefone de comunicação do cliente com matricula {} é {}",
                    situacaoclientesinteticodwEntity != null ? situacaoclientesinteticodwEntity.getMatricula() : "N/A",
                    contextosAlunoVO != null &&
                            contextosAlunoVO.getAluno() != null &&
                            contextosAlunoVO.getAluno().getPessoa() != null &&
                            contextosAlunoVO.getAluno().getPessoa().getTelefonesconsulta() != null
                            ? contextosAlunoVO.getAluno().getPessoa().getTelefonesconsulta() : "N/A");
            ResponseEnviarMensagemVO responseEnviarMensagemVO = this.pactoConversasIAProxy.enviarMensagem(URI.create(urlResolverService.getPactoConversasUrl()), identificadorEmpresa, contextosAlunoVO);
            log.info("Resposta do Conversas.ai ao envio mensagem para o cliente de matricula " + situacaoclientesinteticodwEntity.getMatricula() + " é " + responseEnviarMensagemVO.toString());
            if (responseEnviarMensagemVO.getError() == null) {
                log.info("Mensagem enviada para o cliente com matricula " + situacaoclientesinteticodwEntity.getMatricula());
                Optional<MensagemEnviadaEntity> mensagemEnviadaEntity = mensagemEnviadaEntityRepository.buscarPorEmpresaOuChaveNoDiaDeHoje(resumoEmpresaVO.getCodigo(), Date.valueOf(LocalDate.now()), nomeFase, codigoMetaExtra);
                if (nomeFase != null || nomeMetaExtra != null) {
                    if (mensagemEnviadaEntity.isPresent()) {
                        List<Integer> codigosClientes = mensagemEnviadaEntity.get().getCodigosCliente();
                        codigosClientes.add(cliente);
                        mensagemEnviadaEntity.get().setCodigoEmpresa(resumoEmpresaVO.getCodigo());
                        mensagemEnviadaEntity.get().setNomeFase(nomeFase != null ? nomeFase : nomeMetaExtra);
                        mensagemEnviadaEntity.get().setCodigosCliente(codigosClientes);
                        mensagemEnviadaEntity.get().setCodigoMetaExtra(codigoMetaExtra);
                        this.mensagemEnviadaEntityRepository.save(mensagemEnviadaEntity.get());

                    } else {
                        List<Integer> codigosClientes = new ArrayList<>();
                        codigosClientes.add(cliente);
                        this.mensagemEnviadaEntityRepository.save(MensagemEnviadaEntity.builder()
                                .codigoEmpresa(resumoEmpresaVO.getCodigo())
                                .nomeFase(nomeFase)
                                .dataEnvio(Date.valueOf(LocalDate.now()))
                                .codigosCliente(codigosClientes)
                                .codigoMetaExtra(codigoMetaExtra)
                                .nomeMetaExtra(nomeMetaExtra)
                                .build());
                    }
                }
            }else{
                log.error("Erro ao enviar mensagem para o cliente com matricula " + situacaoclientesinteticodwEntity.getMatricula() + ". Erro: " + responseEnviarMensagemVO.getError());
            }
            return new ResponsePactoConversasVO<>(responseEnviarMensagemVO.getError() != null ? responseEnviarMensagemVO.getError() : responseEnviarMensagemVO.getSuccess(), contextosAlunoVO);
        }

        throw new DataNotFoundException("Cliente com código " + cliente + " não encontrado");
    }

    public ResponsePactoConversasVO<ContextosAlunoVO> inciarPosVendaConversaPorCpf(String cpf) {
        return inciarPosVendaConversaPorCpf(null, cpf);
    }

    @Override
    public ResponsePactoConversasVO<ContextosAlunoVO> inciarPosVendaConversaPorCpf(String cpf, String linkAppTreino) {
        Optional<SituacaoclientesinteticodwEntity> situacaoclientesinteticodwEntityOptional = this.situacaoclientesinteticodwRepository.consultarPorCpf(cpf);
        if (situacaoclientesinteticodwEntityOptional.isPresent()) {
            SituacaoclientesinteticodwEntity situacaoclientesinteticodwEntity = situacaoclientesinteticodwEntityOptional.get();
            ContextosAlunoVO contextosAlunoVO = this.contextoService.consultarContextosAluno(situacaoclientesinteticodwEntity.getEmpresacliente(), situacaoclientesinteticodwEntity.getCodigocliente(), false);

            if(this.requestService.getCurrentConfiguration().getCompanyId() == null){
                this.requestService.getCurrentConfiguration().setCompanyId(situacaoclientesinteticodwEntity.getEmpresacliente());
            }

            contextosAlunoVO.getAluno().setFase_crm(FasesCRMEnum.POS_VENDA.getName());
            contextosAlunoVO.getAluno().setLinkAppTreino(linkAppTreino);

            String identificadorEmpresa = this.contextoService.identificadorEmpresa(situacaoclientesinteticodwEntity.getEmpresacliente());
            try {
                ResponseEnviarMensagemVO mensagemVOResponsePactoConversasVO = this.pactoConversasIAProxy.enviarMensagem(URI.create(urlResolverService.getPactoConversasUrl()), identificadorEmpresa, contextosAlunoVO);

                return new ResponsePactoConversasVO<>(mensagemVOResponsePactoConversasVO.getError() != null ? mensagemVOResponsePactoConversasVO.getError() : mensagemVOResponsePactoConversasVO.getSuccess(), contextosAlunoVO);
            } catch (Exception e) {
                e.printStackTrace();
                try {
                    ObjectMapper objectMapper = new ObjectMapper();
                    String json = objectMapper.writeValueAsString(contextosAlunoVO);
                    System.out.println("Erro ao enviar mensagem. ContextosAlunoVO: " + json);
                } catch (JsonProcessingException jsonException) {
                    jsonException.printStackTrace();
                }
            }
        }

        throw new DataNotFoundException("Não foi possivel gerar o contexto e enviar a mensagem do aluno com CPF " + cpf);
    }

    @Override
    public ResponsePactoConversasVO<ResponseStatusVO> receberIndicadoresBI(IndicadorDTO dto) {

        URI uri = URI.create(urlResolverService.getPactoConversasUrl());
        String identificadorEmpresa = dto.getChave() + "-" + dto.getEmpresa();

        ResponseStatusVO responseStatusVO = pactoConversasIAProxy.indicadoresBI(uri, identificadorEmpresa, dto);
        return ResponsePactoConversasVO.ok(responseStatusVO);
    }


    @Override
    public HistoricoContatoAIVO salvarConversa(HistoricoContatoAIDTO dto) {

        if (!validarFase(dto.getFase()))
            throw new DataValidateException("A fase " + dto.getFase() + " é inválida ." + "Fases válidas: " + Arrays.toString(FasesCRMEnum.values()));
        if (!validaTipoContato(dto.getTipoContato()))
            throw new DataValidateException("O contato " + dto.getTipoContato() + " é inválida ." + "Tipo Contato válidos: " + Arrays.toString(TipoContatoCRM.values()));

        Optional<ClienteEntity> cliente = clienteRepository.consultarPorCodigo(dto.getCliente());
        if (!cliente.isPresent()) {
            throw new DataNotFoundException("Cliente com código " + dto.getCliente() + " não encontrado");
        }

        Optional<List<HistoricoContatoEntity>> historicoContatoEntity = historicoContatoRepository.buscarPorConversa(dto.getCliente(), "Conversa AI", dto.getData());
        if (!historicoContatoEntity.isPresent() || historicoContatoEntity.get().isEmpty()) {
            HistoricoContatoEntity historicoContato = new HistoricoContatoEntity();
            historicoContato.setCliente(dto.getCliente());
            historicoContato.setDia(dto.getData());
            historicoContato.setFase(dto.getFase());
            historicoContato.setResultado("Conversa AI");
            historicoContato.setTipocontato(dto.getTipoContato());
            historicoContato.setContatoavulso(false);
            historicoContato.setObservacao(dto.getMessage());
            historicoContato.setResponsavelcadastro(requestService.getCurrentConfiguration().getZwId());
            historicoContatoRepository.save(historicoContato);
            return this.mapper.map(historicoContato, HistoricoContatoAIVO.class);

        }
        String concatencao;
        String observacao = historicoContatoEntity.get().get(0).getObservacao();
        concatencao = observacao + "\n" + dto.getMessage();
        historicoContatoEntity.get().get(0).setObservacao(concatencao);
        historicoContatoEntity.get().get(0).setDia(dto.getData());
        historicoContatoRepository.save(historicoContatoEntity.get().get(0));
        return this.mapper.map(historicoContatoEntity.get().get(0), HistoricoContatoAIVO.class);
    }

    private boolean validarFase(String fase) {
        try {
            FasesCRMEnum.valueOf(fase.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    private boolean validaTipoContato(String sigla) {
        if (sigla == null || sigla.isEmpty()) {
            return false;
        }
        for (TipoContatoCRM tipo : TipoContatoCRM.values()) {
            if (tipo.getSigla().equals(sigla)) {
                return true;
            }
        }
        return false;
    }

}
