package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.AgendaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface AgendaRepository extends PagingAndSortingRepository<AgendaEntity, Integer> {

        @Query("select a from AgendaEntity a " +
                " where a.dataagendamento = :dataAgendamento " +
                " and  ( a.hora =:hora and a.minuto =:minuto ) " +
                " and ( a.cliente =:cliente OR a.cliente is null)  "+
                " and ( a.passivo =:passivo OR a.passivo is null) "+
                " and ( a.indicado =:indicado OR a.indicado is null) ")
        Optional<AgendaEntity> buscarAgendamentoPorDataCliente(Date dataAgendamento, Integer cliente, Integer passivo,
                                                                     Integer indicado, String hora, String minuto);

}
