package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricoContatoAIVO {

    @Schema(description = "Data do contato", example = "2024-09-10T14:30:00.000+00:00")
    private Timestamp dia;

    @Schema(description = "Código do cliente", example = "617")
    private Integer cliente;

    @Schema(description = "Observação da interação com a IA", example = "IA: SOU GEPETO COMO POSSO AJUDAR?\\nUSER: quero fazer academia posso contratar?\\nIA: Prazer em conhecê-lo, Gabriel! 😊 Estive olhando suas informações e percebi que você visitou nossa academia há cerca de 24 horas. Fiquei feliz com sua visita, mas também estou um pouco triste por você não ter tomado a decisão de investir na sua qualidade de vida. Estamos oferecendo uma aula experimental gratuita, onde você poderá conhecer melhor nossos serviços. Também temos um cupom de desconto de 50% na primeira mensalidade! 🎉O que você acha? Gostaria de agendar a aula ou já tem interesse em contratar um plano?\\nUSER: quero contratar um plano cara\\nAI: Oi, tudo bem? Como posso ajudar?")
    private String observacao;

    @Schema(description = "Resultado da interação", example = "Conversa AI")
    private String resultado;

    @Schema(description = "Fase da interação", example = "AGENDAMENTO")
    private String fase;

    @Schema(description = "Tipo de contato", example = "WA")
    private String tipocontato;

    @Schema(description = "Indica se o contato é avulso", example = "false")
    private Boolean contatoavulso;

}
