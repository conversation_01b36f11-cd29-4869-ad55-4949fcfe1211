package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ProativoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ScheduleNotificationsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoIAProativosService;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
import br.com.pacto.ms.contato.ia.service.impl.PactoConversasUrlResolverService;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConfiguracaoCrmIAProativosServiceImpl implements ConfiguracaoIAProativosService {

    private final static String Type = "book_class";

    private final PactoConversasIAProxy pactoConversasIAProxy;
    private final RequestService requestService;
    private final ConfiguracaoCrmIARepository configuracaoCrmIARepository;
    private final PactoConversasUrlResolverService urlResolverService;

    public String identificadorEmpresa(Integer empresa) {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + empresa;
    }

    public ResponsePactoConversasVO<String> enviarNotificacaco(ProativoDTO dto) {
        String url = urlResolverService.getPactoConversasUrl();

        Map<String, String> parametros = new HashMap<>();
        parametros.put("scheduler_text", dto.getDescricaoProativo());
        String responseEnviarMensagemVO = this.pactoConversasIAProxy
                .enviarNotificationScheme(
                        URI.create(url),
                        identificadorEmpresa(dto.getCodigoEmpresa()),
                        Type,
                        parametros);

        Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities = configuracaoCrmIARepository
                .obterPorCodigoEmpresa(dto.getCodigoEmpresa());
        configuracaoCrmIAEntities.ifPresent(
                configList -> {
                    configList.stream().findFirst().ifPresent(
                            config -> {
                                config.setDescricaoNotificacaoProativo(dto.getDescricaoProativo());
                                configuracaoCrmIARepository.save(config);
                            });
                });

        ResponsePactoConversasVO<String> responsePactoConversasVO = new ResponsePactoConversasVO<>();
        responsePactoConversasVO.setContexto(responseEnviarMensagemVO);
        return responsePactoConversasVO;
    }

    /**
     * Obtém as notificações agendadas para uma empresa.
     *
     * @param codigoEmpresa Código da empresa.
     * @return ResponsePactoConversasVO com o contexto preenchido ou vazio em caso de erro controlado.
     */
    @Override
    @ExceptionHandler(ConfiguracaoIAException.class)
    public ResponsePactoConversasVO<ScheduleNotificationsDTO> obterNotificacoes(Integer codigoEmpresa) {
        try {
            String url = urlResolverService.getPactoConversasUrl();
            ScheduleNotificationsDTO notificationsDTO = obterNotificacoesDaApi(url, codigoEmpresa, Type);

            ResponsePactoConversasVO<ScheduleNotificationsDTO> response = new ResponsePactoConversasVO<>();
            response.setContexto(notificationsDTO != null ? notificationsDTO : new ScheduleNotificationsDTO());
            return response;

        } catch (Exception e) {
            log.error("Erro inesperado ao obter notificações: {}", e.getMessage(), e);
            return new ResponsePactoConversasVO<>();
        }
    }

    private ScheduleNotificationsDTO obterNotificacoesDaApi(String url, int codigoEmpresa, String type) {
        return this.pactoConversasIAProxy.obterNotificacoes(
                URI.create(url),
                identificadorEmpresa(codigoEmpresa),
                type
        );
    }
}
