package br.com.pacto.ms.contato.base.service.contract;

public interface TelefoneService<T> {
    T buscarTelefonePorTipo(int matricula, String tipoContato);

    T buscarTelefoneFixo(int matricula);

    T buscarPorClienteTipoAndRecebersms(int cliente, String tipoContato);

    T buscarPorClienteAndTipoTelefone(int cliente, String tipoContato);

    public void persistirTelefonesClienteLead(Integer codigoPessoa, String celular, String teleFoneResidencial, String teleFoneTrabalho);
}
