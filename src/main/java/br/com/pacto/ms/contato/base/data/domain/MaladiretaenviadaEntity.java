package br.com.pacto.ms.contato.base.data.domain;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "maladiretaenviada", schema = "public")
public class MaladiretaenviadaEntity {
    private int maladireta;
    private Integer cliente;
    private Integer passivo;
    private Integer indicado;
    private int codigo;

    @Basic
    @Column(name = "maladireta")
    public int getMaladireta() {
        return maladireta;
    }


    @Basic
    @Column(name = "cliente")
    public Integer getCliente() {
        return cliente;
    }


    @Basic
    @Column(name = "passivo")
    public Integer getPassivo() {
        return passivo;
    }


    @Basic
    @Column(name = "indicado")
    public Integer getIndicado() {
        return indicado;
    }

    public void setIndicado(Integer indicado) {
        this.indicado = indicado;
    }

    @GeneratedValue(generator = "notificacaoenviada_codigo_seq")
    @Id
    @Column(name = "codigo")
    public int getCodigo() {
        return codigo;
    }

}
