package br.com.pacto.ms.contato.avulso.data.domain;

import com.fasterxml.jackson.annotation.JsonAlias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class HistoricoContatoResumo {

    private String id;
    private Long total;
    private String descricao;

    public HistoricoContatoResumo(Long total, String id) {
        this.setId(id);
        this.setTotal(total);
    }

    public HistoricoContatoResumo(Long total, String id, String descricao) {
        this.setId(id);
        this.setTotal(total);
        this.setDescricao(descricao);
    }
}