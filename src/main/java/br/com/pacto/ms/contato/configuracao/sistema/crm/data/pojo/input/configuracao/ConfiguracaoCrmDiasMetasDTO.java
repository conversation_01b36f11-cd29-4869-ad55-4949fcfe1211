package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ConfiguracaoCrmDiasMetasDTO {


    @Schema(description = "Número de dias", example = "1")
    private Integer nrdias;
    @Schema(description = "Descrição da configuração", example = "Isenção da Matrícula e 1ª parcela")
    private String descricao;
    @Schema(description = "Fase da configuração", example = "EX_ALUNOS")
    private FasesCRMEnum fase;
    private ProdutoDTO produto;
}
