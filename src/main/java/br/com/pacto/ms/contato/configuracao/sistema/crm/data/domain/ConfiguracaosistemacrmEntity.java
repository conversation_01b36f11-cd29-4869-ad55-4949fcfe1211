package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.avulso.data.domain.UsuarioEntity;
import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogFieldValueTransform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaosistemacrm", schema = "public")
@EntityListeners(LogListener.class)
public class ConfiguracaosistemacrmEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "remetentepadrao")
    private String remetentepadrao;
    @Transient
    private UsuarioEntity remetentePadraoVO;

    @Basic
    @Column(name = "emailpadrao")
    private String emailpadrao;
    @Basic
    @Column(name = "mailserver")
    private String mailserver;
    @Basic
    @Column(name = "login")
    private String login;
    @Basic
    @Column(name = "senha")
    private String senha;
    @Basic
    @Column(name = "abertosabado")
    private Boolean abertosabado;
    @Basic
    @Column(name = "abertodomingo")
    private Boolean abertodomingo;
    @Basic
    @Column(name = "nrfaltaplanomensal")
    private Integer nrfaltaplanomensal;
    @Basic
    @Column(name = "nrfaltaplanotrimestral")
    private Integer nrfaltaplanotrimestral;
    @Basic
    @Column(name = "nrfaltaplanoacimasemestral")
    private Integer nrfaltaplanoacimasemestral;
    @Basic
    @Column(name = "nrdiasparaclientepreverenovacao")
    private Integer nrdiasparaclientepreverenovacao;
    @Basic
    @Column(name = "nrdiasparaclientepreveperda")
    private Integer nrdiasparaclientepreveperda;
    @Basic
    @Column(name = "nrrisco")
    private Integer nrrisco;
    @Basic
    @Column(name = "conexaosegura")
    private Boolean conexaosegura;
    @Basic
    @Column(name = "dividirfase")
    private Boolean dividirfase;
    @Basic
    @Column(name = "nrdiasanterioresagendamento")
    private Integer nrdiasanterioresagendamento;
    @Basic
    @Column(name = "nrdiasposterioresagendamento")
    private Integer nrdiasposterioresagendamento;
    @Basic
    @Column(name = "nrdiaslimiteagendamentofuturo")
    private Integer nrdiaslimiteagendamentofuturo;
    @Basic
    @Column(name = "bloqueartermospam")
    private Boolean bloqueartermospam;
    @Basic
    @Column(name = "urljenkins", length = -1)
    private String urljenkins;
    @Basic
    @Column(name = "urlmailing", length = -1)
    private String urlmailing;
    @Basic
    @Column(name = "iniciartls")
    private Boolean iniciartls;
    @Basic
    @Column(name = "qtdindicacoesmes")
    private Integer qtdindicacoesmes;
    @Basic
    @Column(name = "qtdconversoesvendasmes")
    private Integer qtdconversoesvendasmes;
    @Basic
    @Column(name = "nrdiasposagendamentoconversaoexaluno")
    private Integer nrdiasposagendamentoconversaoexaluno;
    @Basic
    @Column(name = "qtdconversoesexalunosmes")
    private Integer qtdconversoesexalunosmes;
    @Basic
    @Column(name = "incluircontratosrenovados")
    private Boolean incluircontratosrenovados;
    @Basic
    @Column(name = "considerarprofessortreinoweb")
    private Boolean considerarprofessortreinoweb;
    @Basic
    @Column(name = "batermetatodasacoes")
    private Boolean batermetatodasacoes;
    @Basic
    @Column(name = "nrdiascontarresultado")
    private Short nrdiascontarresultado;
    @Basic
    @Column(name = "portaserver", length = 10)
    private String portaserver;
    @Basic
    @Column(name = "obrigatorioseguirordemmetas")
    private Boolean obrigatorioseguirordemmetas;
    @Basic
    @Column(name = "ordenacaometas", length = 120)
    @LogFieldValueTransform(OrdenacaoMetasValueTransform.class)
    private String ordenacaometas;
    @Basic
    @Column(name = "enviaremailindividualmente")
    private Boolean enviaremailindividualmente;
    @Basic
    @LogFieldValueTransform(UsuarioValueTransform.class)
    private Integer remetentepadraomailing;
    @OneToOne
    @NotFound(action = NotFoundAction.IGNORE)
    @JoinColumn(name = "remetentepadraomailing", referencedColumnName = "codigo", insertable = false, updatable = false)
    private UsuarioEntity remetentePadraoMailingRelacionado;
    @Basic
    @Column(name = "nrcreditostreinorenovar")
    private Integer nrcreditostreinorenovar;
    @Basic
    @Column(name = "agendamentoparametaconsultor")
    private Boolean agendamentoparametaconsultor;
    @Basic
    @Column(name = "apresentarcolaboradoresportipocolaborador")
    private Boolean apresentarcolaboradoresportipocolaborador;
    @Basic
    @Column(name = "apresentarcolaboradoresinativos")
    private Boolean apresentarcolaboradoresinativos;
    @Basic
    @Column(name = "nrdiasparaclientepreverenovacaomaiorummes")
    private Integer nrdiasparaclientepreverenovacaomaiorummes;
    @Basic
    @Column(name = "usasmtps")
    private Boolean usasmtps;
    @Basic
    @Column(name = "mailingftpserver")
    private String mailingftpserver;
    @Basic
    @Column(name = "mailingftpuser")
    private String mailingftpuser;
    @Basic
    @Column(name = "mailingftppass")
    private String mailingftppass;
    @Basic
    @Column(name = "mailingftpport")
    private Integer mailingftpport;
    @Basic
    @Column(name = "mailingftptype")
    private String mailingftptype;
    @Basic
    @Column(name = "mailingftpfolder")
    private String mailingftpfolder;
    @Basic
    @Column(name = "tokenbitly")
    private String tokenbitly;
    @Basic
    @Column(name = "autorrenovavelentrarenovacao")
    private Boolean autorrenovavelentrarenovacao;
    @Basic
    @Column(name = "integracaosendyativa")
    private Boolean integracaosendyativa;
    @Basic
    @Column(name = "integracaopacto")
    private Boolean integracaopacto;
    @Basic
    @Column(name = "limitediarioemails")
    private Integer limitediarioemails;
    @Basic
    @Column(name = "limitemensalpacto")
    private Integer limitemensalpacto;
    @Basic
    @Column(name = "usarremetentepadraogeral")
    private Boolean usarremetentepadraogeral;
    @Basic
    @Column(name = "gerarindicacaoparacadastroconvidadosvendasonline")
    private Boolean gerarindicacaoparacadastroconvidadosvendasonline;
    @Basic
    @Column(name = "direcionaragendamentosexperimentaisagenda")
    private Boolean direcionaragendamentosexperimentaisagenda;
    @Basic
    @Column(name = "usaConfiguracaoEmailManual")
    private Boolean usaConfiguracaoEmailManual;
}
