package br.com.pacto.ms.contato.health.service.impl;

import br.com.pacto.ms.contato.base.data.repository.TelefoneRepository;
import br.com.pacto.ms.contato.health.data.pojo.output.HealthCheckConnectionVO;
import br.com.pacto.ms.contato.health.enums.HealthCheckStatusEnum;
import br.com.pacto.ms.contato.health.service.contract.HealthService;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;

@AllArgsConstructor
@NoArgsConstructor
public @Service  class HealthServiceImpl<T> implements HealthService {

    @Value("${version}")
    private String version;
    @Autowired
    private RequestService requestService;
    @Autowired
    private TelefoneRepository repository;

    public HealthServiceImpl(RequestService requestService) {
        this.requestService = requestService;
    }

    @Override
    public HealthCheckConnectionVO health(String keys) {
        HealthCheckConnectionVO healthCheckConnectionVO = new HealthCheckConnectionVO();
        Arrays.stream(keys.split(",")).forEach(key -> {
            requestService.getCurrentConfiguration().setCompanyKey(key);
            healthCheckConnectionVO.getConnectionStatus().put(key, healthCheckConnection());
        });

        healthCheckConnectionVO.setVersion(version);
        healthCheckConnectionVO.setStatus(healthCheckConnectionVO.getConnectionStatus()
                .entrySet()
                .stream()
                .filter(connectionStatus -> connectionStatus.getValue().equals(true))
                .findFirst().isPresent() ? HealthCheckStatusEnum.OK : HealthCheckStatusEnum.FAILED);
        return healthCheckConnectionVO;
    }

    private boolean healthCheckConnection(){
        return repository.healthCheckConnection();
    }
}
