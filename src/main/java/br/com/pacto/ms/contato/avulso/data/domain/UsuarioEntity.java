package br.com.pacto.ms.contato.avulso.data.domain;

import java.sql.Date;
import java.sql.Timestamp;

import javax.persistence.*;

import br.com.pacto.ms.contato.base.data.domain.ColaboradorEntity;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "usuario", schema = "public")
public class UsuarioEntity {
    @Basic
    @Column(name = "administrador", nullable = true)
    private Boolean administrador;

    @ManyToOne
    @JoinColumn(name = "cliente", referencedColumnName = "codigo")
    private ClienteEntity cliente;

    @ManyToOne
    @JoinColumn(name = "colaborador", referencedColumnName = "codigo")
    private ColaboradorEntity colaborador;

    @Basic
    @Column(name = "tipousuario", nullable = true, length = 2)
    private String tipousuario;
    @Basic
    @Column(name = "senha", nullable = false, length = 64)
    private String senha;
    @Basic
    @Column(name = "username", nullable = false, length = 255)
    private String username;
    @Basic
    @Column(name = "nome", nullable = false, length = 100)
    private String nome;
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "dataalteracaosenha", nullable = true)
    private Timestamp dataalteracaosenha;
    @Basic
    @Column(name = "permitealterarpropriasenha", nullable = true)
    private Boolean permitealterarpropriasenha;
    @Basic
    @Column(name = "serviceusuario", nullable = true, length = 100)
    private String serviceusuario;
    @Basic
    @Column(name = "servicesenha", nullable = true, length = 100)
    private String servicesenha;
    @Basic
    @Column(name = "pedirsenhafuncionalidade", nullable = true)
    private Boolean pedirsenhafuncionalidade;
    @Basic
    @Column(name = "permissaoalterarrps", nullable = true)
    private Boolean permissaoalterarrps;
    @Basic
    @Column(name = "permiteexecutarprocessos", nullable = true)
    private Boolean permiteexecutarprocessos;
    @Basic
    @Column(name = "linguagem", nullable = true, length = 10)
    private String linguagem;
    @Basic
    @Column(name = "showmodalinativar", nullable = true)
    private Boolean showmodalinativar;
    @Basic
    @Column(name = "dataexibirmodalinativarusuers", nullable = true)
    private Date dataexibirmodalinativarusuers;
    @Basic
    @Column(name = "ultimoacesso", nullable = true)
    private Date ultimoacesso;
    @Basic
    @Column(name = "pin", nullable = true, length = 100)
    private String pin;

}
