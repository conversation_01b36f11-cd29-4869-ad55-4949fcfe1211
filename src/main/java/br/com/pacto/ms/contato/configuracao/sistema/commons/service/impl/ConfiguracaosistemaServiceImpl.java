package br.com.pacto.ms.contato.configuracao.sistema.commons.service.impl;


import java.util.Date;

import org.springframework.stereotype.Service;

import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.pojo.input.EmpresaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.commons.data.repository.ConfiguracaosistemaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.contract.ConfiguracaosistemaService;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.MetaFechadaException;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.service.contract.FeriadoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class ConfiguracaosistemaServiceImpl<T> implements ConfiguracaosistemaService<T> {

    private ConfiguracaosistemaRepository repository;
    private FeriadoService feriadoService;
    @Override
    @LogExecution
    public void verificarCadastroContatoMeta() {

        feriadoService.validarFeriadoPorEmpresaParaCalculoAberturaMeta(EmpresaDTO.builder().codigo(1).build(), new Date(114, 03,18));
        repository.findByValidarcontatometaIsTrue().orElseThrow(MetaFechadaException::new);
    }
}
