package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.VinculoTiposColaboradorPorFaseDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.VinculoFaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.VinculoTiposColaboradorPorFaseVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;

import javax.validation.Valid;
import java.util.List;

public interface TiposVinculosFaseService<T, DTO> {
    List<VinculoFaseVO> consutlar();

    List<VinculoTiposColaboradorPorFaseVO> consultarPorFases(List<FasesCRMEnum> fasesCRMEnums);

    List<VinculoTiposColaboradorPorFaseVO> consultarPorFases();

    VinculoTiposColaboradorPorFaseVO consultarPorFase(FasesCRMEnum fase);

    VinculoTiposColaboradorPorFaseVO alterarTodos(FasesCRMEnum fase, List<TipoColaboradorEnum> tiposColaboradores);

    List<VinculoTiposColaboradorPorFaseVO> alterarTodos(@Valid List<VinculoTiposColaboradorPorFaseDTO> vinculoTiposColaboradorPorFase);

    List<VinculoTiposColaboradorPorFaseVO> consutlarPadrao();
}
