package br.com.pacto.ms.contato.health.web.controller.v1;

import br.com.pacto.ms.contato.health.data.pojo.output.HealthCheckVO;
import br.com.pacto.ms.contato.health.enums.HealthCheckStatusEnum;
import br.com.pacto.ms.contato.health.service.contract.HealthService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@Tag(name = "Health", description = "Operações de consulta de saúde da api")
@RequestMapping("/v1/health")
public class HealthController extends BaseController {

    @Value("${version}")
    private String version;

    @Autowired
    private RequestService requestService;
    @Autowired
    private HealthService healthService;

    @Operation(summary = "Consultar saúde da api")
    @LogExecution
    @GetMapping
    public ResponseEntity<?> health() {
        return super.finish(new HealthCheckVO(version, HealthCheckStatusEnum.OK));
    }

    @Operation(summary = "Consultar saúde de conexões com banco de empresas")
    @LogExecution
    @GetMapping(path = "{keys}")
    public ResponseEntity<?> healthKeys(@Parameter(description = "Chaves de empresas que serão testadas as conexões, separado por virgula",
            example = "810f691394b9cf79d33459a08861f3ed,teste") @PathVariable String keys) {
        return super.finish(healthService.health(keys));
    }

}
