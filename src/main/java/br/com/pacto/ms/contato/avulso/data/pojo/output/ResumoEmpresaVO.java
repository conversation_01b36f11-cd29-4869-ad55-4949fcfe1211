package br.com.pacto.ms.contato.avulso.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResumoEmpresaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;
	private String nome;

}
