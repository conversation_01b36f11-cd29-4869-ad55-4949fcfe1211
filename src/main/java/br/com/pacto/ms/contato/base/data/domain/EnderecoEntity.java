package br.com.pacto.ms.contato.base.data.domain;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


@Data
@Entity
@Table(name = "endereco", schema = "public")
public class EnderecoEntity {

    @Id
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "tipoendereco")
    private String tipoendereco;

    @Column(name = "cep")
    private String cep;

    @Column(name = "bairro")
    private String bairro;

    @Column(name = "numero")
    private String numero;

    @Column(name = "complemento")
    private String complemento;

    @Column(name = "endereco")
    private String endereco;

    @Column(name = "enderecocorrespondencia")
    private Boolean enderecocorrespondencia;
}
