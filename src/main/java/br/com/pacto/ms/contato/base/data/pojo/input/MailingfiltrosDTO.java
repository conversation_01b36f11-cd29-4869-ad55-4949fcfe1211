package br.com.pacto.ms.contato.base.data.pojo.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailingfiltrosDTO {
    private int codigo;
    private Integer categoria;
    private String situacao;
    private Integer vinculocolaborador;
    private Integer modalidade;
    private Integer duracao;
    private Integer evento;
    private String codigoscategoria;
    private String codigosmodalidades;
    private String listasituacoes;
    private String codigosconsultores;
    private String codigosprofessores;
    private String codigosplanos;
    private String codigocontratoduracao;
    private Timestamp datacadastromin;
    private Timestamp datacadastromax;
    private Integer idademin;
    private Integer idademax;
    private Boolean feminino;
    private Boolean masculino;
}
