package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


@Data
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class ConfiguracaoCrmDiasMetasVO {

    @Schema(description = "Código da configuração", example = "1")
    private Integer codigo;
    @Schema(description = "Número de dias", example = "1")
    private Integer nrdias;
    @Schema(description = "Descrição da configuração", example = "Isenção da Matrícula e 1ª parcela")
    private String descricao;
    @Schema(description = "Fase da configuração", example = "EX_ALUNOS")
    private FasesCRMEnum fase;
    private FaseVO faseVO;
    private ProdutoVO produto;
}
