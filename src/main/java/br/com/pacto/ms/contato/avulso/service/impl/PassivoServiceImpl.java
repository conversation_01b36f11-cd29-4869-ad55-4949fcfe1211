package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.PassivoEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.output.PassivoVO;
import br.com.pacto.ms.contato.avulso.data.repository.PassivoRepository;
import br.com.pacto.ms.contato.avulso.service.contract.PassivoService;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class PassivoServiceImpl<T> implements PassivoService<T> {

    private PassivoRepository repository;

    @Override
    @LogExecution
    @ObjectMapper(PassivoVO.class)
    public T salvarObjecao(Integer codigoPassivo, Integer objecao) {
        PassivoEntity entity = repository.findById(codigoPassivo).orElseThrow(DataNotMatchException::new);
        entity.setObjecao(objecao);
        return (T) repository.save(entity);
    }
}
