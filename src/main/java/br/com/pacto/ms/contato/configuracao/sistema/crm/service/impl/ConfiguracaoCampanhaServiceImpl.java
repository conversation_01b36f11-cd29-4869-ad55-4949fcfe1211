package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCampanhaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.CampanhaResponseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.CampanhaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCampanhaService;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponseStatusVO;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
import br.com.pacto.ms.contato.ia.service.impl.PactoConversasUrlResolverService;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.transaction.Transactional;
import java.net.URI;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConfiguracaoCampanhaServiceImpl implements ConfiguracaoCampanhaService {

    private final RequestService requestService;
    private final PactoConversasIAProxy pactoConversasIAProxy;
    private final PactoConversasUrlResolverService urlResolverService;

    @Override
    public List<ConfiguracaoCampanhaDTO> obterCampanhas(Integer empresa) {
        List<CampanhaResponseVO> campanhaVOS = new ArrayList<>();
        try {
            URI uri = URI.create(urlResolverService.getPactoConversasUrl());
            campanhaVOS = pactoConversasIAProxy.obterCampanhas(uri, this.identificadorEmpresa(empresa));
        } catch (Exception e) {
            log.error("Msg: Erro ao consultar campanhas: ", e.getMessage());
        }
        return ConfiguracaoCampanhaDTO.toDto(campanhaVOS);
    }

    @Override
    public ResponseStatusVO salvarCampanha(ConfiguracaoCampanhaDTO configuracaoCampanhaDTO) {
        try {
            URI uri = URI.create(urlResolverService.getPactoConversasUrl());
            CampanhaVO campanhaVO = CampanhaVO
                    .builder()
                    .nome(configuracaoCampanhaDTO.getTitulo())
                    .keyword(configuracaoCampanhaDTO.getTag())
                    .instrucao(configuracaoCampanhaDTO.getDescricao())
                    .imagem(configuracaoCampanhaDTO.getImagemPath())
                    .is_template(configuracaoCampanhaDTO.getTemplate())
                    .data_inicio(configuracaoCampanhaDTO.getPeriodoInicial())
                    .data_fim(Objects.isNull(configuracaoCampanhaDTO.getPeriodoFinal()) ? null
                            : configuracaoCampanhaDTO.getPeriodoFinal())
                    .build();
            ResponseStatusVO responseStatusVO = pactoConversasIAProxy.atualizarContextoCampanha(
                    uri,
                    identificadorEmpresa(configuracaoCampanhaDTO.getCodigoEmpresa()),
                    UUID.randomUUID().toString(),
                    campanhaVO
            );
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {}
            return responseStatusVO;
        } catch (ConfiguracaoIAException e) {
            throw new ConfiguracaoIAException(e.getMessage());
        }
    }

    @Override
    public ResponseStatusVO alterarCampanha(String id, ConfiguracaoCampanhaDTO dto) {
        try {
            CampanhaVO vo = CampanhaVO.builder()
                    .nome(dto.getTitulo())
                    .id_campanha(dto.getId())
                    .id_empresa(dto.getEmpresa())
                    .keyword(dto.getTag())
                    .instrucao(dto.getDescricao())
                    .imagem(dto.getImagemPath())
                    .is_template(dto.getTemplate())
                    .data_inicio(dto.getPeriodoInicial())
                    .data_fim(dto.getPeriodoFinal())
                    .build();

            URI uri = URI.create(urlResolverService.getPactoConversasUrl());
            ResponseStatusVO response = pactoConversasIAProxy.atualizarContextoCampanha(
                    uri,
                    identificadorEmpresa(dto.getCodigoEmpresa()),
                    id,
                    vo
            );
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {}
            log.info(response.getDetails());
            return response;
        } catch (ConfiguracaoIAException e) {
            throw new ConfiguracaoIAException(
                    "Falha ao notificar serviço de conversas: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void excluirCampanha(String id, Integer empresa) {
        try {
            URI uri = URI.create(urlResolverService.getPactoConversasUrl());
            pactoConversasIAProxy.deletarCampanha(
                    uri,
                    identificadorEmpresa(empresa),
                    id);

        } catch (Exception e) {
            e.printStackTrace();
            log.error("Erro ao excluir campanha: " + e.getMessage());
            throw new ConfiguracaoIAException("Erro ao excluir campanha: " + e.getMessage());
        }
    }

    public String identificadorEmpresa(Integer empresa) {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + empresa;
    }
}
