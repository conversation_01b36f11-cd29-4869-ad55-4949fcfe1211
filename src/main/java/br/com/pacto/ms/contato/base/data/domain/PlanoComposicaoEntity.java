package br.com.pacto.ms.contato.base.data.domain;


import lombok.Data;

import javax.persistence.*;

@Entity
@Data
@Table(name = "planocomposicao", schema = "public")
public class PlanoComposicaoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "composicao", nullable = false)
    private ComposicaoEntity composicao;

    @Column(name = "plano")
    private Integer plano;
}
