package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Embeddable;

@Data
@Embeddable
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoGymbot {

    @Column(name = "tokenGymbot")
    private String tokenGymbot;

    @Column(name = "habilitarGymbot")
    private Boolean habilitarGymbot;

    @Column(name = "descricaoDepartamento")
    private String descricaoDepartamento;

    @Column(name = "idDepartamento")
    private String idDepartamento;

    public ConfiguracaoGymbot(String tokenGymbot) {
        this.tokenGymbot = tokenGymbot;
    }
}
