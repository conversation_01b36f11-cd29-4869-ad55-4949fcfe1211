package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.TipoColaboradorVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class VinculoTiposColaboradorPorFaseDTO {

        @Schema(description = "Fase do crm")
        private FasesCRMEnum fase;
        @Schema(description = "tipos de colaboradores responsável pela fase do crm")
        private List<TipoColaboradorEnum> tiposColaboradores;
}
