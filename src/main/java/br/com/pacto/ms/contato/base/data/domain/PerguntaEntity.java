package br.com.pacto.ms.contato.base.data.domain;

import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name = "pergunta", schema = "public")
@Data
public class PerguntaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long codigo;

    @Column(name = "tipopergunta", length = 2, nullable = false)
    private String tipoPergunta;

    @Column(name = "descricao", length = 300, nullable = false)
    private String descricao;
}