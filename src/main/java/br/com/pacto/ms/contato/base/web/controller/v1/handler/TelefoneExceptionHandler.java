package br.com.pacto.ms.contato.base.web.controller.v1.handler;

import br.com.pacto.ms.contato.base.service.exception.TelefoneException;
import br.com.pactosolucoes.commons.data.vo.ResponseImplLegacyVO;
import br.com.pactosolucoes.commons.data.vo.ResponseImplVO;
import br.com.pactosolucoes.commons.data.vo.ResponseVO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.TELEFONE_EXCEPTION;
import static br.com.pactosolucoes.commons.enums.message.APIStructureMessages.PROCESS_FINISHED_ERROR;

public @ControllerAdvice
class TelefoneExceptionHandler {

    private @Autowired
    RequestService requestService;

    @ExceptionHandler(value = {TelefoneException.class})
    public ResponseEntity<?> handleArchetypeException(TelefoneException exception) {
        ResponseVO responseVO = null;

        if (requestService.getCurrentConfiguration().getReponseLegacy()) {
            responseVO = ResponseImplLegacyVO.init()
                    .addMessage(TELEFONE_EXCEPTION);
        } else {
            responseVO = ResponseImplVO.init()
                    .addMessage(TELEFONE_EXCEPTION)
                    .addMessage(PROCESS_FINISHED_ERROR);
        }

        return new ResponseEntity<ResponseVO>(responseVO, null, TELEFONE_EXCEPTION.getStatusCode());

    }
}
