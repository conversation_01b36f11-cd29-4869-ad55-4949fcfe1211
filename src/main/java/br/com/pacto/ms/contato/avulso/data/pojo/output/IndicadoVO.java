package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.validation.constraints.Size;

import br.com.pactosolucoes.commons.util.annotation.CpfCnpj;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IndicadoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private String nomeindicado;

	private String telefoneindicado;

	private String telefone;

	private String email;

	private Integer indicacao;

	private Integer cliente;

	private Integer empresa;

	private Short origemsistema;

	private Timestamp datalancamento;

	private String nomeconsulta;

	private Integer objecao;

	private Boolean lead;

	private String cpf;

}