package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class ChatMessageDTO {

    public enum Sender {
        IA, USER, SISTEMA
    }

    private Sender sender;
    private String content;

    public ChatMessageDTO(Sender sender, String content) {
        this.sender = sender;
        this.content = content;
    }

    public static List<ChatMessageDTO> transformarParaModelo(String observacaoCompleta) {
        List<ChatMessageDTO> listaChatMessageDTOS = new ArrayList<>();
        String[] linhas = observacaoCompleta.split("\n");

        Sender remetenteAtual = null;
        StringBuilder conteudoAtual = new StringBuilder();

        for (String linha : linhas) {
            linha = linha.trim();
            linha = linha.replaceAll("\\*(.*?)\\*", "<b>$1</b>");
            linha = linha.replaceAll("(https?://\\S+)", "<a href=\"$1\" target=\"_blank\">Clique aqui</a>");

            if (linha.startsWith("IA:")) {
                if (remetenteAtual != null) {
                    listaChatMessageDTOS.add(new ChatMessageDTO(remetenteAtual, conteudoAtual.toString().trim()));
                }
                remetenteAtual = Sender.IA;
                conteudoAtual = new StringBuilder(linha.replaceFirst("IA: ", "").trim());
            } else if (linha.startsWith("USER: INFORMAÇÃO DO SISTEMA: ")) {
                if (remetenteAtual != null) {
                    listaChatMessageDTOS.add(new ChatMessageDTO(remetenteAtual, conteudoAtual.toString().trim()));
                }
                remetenteAtual = Sender.SISTEMA;
                conteudoAtual = new StringBuilder(linha.replaceFirst("USER: INFORMAÇÃO DO SISTEMA: ", "").trim());
            } else if (linha.startsWith("USER:")) {
                if (remetenteAtual != null) {
                    listaChatMessageDTOS.add(new ChatMessageDTO(remetenteAtual, conteudoAtual.toString().trim()));
                }
                remetenteAtual = Sender.USER;
                conteudoAtual = new StringBuilder(linha.replaceFirst("USER: ", "").trim());
            } else {
                if (remetenteAtual != null) {
                    conteudoAtual.append("\n").append(linha);
                }
            }
        }

        if (remetenteAtual != null && conteudoAtual.length() > 0) {
            listaChatMessageDTOS.add(new ChatMessageDTO(remetenteAtual, conteudoAtual.toString().trim()));
        }

        return listaChatMessageDTOS;
    }

}