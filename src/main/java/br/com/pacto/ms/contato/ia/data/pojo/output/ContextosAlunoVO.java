package br.com.pacto.ms.contato.ia.data.pojo.output;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContextosAlunoVO {
    private ContextoAlunoVO aluno;
    private List<ContextoParcelaVO> parcelas;
    private List<ContextoContatoVO> contatos;
    private List<ContextoContatoVO> historicoContatos;
    private List<ContextoContratoVO> contratos;
    private List<ContextoTurmaVO> turmas;
    private ContextoProgramaContentVO programasTreino;
    private ContextoAvaliacoesFisicaContentVO avaliacaoFisica;
    private List<ContextoAcessoClienteVO> acessos;
    private BoletimDeVisitaVO boletimDeVisita;
}
