package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.VendasOnlineConfig;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface VendasOnlineConfigRepository extends PagingAndSortingRepository<VendasOnlineConfig, Integer> {

    @Query("SELECT c from VendasOnlineConfig  c where c.empresa = :empresa")
    Optional<List<VendasOnlineConfig>> consultarPorEmpresa(@Param("empresa") Integer empresa);
}
