package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ContextoFaseCRMVO {

    @Schema(description = "Código da fase.")
    private Integer codigo;

    @Schema(description = "Sigla da fase.")
    private String sigla;

    @Schema(description = "Descrição da fase.")
    private String descricao;

    @Schema(description = "Objeto relacionado à fase.")
    private String objetivo;
}
