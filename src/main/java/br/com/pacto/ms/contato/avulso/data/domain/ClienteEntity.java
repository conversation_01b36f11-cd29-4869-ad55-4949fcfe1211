package br.com.pacto.ms.contato.avulso.data.domain;

import br.com.pacto.ms.contato.base.enums.SituacaoClienteEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cliente", schema = "public")
public class ClienteEntity {
    @Basic
    @Column(name = "freepass", nullable = true)
    private Integer freepass;
    @Basic
    @Column(name = "responsavelfreepass", nullable = true)
    private Integer responsavelfreepass;
    @Basic
    @Column(name = "codigomatricula", nullable = true)
    private Integer codigomatricula;
    @Basic
    @Column(name = "identificadorparacobranca", nullable = true, length = 20)
    private String identificadorparacobranca;
    @Basic
    @Column(name = "contadigito", nullable = true, length = 10)
    private String contadigito;
    @Basic
    @Column(name = "conta", nullable = true, length = 20)
    private String conta;
    @Basic
    @Column(name = "agenciadigito", nullable = true, length = 10)
    private String agenciadigito;
    @Basic
    @Column(name = "agencia", nullable = true, length = 20)
    private String agencia;
    @Basic
    @Column(name = "banco", nullable = true, length = 30)
    private String banco;
    @Basic
    @Column(name = "codacesso", nullable = true, length = 20)
    private String codacesso;
    @Basic
    @Column(name = "codacessoalternativo", nullable = true, length = 25)
    private String codacessoalternativo;
    @Basic
    @Column(name = "categoria", nullable = true)
    private Integer categoria;
    @Basic
    @Column(name = "matricula", nullable = true, length = 10)
    private String matricula;
    @Basic
    @Column(name = "situacao", nullable = false, length = 2)
    private SituacaoClienteEnum situacao;

    @OneToOne
    @JoinColumn(name = "pessoa", referencedColumnName = "codigo")
    private PessoaEntity pessoa;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "empresa", nullable = true)
    private Integer empresa;
    @Basic
    @Column(name = "uacodigo", nullable = true)
    private Integer uacodigo;
    @Basic
    @Column(name = "matriculaexterna", nullable = true)
    private Long matriculaexterna;
    @Basic
    @Column(name = "parqpositivo", nullable = true)
    private Boolean parqpositivo;
    @Basic
    @Column(name = "pessoaresponsavel", nullable = true)
    private Integer pessoaresponsavel;
    @Basic
    @Column(name = "situacaoclube", nullable = true)
    private Integer situacaoclube;
    @Basic
    @Column(name = "datavalidadecarteirinha", nullable = true)
    private Date datavalidadecarteirinha;
    @Basic
    @Column(name = "verificarcliente", nullable = true)
    private Boolean verificarcliente;
    @Basic
    @Column(name = "verificadoem", nullable = true)
    private Timestamp verificadoem;
    @Basic
    @Column(name = "usuarioverificacao", nullable = true)
    private Integer usuarioverificacao;
    @Basic
    @Column(name = "porcentagemdescontoboletopagantecipado", nullable = true, precision = 2)
    private BigDecimal porcentagemdescontoboletopagantecipado;
    @Basic
    @Column(name = "objecao", nullable = true, updatable = true, insertable = false)
    private Integer objecao;
    @Basic
    @Column(name = "temmaisdeumadigital", nullable = true)
    private Boolean temmaisdeumadigital;
    @Basic
    @Column(name = "anexo", nullable = true, length = 255)
    private String anexo;
    @Basic
    @Column(name = "datacadastroanexo", nullable = true)
    private Date datacadastroanexo;
    @Basic
    @Column(name = "nomeanexo", nullable = true, length = 150)
    private String nomeanexo;
    @Basic
    @Column(name = "sesc", nullable = true)
    private Boolean sesc;
    @Basic
    @Column(name = "renda", nullable = true, precision = 0)
    private Double renda;
    @Basic
    @Column(name = "validadecartaosesc", nullable = true)
    private Timestamp validadecartaosesc;
    @Basic
    @Column(name = "matriculasesc", nullable = true, length = 100)
    private String matriculasesc;
    @Basic
    @Column(name = "nomesocial", nullable = true, length = 150)
    private String nomesocial;
    @Basic
    @Column(name = "gympasstypenumber", nullable = true, length = -1)
    private String gympasstypenumber;
    @Basic
    @Column(name = "gympassuniquetoken", nullable = true, length = -1)
    private String gympassuniquetoken;
    @Basic
    @Column(name = "indicadopor", nullable = true)
    private Integer indicadopor;
    @Basic
    @Column(name = "codamigofit", nullable = true)
    private Integer codamigofit;
    @Basic
    @Column(name = "bancoamigofit", nullable = true)
    private Integer bancoamigofit;
    @Basic
    @Column(name = "agenciaamigofit", nullable = true)
    private Integer agenciaamigofit;
    @Basic
    @Column(name = "digitoagenciaamigofit", nullable = true)
    private Integer digitoagenciaamigofit;
    @Basic
    @Column(name = "contaamigofit", nullable = true)
    private Integer contaamigofit;
    @Basic
    @Column(name = "digitocontaamigofit", nullable = true)
    private Integer digitocontaamigofit;
    @Basic
    @Column(name = "contacorrenteamigofit", nullable = true)
    private Boolean contacorrenteamigofit;
    @Basic
    @Column(name = "contapoupancaamigofit", nullable = true)
    private Boolean contapoupancaamigofit;
    @Basic
    @Column(name = "usernameamigofit", nullable = true, length = 50)
    private String usernameamigofit;
    @Basic
    @Column(name = "senhausuarioamigofit", nullable = true, length = 50)
    private String senhausuarioamigofit;
    @Basic
    @Column(name = "utilizarresponsavelpagamento", nullable = true)
    private Boolean utilizarresponsavelpagamento;
    @Basic
    @Column(name = "publicidmgb", nullable = true, length = -1)
    private String publicidmgb;
    @Basic
    @Column(name = "nivelmgb", nullable = true, length = -1)
    private String nivelmgb;

    @ManyToOne
    @JoinColumn(name = "objecao", referencedColumnName = "codigo", updatable = false, insertable = false)
    private ObjecaoEntity objecaoByObjecao;

}
