package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.PassivoEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

public interface PassivoRepository extends JpaRepository<PassivoEntity, Integer> , JpaSpecificationExecutor<PassivoEntity> {

    @Query("select p from PassivoEntity p where p.email = :email ORDER BY p.codigo ASC")
    Optional<PassivoEntity> consultarPorEmail(String email);
}
