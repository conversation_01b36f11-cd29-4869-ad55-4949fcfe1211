package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCampanhaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmFaseIADTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCampanhaService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@SuppressWarnings("unchecked")
@Validated
@Tag(name = "Configurações de Campanhas", description = "Gestão de configurações para gerir campanhas")
@RequestMapping(value = "/v1/configuracao/campanhas")
@RestController
@RequiredArgsConstructor
public class ConfiguracaoCrmCampanhaController extends BaseController {

    private final RequestService requestService;
    private final ConfiguracaoCampanhaService configuracaoCampanhaService;

    private void disableLegacyResponse() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
    }

    @Operation(summary = "Consultar")
    @GetMapping("/consultar")
    public ResponseEntity<ConfiguracaoCampanhaDTO> consultarCampanhas(
            @Parameter(name = "codigoEmpresa", description = "codigo da empresa", example = "1")
            @RequestParam(value = "codigoEmpresa", required = false) Integer empresa) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCampanhaDTO>) super.finish(configuracaoCampanhaService.obterCampanhas(empresa));
    }


    @Operation(summary = "Incluir campanha")
    @PostMapping()
    public ResponseEntity<ConfiguracaoCampanhaDTO> incluir(@RequestBody @Valid ConfiguracaoCampanhaDTO configuracaoCampanhaDTO) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCampanhaDTO>) super.finish(configuracaoCampanhaService
                .salvarCampanha(configuracaoCampanhaDTO));
    }

    @Operation(summary = "Alterar campanha")
    @PutMapping("/{id}")
    public ResponseEntity<ConfiguracaoCrmFaseIADTO> alterarCampanha(
            @PathVariable @Parameter(name = "id", description = "Código da campanha", example = "1") String id,
            @RequestBody @Valid ConfiguracaoCampanhaDTO configuracaoCampanhaDTO) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCrmFaseIADTO>) super.finish(configuracaoCampanhaService
                .alterarCampanha(id, configuracaoCampanhaDTO));
    }

    @Operation(summary = "Excluir campanha")
    @DeleteMapping("/{id}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<Void> excluirCampanha(
            @Parameter(name = "codigoEmpresa", description = "codigo da empresa", example = "1")
            @RequestParam(value = "codigoEmpresa", required = false) Integer empresa,
            @PathVariable @Parameter(name = "id", description = "id da campanha", example = "1") String id) {
        configuracaoCampanhaService.excluirCampanha(id, empresa);
        return (ResponseEntity<Void>) super.finish();
    }

}
