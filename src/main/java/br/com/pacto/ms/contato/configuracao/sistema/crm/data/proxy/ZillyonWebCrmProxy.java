package br.com.pacto.ms.contato.configuracao.sistema.crm.data.proxy;

import br.com.pacto.ms.contato.config.proxy.ZillyonWebProxyConfig;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.AgendamentoLeadVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ContextoPlanoVendidoOnlineVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "ZillyonWebCrm", configuration = ZillyonWebProxyConfig.class, url = "http://localhost:8200/ZillyonWeb")
public interface ZillyonWebCrmProxy {

    @GetMapping(path = "/prest/basico/processarMetas")
    void processarMetaDiaria();

    @PostMapping(path = "/ai/resgisto/contato/agendamento")
    void agendarLead(@RequestParam("chave") String chaveEmpresa, @RequestBody AgendamentoLeadVO agendamentoLeadVO);

}

