package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.LeadsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.NotificationSchemaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ScheduleNotificationsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoIALeadsService;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class ConfiguracaoCrmIALeadsServiceImpl implements ConfiguracaoIALeadsService {

    private final static String Type = "phase_message_send";

    private final static String Category = "LEADS_HOJE";

    private final PactoConversasIAProxy pactoConversasIAProxy;

    private final RequestService requestService;

    private final ConfiguracaoCrmIARepository configuracaoCrmIARepository;

    public String identificadorEmpresa(Integer empresa) {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + empresa;
    }

    public ResponsePactoConversasVO<ScheduleNotificationsDTO> enviarNotificacaco(LeadsDTO dto) {
        String url = requestService.getClienteDiscovery().getServiceUrls().getPactoConversasApiUrl();

        Map<String,String> parametros = new HashMap<>();
        parametros.put("scheduler_text", dto.getDescricaoReguaAtendimento());
        parametros.put("category", Category);
        String responseEnviarMensagemVO = this.pactoConversasIAProxy
                .enviarNotificationScheme(
                        URI.create(url),
                        identificadorEmpresa(dto.getCodigoEmpresa()),
                        Type,
                        parametros);

        Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities = configuracaoCrmIARepository
                .obterPorCodigoEmpresa(dto.getCodigoEmpresa());
        configuracaoCrmIAEntities.ifPresent(
                configList -> {
                    configList.stream().findFirst().ifPresent(
                            config -> {
                                config.setDescricaoNotificacaoProativo(dto.getDescricaoReguaAtendimento());
                                configuracaoCrmIARepository.save(config);
                            });
                });
        ResponsePactoConversasVO responsePactoConversasVO = new ResponsePactoConversasVO();
        responsePactoConversasVO.setContexto(responseEnviarMensagemVO);
        return responsePactoConversasVO;
    }

    @Override
    public ResponsePactoConversasVO<ScheduleNotificationsDTO> obterNotificacoes(Integer codigoEmpresa) {
        String url = requestService.getClienteDiscovery().getServiceUrls().getPactoConversasApiUrl();

        NotificationSchemaVO response = this.pactoConversasIAProxy
                .obterNotificationScheme(
                        URI.create(url),
                        identificadorEmpresa(codigoEmpresa),
                        Type,
                        Category);

        ResponsePactoConversasVO responsePactoConversasVO = new ResponsePactoConversasVO();
        responsePactoConversasVO.setContexto(response);
        return responsePactoConversasVO;
    }

}
