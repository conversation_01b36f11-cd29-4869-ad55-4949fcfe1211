package br.com.pacto.ms.contato.core.data.pojo.enums;

public enum StatusPDF {
    COMPLETO("completed"),
    PENDENTE("pending"),
    NAO_ENCONTRADO("not_found"),
    ERRO("error");

    private final String value;

    StatusPDF(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static StatusPDF fromValue(String value) {
        for (StatusPDF action : values()) {
            if (action.value.equalsIgnoreCase(value)) {
                return action;
            }
        }
        throw new IllegalArgumentException("Ação desconhecida: " + value);
    }
}