package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoVO;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;

import java.text.ParseException;

public interface ConfiguracaoCrmFaixasDeHorarioDeAcessoService<T, DTO> {

    T consultar();

    T incluir(ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO) throws Exception;

    void excluir(Integer codigo);

    T alterar(Integer codigo, ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO dto) throws ParseException;
}
