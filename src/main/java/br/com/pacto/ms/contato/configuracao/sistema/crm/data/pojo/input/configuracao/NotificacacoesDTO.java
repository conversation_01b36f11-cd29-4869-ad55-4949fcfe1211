package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class NotificacacoesDTO {
    List<NotificacaoProativoDTO> notificacoesEventoDiaBefore;
    List<NotificacaoProativoDTO> notificacoesEventoDiaAfter;
    List<NotificacaoProativoDTO> notificacoesDiasIntermediarios;
    List<NotificacaoProativoDTO> notificacoesMesmoDia;
}
