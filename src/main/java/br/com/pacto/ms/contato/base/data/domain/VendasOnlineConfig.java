package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "vendasonlineconfig", schema = "public")
public class VendasOnlineConfig {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "empresa")
    private Integer empresa;

    @Column(name = "urlvenda")
    private String urlVenda;

    @Column(name = "habilitarcampanha")
    private Boolean habilitarCampanha;

    @Column(name = "apresentarcpflinkpag")
    private Boolean apresentarCpfLinkPag;

    @Column(name = "apresentardtfaturalinkpag")
    private Boolean apresentarDtFaturaLinkPag;

    @Column(name = "titulocheckout")
    private String tituloCheckout;

    @Column(name = "cobrarprimeiraparcelacompra")
    private Boolean cobrarPrimeiraParcelaCompra;

    @Column(name = "detalharparcelatelacheckout")
    private Boolean detalharParcelaTelaCheckout;

    @Column(name = "cobrarprodutosjuntoadesao")
    private Boolean cobrarProdutosJuntoAdesao;

    @Column(name = "emailavisar")
    private String emailAvisar;

    @Column(name = "redirecionarapp")
    private Boolean redirecionarApp;

}
