package br.com.pacto.ms.contato.base.enums;

import java.util.Arrays;

public enum SituacaoClienteEnum {

    VISITANTE_NORMAL("VI", "Visitante Normal"),
    FREE_PASS("PL", "Free Pass"),
    AULA_AVULSA("AA", "Aula Avulsa"),
    DIARIA("DI", "Diária"),
    NORMAL("NO", "Normal"),
    CARENCIA("CR", "Férias"),
    A_VENCER("AV", "A vencer"),
    TRANCADO("TR", "Trancado"),
    TRANCADO_VENCIDO("TV", "Trancado Vencido"),
    VENCIDO("VE", "Vencido"),
    DESISTENCIA("DE", "Desistência"),
    CANCELADO("CA", "Cancelado"),
    ATESTADO_INATIVO("AI", "Atestado(IN)"),
    RENOVADO("RN", "Renovado"),
    INATIVO("IN", "Inativo"),
    ATIVO("AT", "Ativo");

    private String codigo;
    private String descricao;

    SituacaoClienteEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static SituacaoClienteEnum findByCodigo(String codigo) {
        return Arrays.stream(values())
                .filter(SituacaoClienteEnum -> SituacaoClienteEnum.getCodigo().equals(codigo))
                .findFirst().orElse(null);
    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
