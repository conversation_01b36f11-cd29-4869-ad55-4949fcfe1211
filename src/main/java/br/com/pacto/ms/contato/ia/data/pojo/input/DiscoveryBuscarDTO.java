package br.com.pacto.ms.contato.ia.data.pojo.input;

import lombok.*;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class DiscoveryBuscarDTO {
    private Content content;

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class Content {
        private List<String> modulosHabilitados;
        private List<Empresa> empresas;
        private List<FinanceiroEmpresa> financeiroEmpresas;
        private Object redeEmpresas;
        private ServiceUrls serviceUrls;
        private boolean utilizarMoviDesk;
        private boolean utilizarChatMoviDesk;
        private Object grupoChatMovidesk;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class Empresa {
        private int codigo;
        private String nome;
        private String fotoKey;
        private String codigoEmpresaRede;
        private int codigoFinanceiro;
        private boolean ativa;
        private String siglaEstado;
        private String pais;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class FinanceiroEmpresa {
        private Object empresazw;
        private Object codigoFinanceiro;
        private String nome;
        private String cidade;
        private Object estado;
        private String pais;
        private int redeEmpresaId;
        private String chaveRede;
    }

    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Getter
    @Setter
    public static class ServiceUrls {
        private String alunoMsUrl;
        private String loginMsUrl;
        private String colaboradorMsUrl;
        private String graduacaoMsUrl;
        private String treinoApiUrl;
        private String treinoUrl;
        private String loginAppUrl;
        private String oamdUrl;
        private String zwUrl;
        private String personagemMsUrl;
        private String autenticacaoUrl;
        private String frontPersonal;
        private String planoMsUrl;
        private String clienteMsUrl;
        private String produtoMsUrl;
        private String relatorioFull;
        private Object sinteticoMsUrl;
        private String pactoPayDashUrl;
        private String pactoPayMsUrl;
        private String financeiroMsUrl;
        private String cadastroAuxiliarUrl;
        private String zwFrontUrl;
        private String treinoFrontUrl;
        private String apiZwUrl;
        private String relatorioMsUrl;
        private String midiaMsUrl;
        private String clubeVantagensMsUrl;
        private String acessoSistemaMsUrl;
        private String admCoreUrl;
        private String pessoaMsUrl;
        private String integracoesMsUrl;
        private String loginFrontUrl;
        private String biMsUrl;
        private Object gameUrl;
        private String vendasOnlineUrl;
        private String contatoMsUrl;
        private String admMsUrl;
        private Object integracaoGympassMsUrl;
        private String urlMidiaSocialMs;
        private String urlCrmFront;
        private Object urlMarketingMs;
        private String notificacaoMs;
        private String urlTreinoPersonal;
        private String recursoMsUrl;
        private String zwBack;
    }
}