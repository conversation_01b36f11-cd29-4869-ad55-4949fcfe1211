package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.contato.ia.data.pojo.output.LinkAppVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.TreinoAppLoginVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@FeignClient(name = "TreinoApp", url = "https://app-do-aluno-unificado.web.app")
@Component
public interface TreinoAppProxy {

    @RequestMapping(path = "/usuario/solicitarLinkDeLoginV2",
            method = RequestMethod.PATCH,
            consumes = MediaType.APPLICATION_JSON_VALUE)
    LinkAppVO solicitarLinkLogin(@RequestBody TreinoAppLoginVO treinoAppLoginVO);
}
