package br.com.pacto.ms.contato.config.proxy;

import br.com.pacto.ms.contato.ia.service.contract.PactoConversaAuthService;
import br.com.pacto.ms.contato.ia.util.annotation.PactoConversaAuthToken;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class PactoConversasIAProxyConfig implements RequestInterceptor {

    @Autowired
    private PactoConversaAuthService pactoConversaAuthService;

    @Override
    public void apply(RequestTemplate template) {
        if (template.methodMetadata().method().isAnnotationPresent(PactoConversaAuthToken.class)) {
            String token = pactoConversaAuthService.getAuthTokenPactoConversa();
            if (StringUtils.hasText(token)) {
                template.header("Authorization", "Bearer " + token);
            }
        }
    }
}