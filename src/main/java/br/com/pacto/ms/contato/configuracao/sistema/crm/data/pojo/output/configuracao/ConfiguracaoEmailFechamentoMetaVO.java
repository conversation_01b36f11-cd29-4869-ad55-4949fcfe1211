package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoEmailFechamentoMetaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.EmpresaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


@Data
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class ConfiguracaoEmailFechamentoMetaVO {

    @Schema(description = "Código do registro do email", example = "1")
    private Integer codigo;
    @Schema(description = "Email de fechamento de meta", example = "<EMAIL>")
    private String email;
    private EmpresaVO empresa;

}
