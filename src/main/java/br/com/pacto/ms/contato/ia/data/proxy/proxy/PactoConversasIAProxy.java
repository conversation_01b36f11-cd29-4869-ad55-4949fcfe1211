package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.comuns.data.pojo.output.DepartamentoVO;
import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.NotificationSchemaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ScheduleNotificationsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.CampanhaResponseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.CampanhaVO;
import br.com.pacto.ms.contato.ia.data.pojo.input.IndicadorDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.InserirPdfRequestVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponseStatusVO;
import br.com.pacto.ms.contato.ia.util.annotation.PactoConversaAuthToken;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@FeignClient(name = "PactoConversas", url = "${conversas.ia.api-url-hml}")
public interface PactoConversasIAProxy {

    @PactoConversaAuthToken
    @PostMapping(path = "/atualizar_contexto_academia/", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponsePactoConversasVO<ContextoEmpresaVO> atualizarContextoEmpresa(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody ContextoEmpresaVO contextoEmpresaVO);

    @PactoConversaAuthToken
    @PostMapping(path = "/atualizar_contexto_turmas/", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponsePactoConversasVO<List<ContextoTurmaVO>> atualizarContextoTurma(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody List<ContextoTurmaVO> contextoTurmaVOS);

    @PactoConversaAuthToken
    @PostMapping(path = "/atualizar_contexto_planos/", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponsePactoConversasVO<ContextosPlanosVO> atualizarContextoPlanos(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody ContextosPlanosVO contextosPlanosVO);

    @PactoConversaAuthToken
    @PostMapping(path = "/atualizar_contexto_fases/", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponsePactoConversasVO<List<ContextoFaseVO>> atualizarContextoFases(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody List<ContextoFaseVO> contextosPlanosVO);

    @PactoConversaAuthToken
    @PostMapping(path = "/enviar_mensagem/", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEnviarMensagemVO enviarMensagem(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody ContextosAlunoVO contextosAlunoVO);

    @PactoConversaAuthToken
    @PostMapping(path = "/atualizar_contexto_personalidade/", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponsePactoConversasVO<ResponseEnviarMensagemVO> atualizarContextoPersonalidade(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody ConfiguracaoCrmIAVO configuracaoCrmIAVO);

    @PostMapping(path = "/atualizar_contexto_produtos/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponsePactoConversasVO<List<ProdutoVO>> atualizarContextoProdutos(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody List<ProdutoVO> contextoProdutosVO);

    @PostMapping(path = "/atualizar_contexto_aluno/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponsePactoConversasVO<ContextosAlunoVO> atualizarContextoAluno(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody ContextosAlunoVO contextoAlunoVO);

    @GetMapping(path = "/get_qr_code", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String obterQrCode(URI baseUrl, @RequestParam("instance_id") String instancia, @RequestParam("token") String token);

    @PostMapping(path = "/disconnect_instance", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String desconectarInstancia(URI baseUrl, @RequestParam("instance_id") String instancia, @RequestParam("token") String token);

    @PostMapping(path = "/create_instance", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String criarInstancia(URI baseUrl, @RequestBody HashMap<String,Object> map);

    @PostMapping(path = "/subscribe_instance", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String subscreverInstancia(URI baseUrl, @RequestParam("instance_id") String instancia, @RequestParam("token") String token);

    @GetMapping(path = "/check_instance_status", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String checkarStatusInstancia(URI baseUrl, @RequestParam("instance_id") String instancia, @RequestParam("token") String token);

    @PostMapping(path = "/cancel_instance", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String cancelarInstancia(URI baseUrl, @RequestParam("instance_id") String instancia, @RequestParam("token") String token);

    @GetMapping(path = "/instance", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String obterInstanciaEmpresa(URI baseUrl, @RequestParam("id_empresa") String idEmpresa);

    @GetMapping(path = "/device", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String getDevice(URI baseUrl, @RequestParam("instance_id") String instancia, @RequestParam("token") String token);

    @GetMapping(path = "/departamento", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    List<DepartamentoVO> obterDepartamentos(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("canal") String canal);

    @PostMapping(path = "/atualizar_token", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponsePactoConversasVO<String> atualizarToken(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("canal") String canal, HashMap<String, Object> body);

    @PostMapping(path = "/departamento", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponsePactoConversasVO<String> salvarConfiguracaoGymbotConvesasIA(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("canal") String canal, DepartamentoVO departamentoVO);

    @GetMapping(path = "/consultar_token", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    HashMap<String, String> consultarToken(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("canal") String canal);

    @PostMapping(path = "/notification_scheme/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    String enviarNotificationScheme(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("notification_type") String type, @RequestBody Map<String, String> body);

    @GetMapping(path = "/notification_scheme/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ScheduleNotificationsDTO obterNotificacoes(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("notification_type") String type);

    @GetMapping(path = "/notification_scheme/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    NotificationSchemaVO obterNotificationScheme(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("notification_type") String type, @RequestParam("category") String category);

    @GetMapping(path = "/consultar_contexto_campanhas/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    List<CampanhaResponseVO> obterCampanhas(URI uri, @RequestParam("empresa") String empresa);

    @PostMapping(path = "/atualizar_contexto_campanha/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponseStatusVO atualizarContextoCampanha(URI baseUrl, @RequestParam("empresa") String empresa, @RequestParam("id_campanha") String idCampanha, @RequestBody CampanhaVO campanhaVO);

    @DeleteMapping(path = "/apagar_contexto_campanha/", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponseStatusVO deletarCampanha(URI uri, @RequestParam("empresa") String empresa, @RequestParam("id_campanha") String idCampanha);

    @GetMapping(path = "/doc", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponsePDFDocumentoVO consultarPDF(URI baseUrl, @RequestParam("empresa") String empresa);

    @PostMapping(path = "/doc", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    InserirPdfResponseVO inserirPDF(URI baseUrl, @RequestParam("empresa") String empresa, @RequestBody InserirPdfRequestVO request);

    @DeleteMapping(path = "/doc", produces = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    InserirPdfResponseVO deletarPDF(URI baseUrl, @RequestParam("empresa") String empresa);

    @PostMapping(path = "/bi/indicadores", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @PactoConversaAuthToken
    ResponseStatusVO indicadoresBI(URI baseUrl, @RequestParam("empresa") String empresa,  @RequestBody IndicadorDTO dto);
}
