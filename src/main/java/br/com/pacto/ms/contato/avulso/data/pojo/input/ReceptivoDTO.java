package br.com.pacto.ms.contato.avulso.data.pojo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReceptivoDTO {
    @Schema(description = "Nome do contato", example = "<PERSON>", required = true)
    private String nome;
    @Schema(description = "Telefone celular do contato", example = "+5562999887766", required = true)
    private String telefonecelular;
    @Schema(description = "Telefone de trabalho do contato", example = "+5562999887766")
    private String telefonetrabalho;
    @Schema(description = "Telefone residencial do contato", example = "+5562999887766")
    private String telefoneresidencial;
    @Schema(description = "E-mail do contato", example = "<EMAIL>")
    private String email;
    @Schema(description = "Observação do contato", example = "O cliente está interessado em um novo plano")
    private String observacao;
    @Schema(description = "Código da objeção")
    private Integer objecao;

    public void setObjecao(Integer objecao) {
        this.objecao = (objecao != null && objecao == 0) ? null : objecao;
    }
}
