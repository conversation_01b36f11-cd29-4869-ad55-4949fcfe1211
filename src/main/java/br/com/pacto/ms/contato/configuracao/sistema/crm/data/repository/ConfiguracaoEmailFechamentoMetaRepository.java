package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoEmailFechamentoMetaEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

public interface ConfiguracaoEmailFechamentoMetaRepository extends PagingAndSortingRepository<ConfiguracaoEmailFechamentoMetaEntity, Integer> {

    ConfiguracaoEmailFechamentoMetaEntity findFirstByEmailAndAndEmpresaCodigo(String email, Integer empresaCodigo);

}

