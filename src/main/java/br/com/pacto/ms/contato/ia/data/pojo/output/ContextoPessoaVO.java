package br.com.pacto.ms.contato.ia.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ContextoPessoaVO {

    @Schema(description = "Código identificador da pessoa.")
    private Integer codigo;

    @Schema(description = "Nome completo da pessoa.")
    private String nome;

    @Schema(description = "CPF da pessoa, não nulo, com exatamente 14 caracteres.")
    private String cpf;

    @Schema(description = "Nome da mãe da pessoa, pode ser nulo, com no máximo 50 caracteres.")
    private String nomeMae;

    @Schema(description = "Nome do pai da pessoa, pode ser nulo, com no máximo 50 caracteres.")
    private String nomePai;

    @Schema(description = "Data de nascimento da pessoa.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date dataNasc;

    @Schema(description = "Data de cadastro da pessoa.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date dataCadastro;

    @Schema(description = "Sexo da pessoa, pode ser nulo, com no máximo 2 caracteres.")
    private String sexo;

    @Schema(description = "Naturalidade da pessoa.")
    private String naturalidade;

    @Schema(description = "Nacionalidade da pessoa, pode ser nulo, com no máximo 20 caracteres.")
    private String nacionalidade;

    @Schema(description = "Estado civil da pessoa, pode ser nulo, com no máximo 10 caracteres.")
    private String estadoCivil;

    @Schema(description = "Grau de instrução da pessoa, pode ser nulo.")
    private Integer grauInstrucao;

    @Schema(description = "Profissão da pessoa, pode ser nulo.")
    private Integer profissao;

    @Schema(description = "CPF do pai da pessoa, pode ser nulo, com no máximo 30 caracteres.")
    private String cpfPai;

    @Schema(description = "CPF da mãe da pessoa, pode ser nulo, com no máximo 30 caracteres.")
    private String cpfMae;

    @Schema(description = "Nome usado para consulta, pode ser nulo, com no máximo 80 caracteres.")
    private String nomeConsulta;

    @Schema(description = "Gênero da pessoa, pode ser nulo, com no máximo 2 caracteres.")
    private String genero;

    @Schema(description = "Email do pai da pessoa, pode ser nulo.")
    private String emailPai;

    @Schema(description = "Email da mãe da pessoa, pode ser nulo.")
    private String emailMae;

    @Schema(description = "Nome de registro da pessoa, pode ser nulo, com no máximo 120 caracteres.")
    private String nomeRegistro;

    @Schema(description = "Lista de endereços da pessoa.")
    private List<ContextoEnderecoVO> enderecos;

    @Schema(description = "Lista de emails da pessoa.")
    private List<ContextoEmailVO> emails;

    @Schema(description = "Telefones da pessoa")
    private List<ContextoTelefoneVO> telefones;

    @Schema(description = "Número de telefone principal do aluno que será utilziado em conversas")
    private String telefonesconsulta;
}
