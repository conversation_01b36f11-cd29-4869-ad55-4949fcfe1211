package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.TermoSpamEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface TermoSpanRepository extends JpaRepository<TermoSpamEntity, String> {

    @Query("select t from TermoSpamEntity t where t.termo = :termo")
    Optional<TermoSpamEntity> findByTermo(String termo);

}

