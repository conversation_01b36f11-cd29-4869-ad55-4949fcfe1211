package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "questionario", schema = "public")
public class QuestionarioEntity {
    private String nomeinterno;
    private int codigo;
    private String tipoquestionario;
    private String fundocor;
    private String fundoimagem;
    private String textoinicio;
    private String textofim;
    private Boolean somenteumaresposta;
    private Boolean ativo;
    private String titulopesquisa;

    @Basic
    @Column(name = "nomeinterno")
    public String getNomeinterno() {
        return nomeinterno;
    }


    @Id
    @Column(name = "codigo")
    public int getCodigo() {
        return codigo;
    }



    @Basic
    @Column(name = "tipoquestionario")
    public String getTipoquestionario() {
        return tipoquestionario;
    }


    @Basic
    @Column(name = "fundocor")
    public String getFundocor() {
        return fundocor;
    }

    @Basic
    @Column(name = "fundoimagem")
    public String getFundoimagem() {
        return fundoimagem;
    }


    @Basic
    @Column(name = "textoinicio")
    public String getTextoinicio() {
        return textoinicio;
    }


    @Basic
    @Column(name = "textofim")
    public String getTextofim() {
        return textofim;
    }

    public void setTextofim(String textofim) {
        this.textofim = textofim;
    }

    @Basic
    @Column(name = "somenteumaresposta")
    public Boolean getSomenteumaresposta() {
        return somenteumaresposta;
    }


    @Basic
    @Column(name = "ativo")
    public Boolean getAtivo() {
        return ativo;
    }


    @Basic
    @Column(name = "titulopesquisa")
    public String getTitulopesquisa() {
        return titulopesquisa;
    }

}
