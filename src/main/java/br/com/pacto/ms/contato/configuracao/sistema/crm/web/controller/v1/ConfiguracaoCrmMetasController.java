package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmDiasMetasDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmDiasMetasVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ProcessarMetaDiariaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmDiasMetasService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.FaseCrmService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ZwCrmService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RestController
@RequestMapping(value = "/v1/configuracao/meta")
public class ConfiguracaoCrmMetasController extends BaseController {

    @Autowired
    private ConfiguracaoCrmDiasMetasService<ConfiguracaoCrmDiasMetasVO, ConfiguracaoCrmDiasMetasDTO> service;
    @Autowired
    private RequestService requestService;
    @Autowired
    private FaseCrmService faseCrmService;
    @Autowired
    private ZwCrmService zwCrmService;

    @Operation(summary = "Consultar metas")
    @GetMapping
    public ResponseEntity<ConfiguracaoCrmDiasMetasVO> consultar(
            @Parameter(name = "fase", description = "Identificador da fase do CRM", example = "EX_ALUNOS") @RequestParam(required = false) FasesCRMEnum fase,
            @Parameter(name = "tipoFase", description = "Identificador do tipo da fase do CRM", example = "ESTUDIO") @RequestParam(required = false) TipoFaseCRMEnum tipoFase
    ) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoCrmDiasMetasVO>) super.finish(service.consultar(fase, tipoFase));
    }

    @Operation(summary = "Cadastrar configuração de metas")
    @PostMapping
    public ResponseEntity<ConfiguracaoCrmDiasMetasVO> incluir(@RequestBody @Valid ConfiguracaoCrmDiasMetasDTO configuracaoCrmDiasMetasDTO){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoCrmDiasMetasVO>) super.finish(service.incluir(configuracaoCrmDiasMetasDTO));
    }

    @Operation(summary = "Alterar configuração de metas")
    @PutMapping("/{codigo}")
    public ResponseEntity<ConfiguracaoCrmDiasMetasVO> alterar(
            @PathVariable @Parameter(name = "codigo", description = "Código da configuração de meta", example = "1") Integer codigo,
            @RequestBody @Valid ConfiguracaoCrmDiasMetasDTO configuracaoCrmDiasMetasDTO){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoCrmDiasMetasVO>) super.finish(service.alterar(codigo, configuracaoCrmDiasMetasDTO));
    }

    @Operation(summary = "Excluir configuração de metas")
    @DeleteMapping("/{codigo}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<?> excluir(@Parameter(name = "codigo", description = "Código da do registro de Faixas de Horário de Acesso", example = "1")
                                     @PathVariable Integer codigo){
        this.service.excluir(codigo);
        return super.finish(ExceptionMessage.CONFIGURACAO_DIAS_METAS_EXCLUSAO);
    }

    @Operation(summary = "Consultar ordenação de metas do CRM")
    @GetMapping("ordenacao")
    public ResponseEntity<FaseVO> consultarOrdenacaoMetas() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<FaseVO>) super.finish(faseCrmService.consultarOrdenacaoMetas());
    }

    @Operation(summary = "Consultar ordenação de metas padrão do CRM")
    @GetMapping("ordenacao/padrao")
    public ResponseEntity<FaseVO> consultarOrdenacaoMetasPadrao() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<FaseVO>) super.finish(faseCrmService.consultarOrdenacaoMetasPadrao());
    }

    @Operation(summary = "Processar meta diária do CRM")
    @PostMapping("processar")
    public ResponseEntity<ProcessarMetaDiariaVO> processarMetaDiaria() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ProcessarMetaDiariaVO>) super.finish(zwCrmService.processarMetaDiaria());
    }

}
