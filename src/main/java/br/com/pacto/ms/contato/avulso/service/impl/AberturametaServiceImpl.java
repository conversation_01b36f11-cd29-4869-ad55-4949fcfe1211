package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.pojo.output.AberturametaVO;
import br.com.pacto.ms.contato.avulso.data.repository.AberturametaRepository;
import br.com.pacto.ms.contato.avulso.service.contract.AberturametaService;
import br.com.pacto.ms.contato.avulso.service.exception.MetaFechadaException;
import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.GenericException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.AGENDAMENTO_DIA_INFORMADO_EXCEPTION;
import static br.com.pactosolucoes.utils.DateUtils.maior;

@AllArgsConstructor
public @Service class AberturametaServiceImpl<T> implements AberturametaService<T> {

    private AberturametaRepository repository;

    @Override
    @LogExecution
    public void verificarMetaAberta(Integer codigoColaborador, Integer codigoEmpresa, LocalDateTime dia) {
        Date diaEmDate = Date.from(dia.atZone(ZoneId.systemDefault()).toInstant());
        repository.findByColaboradorresponsavelAndEmpresaAndDia(codigoColaborador, codigoEmpresa, diaEmDate).
            orElseThrow(MetaFechadaException::new);
    }

    @Override
    @LogExecution
    public void verificarAberturaMetaColaboradorAgendamento(Integer codigoColaborador, Integer codigoEmpresa, LocalDateTime dia) {
        Date diaEmDate = Date.from(dia.atZone(ZoneId.systemDefault()).toInstant());
        if(this.repository.findByColaboradorresponsavelAndEmpresaAndDia(codigoColaborador, codigoEmpresa, diaEmDate).isPresent() &&
          maior(diaEmDate, Calendar.getInstance().getTime())) {
            throw new GenericException(new ExceptionMessageVO().message(AGENDAMENTO_DIA_INFORMADO_EXCEPTION));
        }
    }

}
