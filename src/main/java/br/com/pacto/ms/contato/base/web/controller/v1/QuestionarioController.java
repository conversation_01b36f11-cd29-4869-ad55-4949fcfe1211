package br.com.pacto.ms.contato.base.web.controller.v1;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.QUESTIONARIO;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.QUESTIONARIO_DESCRICAO;

import br.com.pacto.ms.comuns.Util;
import br.com.pacto.ms.contato.base.data.pojo.output.QuestionarioVO;
import br.com.pacto.ms.contato.base.service.contract.QuestionarioService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoSistemaCrmVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoPerguntaEnum;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.http.client.utils.URIBuilder;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.Positive;
import java.net.URI;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static br.com.pactosolucoes.utils.EncriptUtil.encriptar;

@Validated
@RestController
@RequestMapping("/v1/generico/questionario")
@Tag(name = QUESTIONARIO, description = QUESTIONARIO_DESCRICAO)
public class QuestionarioController  extends BaseController {

    @Autowired
    private RequestService requestService;
    @Autowired
    private QuestionarioService<QuestionarioVO> hcService;

    @Autowired
    private ConfiguracaoSistemaCrmService<ConfiguracaoSistemaCrmVO, ?> configuracaoSistemaCrmService;

    @Value("${discovery.url}")
    private String discovery;


    @Operation(summary = "Busca as questoes pelo tipo",
            description = "Busca as questoes pelo tipo ")
    @LogExecution
    @GetMapping("/tipoquestionario")
    public ResponseEntity<?> buscarPorTipo() {
        JSONObject filter = requestService.getCurrentConfiguration().getFilters();
        ResponseEntity<?> response = null;

        if (Objects.nonNull(filter) && filter.has("quicksearchValue")
                && !filter.get("quicksearchValue").toString().equals("null")) {
            response = buscarPorNomeTipo("PS", filter.getString("quicksearchValue"));
        } else {
            response = super.finish(hcService.buscarPortipo("PS"));
        }
        return response;
    }

    @Operation(summary = "Busca as questões pelo nome e tipo",
            description = "Busca as questões pelo nome e tipo  Exemplo: PS, PL ...")
    @LogExecution
    @GetMapping("/tipoquestionario/{tipo}/nome/{nome}")
    public ResponseEntity<?> buscarPorNomeTipo(@PathVariable String  nome, @PathVariable String tipo) {
        return super.finish( hcService.buscarPortipo(nome, tipo));
    }

    @Operation(summary = "Monta o link da pesquisa", description = "Monta o link da pesquisa")
    @LogExecution
    @GetMapping("/linkpesquisa/{codigo}/{cliente}/{colaborador}")
    public ResponseEntity<?> linkPesquisa(@Positive @PathVariable Integer codigo,
                                          @Positive @PathVariable Integer cliente,
                                          @Positive @PathVariable Integer colaborador) throws Exception {

        JSONObject json = new JSONObject();
        String key = requestService.getCurrentConfiguration().getCompanyKey();
        ConfiguracaoSistemaCrmVO configuracaoSistemaCRMVO = configuracaoSistemaCrmService.consultar();
        String tokenBitly = configuracaoSistemaCRMVO.getTokenbitly();

        json.put("key", key);
        json.put("questionario", codigo);
        json.put("cliente", cliente);
        json.put("empresa", 0);
        json.put("colaborador", colaborador);

        String encryptedQuery = encriptar(json.toString(), "PESQUIS@");
        String safeQuery = URLEncoder.encode(encryptedQuery, "UTF-8");

        URIBuilder uriBuilder = new URIBuilder(this.discovery + "/redir/" + key + "/zillyonWeb");
        uriBuilder.addParameter("m", "/faces/pesquisa.jsp");
        uriBuilder.addParameter("q", safeQuery);
        URI uri = uriBuilder.build();
        String urlPesquisa = uri.toString();

         if (!Util.emptyString(tokenBitly)) {
            JSONObject jsonBitLy = new JSONObject();
            jsonBitLy.put("domain", "bit.ly");
            jsonBitLy.put("long_url", urlPesquisa);
            urlPesquisa = Util.enviarSolicitacaoEncurtarLink(jsonBitLy, tokenBitly);
        }

        if (Util.emptyString(urlPesquisa)) {
            throw new Exception("Erro ao gerar URL da Pesquisa! Questionario: " + codigo + " | Cliente: " + cliente);
        }

        ObjectMapper map = new ObjectMapper();
        ObjectNode obj = map.createObjectNode();
        List<ObjectNode> list = new ArrayList<>();
        obj.put("url", urlPesquisa);
        list.add(obj);
        return super.finish(list);
    }


    @Operation(summary = "Lista o enum tipo de pergunta disertativa, objetiva",
            description = "Lista o enum tipo de pergunta disertativa, objetiva")
    @LogExecution
    @GetMapping("/tipomensagemEnum")
    public ResponseEntity<?> buscarPorTipoMensagem() {
        return super.finish(TipoPerguntaEnum.getListTipoPerguntaEnuM(""));
    }



}
