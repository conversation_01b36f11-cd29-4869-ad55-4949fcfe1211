package br.com.pacto.ms.contato.base.data.domain;

import lombok.Data;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "acessocliente")
@Data
public class AcessoClienteEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long codigo;

    @Column(name = "cliente", nullable = false)
    private Integer cliente;

    @Column(name = "sentido", nullable = false, length = 1)
    private String sentido;

    @Column(name = "situacao", length = 50)
    private String situacao;

    @Column(name = "dthrentrada", nullable = false)
    @Temporal(TemporalType.TIMESTAMP)
    private Date dthrEntrada;

    @Column(name = "dthrsaida")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dthrSaida;

    @Column(name = "meioidentificacaoentrada")
    private Short meioIdentificacaoEntrada;

    @Column(name = "meioidentificacaosaida")
    private Short meioIdentificacaoSaida;

    @Column(name = "tipoacesso")
    private Short tipoAcesso;

    @Column(name = "dataregistro")
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataRegistro;

    @Column(name = "ticket", length = 70)
    private String ticket;

    @Column(name = "nomecodempresaacessou", length = 255)
    private String nomeCodEmpresaAcessou;
}
