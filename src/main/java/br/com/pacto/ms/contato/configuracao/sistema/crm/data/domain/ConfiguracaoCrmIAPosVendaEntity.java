package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.log.data.listener.LogListener;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaocrmiaposvenda", schema = "public")
@EntityListeners(LogListener.class)
public class ConfiguracaoCrmIAPosVendaEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @Column(name = "codigoposvenda", nullable = false)
    private Integer codigoposvenda;

    @Basic
    @Column(name = "instrucao")
    private String instrucao;

    @Basic
    @Column(name = "habilitar")
    private Boolean habilitar;

}
