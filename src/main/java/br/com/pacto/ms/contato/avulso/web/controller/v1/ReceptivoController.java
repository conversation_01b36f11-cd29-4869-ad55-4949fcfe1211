package br.com.pacto.ms.contato.avulso.web.controller.v1;

import br.com.pacto.ms.contato.avulso.data.pojo.input.ReceptivoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ReceptivoFiltro;
import br.com.pacto.ms.contato.avulso.data.pojo.output.PassivoVO;
import br.com.pacto.ms.contato.avulso.service.contract.ReceptivoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.time.Instant;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.RECEPTIVO;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.RECEPTIVO_DESCRICAO;

@Tag(name = RECEPTIVO, description = RECEPTIVO_DESCRICAO)

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/receptivo")
public class ReceptivoController extends BaseController {

    private final ReceptivoService receptivoService;

    @PostMapping
    @LogExecution
    @Operation(summary = "Cadastrar um receptivo", description = "Salva um receptivo.")
    public ResponseEntity<?> salvar(@RequestBody ReceptivoDTO receptivo) {
        PassivoVO passivoVO = receptivoService.cadastrarReceptivo(receptivo);
        return super.finish(passivoVO);
    }

    @LogExecution
    @GetMapping(value = "/{codigo}")
    @Operation(summary = "Buscar um receptivo.", description = "Buscar receptivo por código.")
    public ResponseEntity<?> obter(@PathVariable("codigo") Integer codigo) {
        PassivoVO passivoVO = receptivoService.obter(codigo);
        return super.finish(passivoVO);
    }

    @LogExecution
    @GetMapping
    @Operation(summary = "Buscar Receptivo", description = "Buscar receptivo por filtro.")
    public ResponseEntity<?> buscar(
        @Parameter(name = "dia", description = "Dia do cadastro do Receptivo.", example = "2024-09-10T10:15:30.00Z")
        @RequestParam(name = "dia", required = false) String dia,
        @RequestParam(name = "objecao", required = false) Integer objecao,
        @RequestParam(name = "colaboradoResponsavel", required = false) Integer colaboradoResponsavel,
        @RequestParam(name = "pagina", defaultValue = "1") Integer pagina,
        @RequestParam(name = "quantidade", defaultValue = "10") Integer quantidade
    ) {
        ReceptivoFiltro receptivoFiltro = ReceptivoFiltro.builder()
                .dia(Timestamp.from(Instant.parse(dia)))
                .objecao(objecao)
                .colaboradorResponsavel(colaboradoResponsavel)
                .pagina(pagina - 1)
                .quantidade(quantidade)
            .build();
        Page<PassivoVO> pagePassivo = receptivoService.buscar(receptivoFiltro);
        return super.finish(pagePassivo);
    }

}
