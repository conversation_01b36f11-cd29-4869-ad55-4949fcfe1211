package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.comuns.data.pojo.output.DepartamentoVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoGymbotVO {
    private Integer codigoConfiguracao;
    private Integer codigoEmpresa;
    private String tokenGymbot;
    private Boolean habilitarGymbot;
    private DepartamentoVO departamento;
}
