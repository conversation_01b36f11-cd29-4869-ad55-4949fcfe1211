package br.com.pacto.ms.contato.ia.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponsePactoConversasVO<T> {
    private String success;
    private T contexto;


    public static <T> ResponsePactoConversasVO<T> ok(T contexto) {
        return new ResponsePactoConversasVO<>("true", contexto);
    }
}
