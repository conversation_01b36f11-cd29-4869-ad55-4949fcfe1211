package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogFieldIgnore;
import br.com.pactosolucoes.commons.util.annotation.LogFieldValueTransform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaodiasposvenda", schema = "public")
@EntityListeners(LogListener.class)
public class ConfiguracaosistemaCrmDiasPosVendasEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "configuracaosistemacrm")
    @LogFieldIgnore
    private Integer configuracaoSistemaCRM;
    @Basic
    @Column(name = "nrdia")
    private Integer nrDia;
    @Basic
    @Column(name = "descricao")
    private String descricao;
    @Basic
    @Column(name = "ativo")
    private Boolean ativo;
    @Basic
    @Column(name = "siglaresponsavelpelocontato")
    @LogFieldValueTransform(TipoColaboradorValueTransform.class)
    private String siglaResponsavelPeloContato;


}
