package br.com.pacto.ms.contato.avulso.web.controller.v1;

import br.com.pacto.ms.contato.avulso.data.pojo.input.ObjecaoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ObjecaoVO;
import br.com.pacto.ms.contato.avulso.service.contract.ObjecaoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.util.List;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.OBJECAO;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.OBJECAO_DESCRICAO;
import static org.springframework.http.HttpStatus.NO_CONTENT;

@Tag(name = OBJECAO, description = OBJECAO_DESCRICAO)

@Validated
@RestController
@RequestMapping("/v1/comum/objecao")
public class ObjecaoController extends BaseController {

    private @Autowired ObjecaoService<ObjecaoVO> objecaoService;

    @Operation(summary = "Salvar uma Objeção", description = "Salva uma objeção simples.")
    @PostMapping
    @LogExecution
    public ResponseEntity<?> salvar(@Valid @RequestBody ObjecaoDTO objecao) {
        return super.finish(objecaoService.salvar(objecao));
    }

    @Operation(summary = "Atualizar uma Objeção", description = "Atualiza uma objeção simples.")
    @PutMapping("/id/{id}")
    @LogExecution
    public ResponseEntity<?> atualizar( @Positive Integer id,
                                        @RequestBody ObjecaoDTO objecao) {
        return super.finish(objecaoService.atualizar(id, objecao));
    }

    @Operation(summary = "Excluir uma Objeção", description = "Exclui uma objeção simples.")
    @DeleteMapping("/id/{id}")
    @LogExecution
    @ResponseStatus(NO_CONTENT)
    public void excluir(@Positive Integer id) {
        objecaoService.excluir(id);
    }

    @Operation(summary = "Excluir uma lista de Objeção", description = "Exclui uma lista de objeção simples.")
    @DeleteMapping("/ids/{ids}")
    @LogExecution
    @ResponseStatus(NO_CONTENT)
    public void excluirPorListaDeId(@NotEmpty List<Integer> ids,
                                    @RequestBody ObjecaoDTO objecao) {
        objecaoService.excluir(ids);
    }

    @Operation(summary = "Busca todas as Objeções", description = "Busca todas as objeções.")
    @GetMapping
    @LogExecution
    public ResponseEntity<?> buscarTodos(@RequestParam(required = false, defaultValue = "0") Integer page,
                                         @RequestParam(required = false, defaultValue = "5") Integer size,
                                         @RequestParam(required = false) String[] orderBy) {

        return super.finish(objecaoService.buscarTodos(super.getPagingAndSortingData(page, size, orderBy)));
    }

    @Operation(summary = "Busca as Objeções por tipo", description = "Busca as objeções contendo uma parte do tipo do grupo.")
    @GetMapping("/tipo/{tipo}")
    @LogExecution
    public ResponseEntity<?> buscarPorTipo(@RequestParam(required = false, defaultValue = "0") Integer page,
                                           @RequestParam(required = false, defaultValue = "5") Integer size,
                                           @RequestParam(required = false) String[] orderBy,
                                           @Size(min = 1, max = 2) @PathVariable String tipo) {
        return super.finish(objecaoService.buscarPorTipo(super.getPagingAndSortingData(page, size, orderBy), tipo));
    }

    @Operation(summary = "Busca as Objeções por tipo", description = "Busca as objeções contendo uma parte do tipo do grupo.")
    @GetMapping("somenteAtivos/tipo/{tipo}")
    @LogExecution
    public ResponseEntity<?> buscarSomenteAtivosPorTipo(@RequestParam(required = false, defaultValue = "0") Integer page,
                                           @RequestParam(required = false, defaultValue = "50") Integer size,
                                           @RequestParam(required = false) String[] orderBy,
                                           @Size(min = 1, max = 2) @PathVariable String tipo) {
        return super.finish(objecaoService.buscarSomenteAtivosPorTipo(super.getPagingAndSortingData(page, size, orderBy), tipo));
    }

    @Operation(summary = "Busca as Objeções por grupo", description = "Busca as objeções contendo uma parte do grupo.")
    @GetMapping("/grupo/{grupo}")
    @LogExecution
    public ResponseEntity<?> buscarPorGrupo(@RequestParam(required = false, defaultValue = "0") Integer page,
                                            @RequestParam(required = false, defaultValue = "5") Integer size,
                                            @RequestParam(required = false) String[] orderBy,
                                            @Size(min = 3, max = 50) @PathVariable String grupo) {
        return super.finish(objecaoService.buscarPorGrupo(super.getPagingAndSortingData(page, size, orderBy), grupo));
    }

    @Operation(summary = "Busca as Objeções por descrição", description = "Busca as objeções contendo uma parte da descrição")
    @GetMapping("/descricao/{descricao}")
    @LogExecution
    public ResponseEntity<?> buscarPorDescricao(@RequestParam(required = false, defaultValue = "0") Integer page,
                                                @RequestParam(required = false, defaultValue = "5") Integer size,
                                                @RequestParam(required = false) String[] orderBy,
                                                @Size(min = 3, max = 50) @PathVariable String descricao) {
        return super.finish(objecaoService.buscarPorDescricao(super.getPagingAndSortingData(page, size, orderBy), descricao));
    }
}