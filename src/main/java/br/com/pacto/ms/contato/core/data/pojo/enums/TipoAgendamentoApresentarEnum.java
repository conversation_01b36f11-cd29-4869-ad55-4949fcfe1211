package br.com.pacto.ms.contato.core.data.pojo.enums;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum TipoAgendamentoApresentarEnum {

        LIGACAO("LI", "Ligação"),
        VISITNATE("VI", "Visitante"),
        AULAEXPERIMENTAL("AE", "Aula Experimental"),
        GYPASS("AE", "GymPass"),
        DIARIA("DI", "Diária")
        ;
        private String sigla;
        private String descricao;

        public static List<ObjectNode> getListTipoAgendamentoApresentarEnum(String filter){
                ObjectMapper map = new ObjectMapper();
                List<ObjectNode> list = new ArrayList<>();
                Arrays.stream(TipoAgendamentoApresentarEnum.values()).forEach(item ->{
                                ObjectNode obj = map.createObjectNode();
                                if (!filter.equals("") && item.getDescricao().toLowerCase().contains(filter)) {
                                        obj.put("sigla", item.getSigla());
                                        obj.put("descricao", item.getDescricao());
                                        list.add(obj);
                                        return;
                                }else{
                                        if(filter.equals("")){
                                                obj.put("sigla", item.getSigla());
                                                obj.put("descricao", item.getDescricao());
                                                list.add(obj);
                                        } }
                        }
                );
                return list;
        }
}
