package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.CampanhaResponseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.CampanhaVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoCampanhaDTO {
    @Schema(description = "id", example = "1")
    private String id;
    @Schema(description = "codigoEmpresa", example = "1")
    private Integer codigoEmpresa;
    @Schema(description = "empresa", example = "1")
    private String empresa;
    @Schema(description = "titulo", example = "1")
    private String titulo;
    @Schema(description = "tag", example = "1")
    private String tag;
    @Schema(description = "linkWhatsapp", example = "https:wa.me..")
    private String linkWhatsapp;
    @Schema(description = "descricao", example = "1")
    private String descricao;
    @Schema(description = "imagemPath", example = "1")
    private String imagemPath;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    @Schema(description = "periodoInicial", example = "1")
    private LocalDateTime periodoInicial;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    @Schema(description = "periodoFinal", example = "1")
    private LocalDateTime periodoFinal;
    @Schema(description = "verificacao para template")
    private Boolean template;

    public static ConfiguracaoCampanhaDTO toDto(CampanhaResponseVO vo){
        return ConfiguracaoCampanhaDTO.builder()
                .id(vo.getId_campanha())
                .empresa(vo.getId_empresa())
                .titulo(vo.getNome())
                .tag(vo.getKeyword())
                .descricao(vo.getInstrucao())
                .imagemPath(vo.getImagem())
                .linkWhatsapp(vo.getWhatsapp_link())
                .template(vo.getIs_template())
                .periodoInicial(parseDate(vo.getData_inicio()))
                .periodoFinal(parseDate(vo.getData_fim()))
                .build();
    }

    public static List<ConfiguracaoCampanhaDTO> toDto(List<CampanhaResponseVO> campanhas){
        return campanhas.stream().map(ConfiguracaoCampanhaDTO::toDto).collect(Collectors.toList());
    }

    private static LocalDateTime parseDate(String date) {
        if( Objects.nonNull(date) && !date.isEmpty()){
            try{
                if(date.equals("None")) return null;
                return LocalDateTime.parse(date, DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));
            } catch (Exception e) {}
        }
        return null;
    }

}
