package br.com.pacto.ms.contato.base.data.domain;


import lombok.Data;

import javax.persistence.*;

@Entity
@Data
@Table(name = "planoexcecao", schema = "public")
public class PlanoExcecaoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long codigo;

    @ManyToOne
    @JoinColumn(name = "pacote", referencedColumnName = "codigo", foreignKey = @ForeignKey(name = "fk_planoexcecao_pacote"))
    private ComposicaoEntity pacote;

    @Column(nullable = false)
    private Integer plano;

    @ManyToOne
    @JoinColumn(name = "modalidade", referencedColumnName = "codigo", foreignKey = @ForeignKey(name = "fk_planoexcecao_modalidade"))
    private ModalidadeEntity modalidade;

    @Column(name = "vezessemana")
    private Integer vezesSemana;

    @Column(name = "duracao")
    private Integer duracao;

    @ManyToOne
    @JoinColumn(name = "horario", referencedColumnName = "codigo")
    private HorarioEntity horario;

    @Column(name = "valor")
    private Double valor;
}
