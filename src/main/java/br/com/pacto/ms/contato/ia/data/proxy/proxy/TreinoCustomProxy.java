package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.contato.config.proxy.TreinoProxyCustomConfig;
import br.com.pacto.ms.contato.ia.data.pojo.output.ContextoAvaliacoesFisicasVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ContextoProgramasVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

@FeignClient(name = "TreinoWeb", configuration = TreinoProxyCustomConfig.class, url = "http://localhost:8201/TreinoWeb")
public interface TreinoCustomProxy {

    @GetMapping(path = "/prest/psec/alunos/obter-aluno-completo-por-matricula/{matricula}", produces = MediaType.APPLICATION_JSON_VALUE)
    ContextoProgramasVO obterAlunoPorMatricula(@PathVariable("matricula") Integer matricula, @RequestHeader(value = "empresaId", required = true) Integer empresaId, @RequestHeader(value = "Authorization") String token);

    @GetMapping(path = "/prest/psec/perfil/aluno/{matricula}/avaliacao", produces = MediaType.APPLICATION_JSON_VALUE)
    ContextoAvaliacoesFisicasVO obterAvaliacaoFisicaPorMatricula(@PathVariable("matricula") Integer matricula, @RequestHeader(value = "empresaId", required = true) Integer empresaId, @RequestHeader(value = "Authorization") String token);

}
