package br.com.pacto.ms.contato.base.web.controller.v1;

import br.com.pacto.ms.contato.base.data.pojo.output.ModalidadeVO;
import br.com.pacto.ms.contato.base.service.contract.ModalidadeService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.MODALIDADE;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.MODALIDADE_DESCRICAO;

@Validated
@RestController
@RequestMapping("/v1/generico/modalidade")
@Tag(name = MODALIDADE, description = MODALIDADE_DESCRICAO)
public class ModeloController extends BaseController {

    @Autowired
    private RequestService requestService;
    @Autowired
    private ModalidadeService<ModalidadeVO>  hcservice;


    @Operation(summary = "Busca modalidade ",
            description = "Busca modalidade ")
    @LogExecution
    @GetMapping
    public ResponseEntity<?> buscaModalidade() {
        JSONObject filter = requestService.getCurrentConfiguration().getFilters();
        if (Objects.nonNull(filter) && filter.has("quicksearchValue") && !filter.get("quicksearchValue").toString().equals("null")) {
            return super.finish( hcservice.buscarModalidade( filter.getString("quicksearchValue").toUpperCase()));
        }
        return super.finish( hcservice.buscarTodasModalidade());
    }
}
