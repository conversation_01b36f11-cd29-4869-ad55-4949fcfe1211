package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pactosolucoes.commons.util.annotation.LogTagTransformContract;

public class TipoVinculoFaseLogTagTransform  implements LogTagTransformContract<TiposVinculosFaseEntity> {

    @Override
    public String transform(TiposVinculosFaseEntity tiposVinculosFaseEntity) {
        if(tiposVinculosFaseEntity == null){
            return null;
        }

        return tiposVinculosFaseEntity.getFase().getName();
    }
}
