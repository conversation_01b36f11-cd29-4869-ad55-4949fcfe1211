package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ContextoEmailVO {

    @Schema(description = "Código identificador do email.")
    private Integer codigo;

    @Schema(description = "Endereço de email.")
    private String email;

    @Schema(description = "Indica se o email é de correspondência.")
    private Boolean emailCorrespondencia;

    @Schema(description = "Indica se o email está bloqueado por bounce.")
    private Boolean bloqueadoBounce;

    @Schema(description = "Indica se o usuário optou por receber novidades por email.")
    private Boolean receberEmailNovidades;
}
