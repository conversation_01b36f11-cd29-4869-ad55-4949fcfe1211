package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.VinculoTiposColaboradorPorFaseDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.VinculoFaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.VinculoTiposColaboradorPorFaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.FaseCrmService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.TiposVinculosFaseService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;
import java.util.Objects;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RestController
@RequestMapping(value = "/v1/configuracao")
public class ConfiguracaoFaseController extends BaseController {

    @Autowired
    private TiposVinculosFaseService tiposVinculosFaseService;
    @Autowired
    private FaseCrmService faseCrmService;
    @Autowired
    private RequestService requestService;


    @Operation(summary = "Consultar fases do CRM")
    @GetMapping("fases")
    public ResponseEntity<FaseVO> consultarFase(
            @RequestParam(required = false) List<TipoFaseCRMEnum> tiposFase
    ) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<FaseVO>) super.finish(faseCrmService.consultar(tiposFase));
    }

    @Operation(summary = "Consultar fases do CRM")
    @GetMapping("fases/cod_name")
    public ResponseEntity<List<FaseVO>> consultarFases(
            @RequestParam(required = false) List<TipoFaseCRMEnum> tiposFase
    ) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        JSONObject filter = requestService.getCurrentConfiguration().getFilters();
        String descricao = "";
        if(Objects.nonNull(filter) && filter.has("quicksearchValue") && !filter.get("quicksearchValue").toString().equals("null")){
            descricao = filter.get("quicksearchValue").toString();
        }
        return ResponseEntity.ok((List<FaseVO>) faseCrmService.consultarCodName(tiposFase, descricao)) ;
    }


    @Operation(summary = "Consultar associação de tipo de colaboradores fases do CRM")
    @GetMapping("responsaveis")
    public ResponseEntity<VinculoFaseVO> consultarVinculos() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<VinculoFaseVO>) super.finish(tiposVinculosFaseService.consutlar());
    }

    @Operation(summary = "Consultar padrão de tipos de colaboradores responsáveis por fases do CRM")
    @GetMapping("responsaveis-por-fase/padrao")
    public ResponseEntity<VinculoTiposColaboradorPorFaseVO> consultarPadraoPorFases() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<VinculoTiposColaboradorPorFaseVO>) super.finish(tiposVinculosFaseService.consutlarPadrao());
    }

    @Operation(summary = "Consultar tipos de colaboradores responsáveis por fases do CRM")
    @GetMapping("responsaveis-por-fase")
    public ResponseEntity<VinculoTiposColaboradorPorFaseVO> consultarPorFases() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<VinculoTiposColaboradorPorFaseVO>) super.finish(tiposVinculosFaseService.consultarPorFases());
    }

    @Operation(summary = "Consultar tipos de colaboradores por fases do CRM", description = "Consultar tipos de colaboradores associados a fases do CRM")
    @GetMapping("responsaveis-por-fase/{fase}")
    public ResponseEntity<VinculoTiposColaboradorPorFaseVO> consultarPorFase(
            @Parameter(description = "Fase do crm", example = "AGENDAMENTO") @PathVariable FasesCRMEnum fase) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<VinculoTiposColaboradorPorFaseVO>) super.finish(tiposVinculosFaseService.consultarPorFase(fase));
    }

    @Operation(summary = "Alterar todos os tipos de colaboradores associados a uma fase do CRM")
    @PutMapping("responsaveis-por-fase/{fase}")
    public ResponseEntity<VinculoFaseVO> alterarPorFase(
            @Parameter(description = "sigla da fase do CRM", example = "AGENDAMENTO") @PathVariable FasesCRMEnum fase,
            @RequestBody @Valid List<TipoColaboradorEnum> tipoColaboradorEnums) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<VinculoFaseVO>) super.finish(tiposVinculosFaseService.alterarTodos(fase, tipoColaboradorEnums));
    }

    @Operation(summary = "Alterar todos os tipos de colaboradores associados a multiplas fases do CRM")
    @PutMapping("responsaveis-por-fase")
    public ResponseEntity<VinculoTiposColaboradorPorFaseVO> alterarMultiplasFases(
            @RequestBody @Valid List<VinculoTiposColaboradorPorFaseDTO> vinculoTiposColaboradorPorFase) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<VinculoTiposColaboradorPorFaseVO>) super.finish(tiposVinculosFaseService.alterarTodos(vinculoTiposColaboradorPorFase));
    }

}
