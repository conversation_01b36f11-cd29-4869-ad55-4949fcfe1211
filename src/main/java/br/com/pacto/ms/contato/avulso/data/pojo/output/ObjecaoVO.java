package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.util.List;

import br.com.pactosolucoes.commons.data.RepresentationModelData;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ObjecaoVO extends RepresentationModelData<ObjecaoVO> {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Boolean ativo;

	private String comentario;

	private String descricao;

	private String grupo;

	private String tipogrupo;
}