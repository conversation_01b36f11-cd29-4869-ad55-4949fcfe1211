package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Arrays;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "modelomensagem", schema = "public")
public class ModelomensagemEntity {
    @Basic
    @Column(name = "mensagem")
    private String mensagem;

    @Basic
    @Column(name = "tipomensagem")
    private String tipomensagem;
    @Basic
    @Column(name = "titulo")
    private String titulo;

    @Id
    @Column(name = "codigo")
    private int codigo;

    @Basic
    @Column(name = "imagemmodelo")
    private byte[] imagemmodelo;
    @Basic
    @Column(name = "nomeimagem")
    private String nomeimagem;
    @Basic
    @Column(name = "meiodeenvio")
    private Short meiodeenvio;

    @Basic
    @Column(name = "ativo")
    private Boolean ativo;
    @Basic
    @Column(name = "urlredirecionamento")
    private String urlredirecionamento;
    @Basic
    @Column(name = "builder")
    private Boolean builder;
    @Basic
    @Column(name = "configs")
    private String configs;
}
