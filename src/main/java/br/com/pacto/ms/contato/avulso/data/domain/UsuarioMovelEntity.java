package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "usuariomovel", schema = "public")
public class UsuarioMovelEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "nome", nullable = false, unique = true)
    private String nome;

    @Column(name = "senha", nullable = false)
    private String senha;

    @Column(name = "cliente")
    private Integer cliente;

    @Column(name = "ativo", nullable = false, columnDefinition = "boolean default true")
    private Boolean ativo;

    @Column(name = "usuariozw")
    private Integer usuariozw;

    @Column(name = "empresa")
    private Integer empresa;

    @Column(name = "origem", columnDefinition = "varchar default 'TW'")
    private String origem;

    @Column(name = "cpf", length = 20)
    private String cpf;

    @Column(name = "indicado")
    private Integer indicado;

    @Column(name = "usuariotw")
    private Integer usuariotw;
}
