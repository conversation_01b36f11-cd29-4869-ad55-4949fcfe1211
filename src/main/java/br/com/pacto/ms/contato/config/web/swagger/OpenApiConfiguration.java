package br.com.pacto.ms.contato.config.web.swagger;


import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import org.springframework.context.annotation.Bean;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;

@OpenAPIDefinition(info = @Info(
									title = "${swagger.config.info.title}", 
									version = "${swagger.config.info.version}", 
									description = "${swagger.config.info.description}"
								)
				   )
public class OpenApiConfiguration {

	public @Bean OpenAPI customOpenAPI() {
		final String securitySchemeName = "Authorization bearer";
		return new OpenAPI()
				.addSecurityItem(new SecurityRequirement()
						.addList(securitySchemeName))
				.components(new Components()
						.addSecuritySchemes(securitySchemeName, new SecurityScheme()
								.name(securitySchemeName)
								.type(SecurityScheme.Type.HTTP)
								.scheme("Bearer")
								.bearerFormat("JWT")));
		
	  }
}
