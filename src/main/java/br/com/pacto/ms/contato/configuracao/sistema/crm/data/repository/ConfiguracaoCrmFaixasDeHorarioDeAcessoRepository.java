package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoEmailFechamentoMetaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ConfiguracaoCrmFaixasDeHorarioDeAcessoRepository extends PagingAndSortingRepository<ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity, Integer> {


    @Query("select c from ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity c where :codigo is null or c.codigo != :codigo")
    List<ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity> findAllNotContainsCodigo(Integer codigo);
}

