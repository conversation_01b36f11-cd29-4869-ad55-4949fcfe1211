package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.ObjecaoEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;


public interface ObjecaoRepository extends PagingAndSortingRepository<ObjecaoEntity, Integer> {

    Optional<List<ObjecaoEntity>> findByTipogrupoContainingIgnoreCase(String tipo, Pageable pageable);

    Optional<List<ObjecaoEntity>> findByTipogrupoContainingIgnoreCaseAndAtivo(String tipo, boolean ativo, Pageable pageable);

    Optional<List<ObjecaoEntity>> findByGrupoContainingIgnoreCase(String grupo, Pageable pageable);

    Optional<Page<ObjecaoEntity>> findByDescricaoContainingIgnoreCase(String descricao, Pageable pageable);
}
