package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;

@DynamicUpdate
@DynamicInsert
@Data
@NoArgsConstructor
@Entity
@Table(name = "empresa", schema = "public")
public class EmpresaEntity {
    @Basic
    @Column(name = "nrdiasavencer", nullable = true)
    private Integer nrdiasavencer;
    @Basic
    @Column(name = "toleranciapagamento", nullable = true)
    private Integer toleranciapagamento;
    @Basic
    @Column(name = "qtdfaltapeso1", nullable = true)
    private Integer qtdfaltapeso1;
    @Basic
    @Column(name = "qtdfaltainiciopeso2", nullable = true)
    private Integer qtdfaltainiciopeso2;
    @Basic
    @Column(name = "qtdfaltaterminopeso2", nullable = true)
    private Integer qtdfaltaterminopeso2;
    @Basic
    @Column(name = "qtdfaltapeso3", nullable = true)
    private Integer qtdfaltapeso3;
    @Basic
    @Column(name = "fotorelatorio", nullable = true)
    private byte[] fotorelatorio;
    @Basic
    @Column(name = "foto", nullable = true)
    private byte[] foto;
    @Basic
    @Column(name = "alturafotoempresa", nullable = true, length = 50)
    private String alturafotoempresa;
    @Basic
    @Column(name = "alturafotorelatorio", nullable = true, length = 50)
    private String alturafotorelatorio;
    @Basic
    @Column(name = "largurafotoempresa", nullable = true, length = 50)
    private String largurafotoempresa;
    @Basic
    @Column(name = "largurafotorelatorio", nullable = true, length = 50)
    private String largurafotorelatorio;
    @Basic
    @Column(name = "carenciarenovacao", nullable = true)
    private Integer carenciarenovacao;
    @Basic
    @Column(name = "mascaramatricula", nullable = true, length = -1)
    private String mascaramatricula;
    @Basic
    @Column(name = "nrdiasvigentequestionariovisita", nullable = true)
    private Integer nrdiasvigentequestionariovisita;
    @Basic
    @Column(name = "nrdiasvigentequestionarioretorno", nullable = true)
    private Integer nrdiasvigentequestionarioretorno;
    @Basic
    @Column(name = "nrdiasvigentequestionariorematricula", nullable = true)
    private Integer nrdiasvigentequestionariorematricula;
    @Basic
    @Column(name = "permitecontratosconcomintante", nullable = true)
    private Boolean permitecontratosconcomintante;
    @Basic
    @Column(name = "permitesituacaoatestadocontrato", nullable = true)
    private Boolean permitesituacaoatestadocontrato;
    @Basic
    @Column(name = "questionariorematricula", nullable = true)
    private Integer questionariorematricula;
    @Basic
    @Column(name = "questionarioretorno", nullable = true)
    private Integer questionarioretorno;
    @Basic
    @Column(name = "questionarioprimeiravisita", nullable = true)
    private Integer questionarioprimeiravisita;
    @Basic
    @Column(name = "fax", nullable = true, length = 14)
    private String fax;
    @Basic
    @Column(name = "site", nullable = true, length = 50)
    private String site;
    @Basic
    @Column(name = "email", nullable = true, length = 50)
    private String email;
    @Basic
    @Column(name = "telcomercial3", nullable = true, length = 14)
    private String telcomercial3;
    @Basic
    @Column(name = "telcomercial2", nullable = true, length = 14)
    private String telcomercial2;
    @Basic
    @Column(name = "telcomercial1", nullable = true, length = 14)
    private String telcomercial1;
    @Basic
    @Column(name = "inscestadual", nullable = false, length = 20)
    private String inscestadual;
    @Basic
    @Column(name = "cnpj", nullable = false, length = 18)
    private String cnpj;
    @Basic
    @Column(name = "cep", nullable = true, length = 10)
    private String cep;
    @Basic
    @Column(name = "estado", nullable = false)
    private Integer estado;
    @Basic
    @Column(name = "cidade", nullable = false)
    private Integer cidade;
    @Basic
    @Column(name = "pais", nullable = false)
    private Integer pais;
    @Basic
    @Column(name = "complemento", nullable = true, length = 50)
    private String complemento;
    @Basic
    @Column(name = "numero", nullable = true, length = 5)
    private String numero;
    @Basic
    @Column(name = "setor", nullable = true, length = 50)
    private String setor;
    @Basic
    @Column(name = "endereco", nullable = false, length = 50)
    private String endereco;
    @Basic
    @Column(name = "razaosocial", nullable = false, length = 50)
    private String razaosocial;
    @Basic
    @Column(name = "nome", nullable = false, length = 50)
    private String nome;
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "carencia", nullable = true)
    private Integer carencia;
    @Basic
    @Column(name = "nrdiasprorata", nullable = true)
    private Integer nrdiasprorata;
    @Basic
    @Column(name = "somadv", nullable = true)
    private Integer somadv;
    @Basic
    @Column(name = "fotoemail", nullable = true)
    private byte[] fotoemail;
    @Basic
    @Column(name = "alturafotoemail", nullable = true, length = 50)
    private String alturafotoemail;
    @Basic
    @Column(name = "largurafotoemail", nullable = true, length = 50)
    private String largurafotoemail;
    @Basic
    @Column(name = "toleranciadiascontratovencido", nullable = true)
    private Integer toleranciadiascontratovencido;
    @Basic
    @Column(name = "timezonedefault", nullable = true, length = 40)
    private String timezonedefault;
    @Basic
    @Column(name = "urlrecorrencia", nullable = true, length = -1)
    private String urlrecorrencia;
    @Basic
    @Column(name = "serviceusuario", nullable = true, length = 100)
    private String serviceusuario;
    @Basic
    @Column(name = "servicesenha", nullable = true, length = 255)
    private String servicesenha;
    @Basic
    @Column(name = "nrdiascompensacao", nullable = true)
    private Short nrdiascompensacao;
    @Basic
    @Column(name = "toleranciaocupacaoturma", nullable = true)
    private Short toleranciaocupacaoturma;
    @Basic
    @Column(name = "bloquearacessoseparcelaaberta", nullable = true)
    private Boolean bloquearacessoseparcelaaberta;
    @Basic
    @Column(name = "pessoafinan", nullable = true)
    private Integer pessoafinan;
    @Basic
    @Column(name = "consultorvendaavulsa", nullable = true)
    private Integer consultorvendaavulsa;
    @Basic
    @Column(name = "permitehorariosconcorrentesparaprofessor", nullable = true)
    private Boolean permitehorariosconcorrentesparaprofessor;
    @Basic
    @Column(name = "professoremambientesdiferentesmesmohorario", nullable = true)
    private Boolean professoremambientesdiferentesmesmohorario;
    @Basic
    @Column(name = "mostrarcnpj", nullable = true)
    private Boolean mostrarcnpj;
    @Basic
    @Column(name = "codigolistaservico", nullable = true, length = 20)
    private String codigolistaservico;
    @Basic
    @Column(name = "codigotributacaomunicipal", nullable = true, length = 20)
    private String codigotributacaomunicipal;
    @Basic
    @Column(name = "issretido", nullable = true)
    private Boolean issretido;
    @Basic
    @Column(name = "usarnfse", nullable = true)
    private Boolean usarnfse;
    @Basic
    @Column(name = "chavenfse", nullable = true, length = 100)
    private String chavenfse;
    @Basic
    @Column(name = "tokensms", nullable = true, length = -1)
    private String tokensms;
    @Basic
    @Column(name = "dataexpiracao", nullable = true)
    private Date dataexpiracao;
    @Basic
    @Column(name = "nrdiaschequeavista", nullable = true)
    private Integer nrdiaschequeavista;
    @Basic
    @Column(name = "mostrarmodalidade", nullable = true)
    private Boolean mostrarmodalidade;
    @Basic
    @Column(name = "bvobrigatorio", nullable = true)
    private Boolean bvobrigatorio;
    @Basic
    @Column(name = "tempoaposfaltareposicao", nullable = true)
    private Integer tempoaposfaltareposicao;
    @Basic
    @Column(name = "enviarnfseautomatico", nullable = true)
    private Boolean enviarnfseautomatico;
    @Basic
    @Column(name = "codigocnae", nullable = true, length = -1)
    private String codigocnae;
    @Basic
    @Column(name = "comissaomatricularematricula", nullable = true)
    private Boolean comissaomatricularematricula;
    @Basic
    @Column(name = "questionarioprimeiracompra", nullable = true)
    private Integer questionarioprimeiracompra;
    @Basic
    @Column(name = "nrdiasvigentequestionarioprimeiracompra", nullable = true)
    private Integer nrdiasvigentequestionarioprimeiracompra;
    @Basic
    @Column(name = "questionarioretornocompra", nullable = true)
    private Integer questionarioretornocompra;
    @Basic
    @Column(name = "nrdiasvigentequestionarioretornocompra", nullable = true)
    private Integer nrdiasvigentequestionarioretornocompra;
    @Basic
    @Column(name = "nfseporpagamento", nullable = true)
    private Boolean nfseporpagamento;
    @Basic
    @Column(name = "qtdvias", nullable = true)
    private Integer qtdvias;
    @Basic
    @Column(name = "quebrarpaginarecibo", nullable = true)
    private Boolean quebrarpaginarecibo;
    @Basic
    @Column(name = "detalharperiodoproduto", nullable = true)
    private Boolean detalharperiodoproduto;
    @Basic
    @Column(name = "detalharparcelas", nullable = true)
    private Boolean detalharparcelas;
    @Basic
    @Column(name = "detalharpagamentos", nullable = true)
    private Boolean detalharpagamentos;
    @Basic
    @Column(name = "detalhardescontos", nullable = true)
    private Boolean detalhardescontos;
    @Basic
    @Column(name = "apresentarassinaturas", nullable = true)
    private Boolean apresentarassinaturas;
    @Basic
    @Column(name = "qtddiascobrarrematricula", nullable = true)
    private Integer qtddiascobrarrematricula;
    @Basic
    @Column(name = "cpfnfse", nullable = true)
    private Boolean cpfnfse;
    @Basic
    @Column(name = "emailnfse", nullable = true)
    private Boolean emailnfse;
    @Basic
    @Column(name = "endereconfse", nullable = true)
    private Boolean endereconfse;
    @Basic
    @Column(name = "tipogestaonfse", nullable = true)
    private Integer tipogestaonfse;
    @Basic
    @Column(name = "retrocedervalormensalplanocancelamento", nullable = true)
    private Boolean retrocedervalormensalplanocancelamento;
    @Basic
    @Column(name = "fecharnegociacaosemautorizacaodcc", nullable = true)
    private Boolean fecharnegociacaosemautorizacaodcc;
    @Basic
    @Column(name = "liberarpersonalcomtaxaemaberto", nullable = true)
    private Boolean liberarpersonalcomtaxaemaberto;
    @Basic
    @Column(name = "reciboparaimpressoratermica", nullable = true)
    private Boolean reciboparaimpressoratermica;
    @Basic
    @Column(name = "fotoredesocial", nullable = true)
    private byte[] fotoredesocial;
    @Basic
    @Column(name = "alturafotoredesocial", nullable = true, length = 10)
    private String alturafotoredesocial;
    @Basic
    @Column(name = "largurafotoredesocial", nullable = true, length = 10)
    private String largurafotoredesocial;
    @Basic
    @Column(name = "usarmanutencaomodalidadecomissao", nullable = true)
    private Boolean usarmanutencaomodalidadecomissao;
    @Basic
    @Column(name = "observacaorecibo", nullable = true, length = -1)
    private String observacaorecibo;
    @Basic
    @Column(name = "nrdiasdesistenteremovervinculotreino", nullable = true)
    private Integer nrdiasdesistenteremovervinculotreino;
    @Basic
    @Column(name = "removervinculosaposdesistencia", nullable = true)
    private Boolean removervinculosaposdesistencia;
    @Basic
    @Column(name = "creditodcc", nullable = true)
    private Integer creditodcc;
    @Basic
    @Column(name = "devolucaoentranocaixa", nullable = true)
    private Boolean devolucaoentranocaixa;
    @Basic
    @Column(name = "arredondamento", nullable = true)
    private Integer arredondamento;
    @Basic
    @Column(name = "toleranciaprorata", nullable = true)
    private Short toleranciaprorata;
    @Basic
    @Column(name = "serierps", nullable = true, length = 10)
    private String serierps;
    @Basic
    @Column(name = "exigibilidadeiss", nullable = true)
    private Integer exigibilidadeiss;
    @Basic
    @Column(name = "permitereposicaoemturmasdiferentes", nullable = true)
    private Boolean permitereposicaoemturmasdiferentes;
    @Basic
    @Column(name = "usargestaocreditospersonal", nullable = true)
    private Boolean usargestaocreditospersonal;
    @Basic
    @Column(name = "forcarminimovencimento2parcela", nullable = true)
    private Boolean forcarminimovencimento2Parcela;
    @Basic
    @Column(name = "homebackground640x551", nullable = true)
    private byte[] homebackground640X551;
    @Basic
    @Column(name = "homebackground320x276", nullable = true)
    private byte[] homebackground320X276;
    @Basic
    @Column(name = "alturahomebackground640x551", nullable = true, length = 10)
    private String alturahomebackground640X551;
    @Basic
    @Column(name = "largurahomebackground640x551", nullable = true, length = 10)
    private String largurahomebackground640X551;
    @Basic
    @Column(name = "alturahomebackground320x276", nullable = true, length = 10)
    private String alturahomebackground320X276;
    @Basic
    @Column(name = "largurahomebackground320x276", nullable = true, length = 10)
    private String largurahomebackground320X276;
    @Basic
    @Column(name = "permiterenovarcontratosemturmaslotadas", nullable = true)
    private Boolean permiterenovarcontratosemturmaslotadas;
    @Basic
    @Column(name = "dataexpiracaocreditodcc", nullable = true)
    private Date dataexpiracaocreditodcc;
    @Basic
    @Column(name = "diasrenovacaoautomaticaantecipada", nullable = true)
    private Integer diasrenovacaoautomaticaantecipada;
    @Basic
    @Column(name = "observacaonfse", nullable = true, length = 2000)
    private String observacaonfse;
    @Basic
    @Column(name = "enviarobservacaonfsenadescricao", nullable = true)
    private Boolean enviarobservacaonfsenadescricao;
    @Basic
    @Column(name = "mostrarmensagemvaloresrodape", nullable = true)
    private Boolean mostrarmensagemvaloresrodape;
    @Basic
    @Column(name = "latitude", nullable = true, length = 32)
    private String latitude;
    @Basic
    @Column(name = "longitude", nullable = true, length = 32)
    private String longitude;
    @Basic
    @Column(name = "acessochamada", nullable = true)
    private Boolean acessochamada;
    @Basic
    @Column(name = "localacessochamada", nullable = true)
    private Integer localacessochamada;
    @Basic
    @Column(name = "coletorchamada", nullable = true)
    private Integer coletorchamada;
    @Basic
    @Column(name = "consultorsite", nullable = true)
    private Integer consultorsite;
    @Basic
    @Column(name = "criarbvvendasite", nullable = true)
    private Boolean criarbvvendasite;
    @Basic
    @Column(name = "liberarpersonalprofessordebito", nullable = true)
    private Boolean liberarpersonalprofessordebito;
    @Basic
    @Column(name = "produtoconvite", nullable = true)
    private Integer produtoconvite;
    @Basic
    @Column(name = "qtdconviteano", nullable = true)
    private Integer qtdconviteano;
    @Basic
    @Column(name = "diaparagerartaxas", nullable = true)
    private Integer diaparagerartaxas;
    @Basic
    @Column(name = "diasvalidadecarteira", nullable = true)
    private Integer diasvalidadecarteira;
    @Basic
    @Column(name = "produtoexamemedico", nullable = true)
    private Integer produtoexamemedico;
    @Basic
    @Column(name = "produtocarteirinha", nullable = true)
    private Integer produtocarteirinha;
    @Basic
    @Column(name = "validademesescarteirinhasocio", nullable = true)
    private Integer validademesescarteirinhasocio;
    @Basic
    @Column(name = "validademesescarteirinhanaosocio", nullable = true)
    private Integer validademesescarteirinhanaosocio;
    @Basic
    @Column(name = "diasdevencimento", nullable = true, length = 80)
    private String diasdevencimento;
    @Basic
    @Column(name = "convenioboletopadrao", nullable = true)
    private Integer convenioboletopadrao;
    @Basic
    @Column(name = "quantidademaxacompanhante", nullable = true)
    private Short quantidademaxacompanhante;
    @Basic
    @Column(name = "cobrarautomaticamentemultajuros", nullable = true)
    private Boolean cobrarautomaticamentemultajuros;
    @Basic
    @Column(name = "emitenfsepordatacompensacao", nullable = true)
    private Boolean emitenfsepordatacompensacao;
    @Basic
    @Column(name = "naocobrarproporcionalatedia", nullable = true)
    private Integer naocobrarproporcionalatedia;
    @Basic
    @Column(name = "permitealterardataemissaonfse", nullable = true)
    private Boolean permitealterardataemissaonfse;
    @Basic
    @Column(name = "tipoprodutoemissaonfse", nullable = true, length = 120)
    private String tipoprodutoemissaonfse;
    @Basic
    @Column(name = "manutencaomodalidadepormes", nullable = true)
    private Boolean manutencaomodalidadepormes;
    @Basic
    @Column(name = "modelomensagemvendasonline", nullable = true)
    private Integer modelomensagemvendasonline;
    @Basic
    @Column(name = "minutosaposultimoacessodiminuircredito", nullable = true)
    private Short minutosaposultimoacessodiminuircredito;
    @Basic
    @Column(name = "somentevendaprodutoscomestoque", nullable = true)
    private Boolean somentevendaprodutoscomestoque;
    @Basic
    @Column(name = "ativa", nullable = true)
    private Boolean ativa;
    @Basic
    @Column(name = "pagarcomissaomanutencaomodalidade", nullable = true)
    private Boolean pagarcomissaomanutencaomodalidade;
    @Basic
    @Column(name = "pagarcomissaoseatingirmetafinanceira", nullable = true)
    private Boolean pagarcomissaoseatingirmetafinanceira;
    @Basic
    @Column(name = "permitecontratopospagorenovacaoauto", nullable = true)
    private Boolean permitecontratopospagorenovacaoauto;
    @Basic
    @Column(name = "tentativasliberarparcelavencida", nullable = true)
    private Integer tentativasliberarparcelavencida;
    @Basic
    @Column(name = "tipocobrancapacto", nullable = true)
    private Integer tipocobrancapacto;
    @Basic
    @Column(name = "gerarcobrancaautomaticapacto", nullable = true)
    private Boolean gerarcobrancaautomaticapacto;
    @Basic
    @Column(name = "dtultimacobrancapacto", nullable = true)
    private Date dtultimacobrancapacto;
    @Basic
    @Column(name = "qtddiasfechamentocobrancapacto", nullable = true)
    private Integer qtddiasfechamentocobrancapacto;
    @Basic
    @Column(name = "valorcreditopacto", nullable = true, precision = 0)
    private Double valorcreditopacto;
    @Basic
    @Column(name = "gerarnotafiscalcobrancapacto", nullable = true)
    private Boolean gerarnotafiscalcobrancapacto;
    @Basic
    @Column(name = "qtdparcelascobrancapacto", nullable = true)
    private Integer qtdparcelascobrancapacto;
    @Basic
    @Column(name = "qtdcreditorenovarprepagocobrancapacto", nullable = true)
    private Integer qtdcreditorenovarprepagocobrancapacto;
    @Basic
    @Column(name = "propagandaboleto", nullable = true)
    private byte[] propagandaboleto;
    @Basic
    @Column(name = "alturapropagandaboleto", nullable = true, length = 10)
    private String alturapropagandaboleto;
    @Basic
    @Column(name = "largurapropagandaboleto", nullable = true, length = 10)
    private String largurapropagandaboleto;
    @Basic
    @Column(name = "qtddiasvencimentoboleto", nullable = true)
    private Integer qtddiasvencimentoboleto;
    @Basic
    @Column(name = "tipoparcelascobrarvendasite", nullable = true)
    private Integer tipoparcelascobrarvendasite;
    @Basic
    @Column(name = "existemnovosboletospacto", nullable = true)
    private Boolean existemnovosboletospacto;
    @Basic
    @Column(name = "gerarloginapiaoincluircontrato", nullable = true)
    private Boolean gerarloginapiaoincluircontrato;
    @Basic
    @Column(name = "modelomensagemesqueciminhasenhavendasonline", nullable = true)
    private Integer modelomensagemesqueciminhasenhavendasonline;
    @Basic
    @Column(name = "quantidadeparcelasseguidascancelamento", nullable = true)
    private Integer quantidadeparcelasseguidascancelamento;
    @Basic
    @Column(name = "tipoparcelacancelamento", nullable = true, length = 10)
    private String tipoparcelacancelamento;
    @Basic
    @Column(name = "enviarnotacidadeempresa", nullable = true)
    private Boolean enviarnotacidadeempresa;
    @Basic
    @Column(name = "usardatainiciodecontratonobi_icv", nullable = true)
    private Boolean usardatainiciodecontratonobiIcv;
    @Basic
    @Column(name = "qtdparcelasdecimaterceiramanutencao", nullable = true)
    private Integer qtdparcelasdecimaterceiramanutencao;
    @Basic
    @Column(name = "mesiniciodecimoterceiroproporcional", nullable = true)
    private Integer mesiniciodecimoterceiroproporcional;
    @Basic
    @Column(name = "cancelamentoantecipado", nullable = true)
    private Boolean cancelamentoantecipado;
    @Basic
    @Column(name = "cancelamentoantecipadodias", nullable = true)
    private Integer cancelamentoantecipadodias;
    @Basic
    @Column(name = "cancelamentoantecipadoplanosdata", nullable = true)
    private Date cancelamentoantecipadoplanosdata;
    @Basic
    @Column(name = "enviaremailcancelamento", nullable = true)
    private Boolean enviaremailcancelamento;
    @Basic
    @Column(name = "qtddiasparaliberacaodevagaemtrancamento", nullable = true)
    private Integer qtddiasparaliberacaodevagaemtrancamento;
    @Basic
    @Column(name = "permitirmaillinggerarautorizacaocobrancaboleto", nullable = true)
    private Boolean permitirmaillinggerarautorizacaocobrancaboleto;
    @Basic
    @Column(name = "gerarnfsecontacorrente", nullable = true)
    private Boolean gerarnfsecontacorrente;
    @Basic
    @Column(name = "permitegerararquivoloterps", nullable = true)
    private Boolean permitegerararquivoloterps;
    @Basic
    @Column(name = "inscmunicipal", nullable = true, length = 50)
    private String inscmunicipal;
    @Basic
    @Column(name = "sequencialloterps", nullable = true)
    private Integer sequencialloterps;
    @Basic
    @Column(name = "permitegerarnotamanual", nullable = true)
    private Boolean permitegerarnotamanual;
    @Basic
    @Column(name = "impedirvendacontratoporconflitoreposicao", nullable = true)
    private Boolean impedirvendacontratoporconflitoreposicao;
    @Basic
    @Column(name = "usarnfce", nullable = true)
    private Boolean usarnfce;
    @Basic
    @Column(name = "codigonfcecnae", nullable = true, length = 50)
    private String codigonfcecnae;
    @Basic
    @Column(name = "usarnomeresponsavelnfce", nullable = true)
    private Boolean usarnomeresponsavelnfce;
    @Basic
    @Column(name = "enviarnfcecidadeempresa", nullable = true)
    private Boolean enviarnfcecidadeempresa;
    @Basic
    @Column(name = "utilizarnomeresponsavelnoboleto", nullable = true)
    private Boolean utilizarnomeresponsavelnoboleto;
    @Basic
    @Column(name = "gerarquitacaocancelamentoauto", nullable = true)
    private Boolean gerarquitacaocancelamentoauto;
    @Basic
    @Column(name = "dataexpiracaoappgestor", nullable = true)
    private Timestamp dataexpiracaoappgestor;
    @Basic
    @Column(name = "periodotesteappgestor", nullable = true)
    private Boolean periodotesteappgestor;
    @Basic
    @Column(name = "pagarcomissaoproduto", nullable = true)
    private Boolean pagarcomissaoproduto;
    @Basic
    @Column(name = "naturezaoperacaorps", nullable = true, length = 255)
    private String naturezaoperacaorps;
    @Basic
    @Column(name = "naturezaoperacaonfce", nullable = true, length = 255)
    private String naturezaoperacaonfce;
    @Basic
    @Column(name = "alterardatahoracheckgestaopersonal", nullable = true)
    private Boolean alterardatahoracheckgestaopersonal;
    @Basic
    @Column(name = "senhaacessoonzedigitos", nullable = true)
    private Boolean senhaacessoonzedigitos;
    @Basic
    @Column(name = "naorenovarcontratosemindicefinanceiro", nullable = true)
    private Boolean naorenovarcontratosemindicefinanceiro;
    @Basic
    @Column(name = "codigogympass", nullable = true, length = -1)
    private String codigogympass;
    @Basic
    @Column(name = "tokenapigympass", nullable = true, length = -1)
    private String tokenapigympass;
    @Basic
    @Column(name = "gerarremessacontratocancelado", nullable = true)
    private Boolean gerarremessacontratocancelado;
    @Basic
    @Column(name = "habilitarsomadeaulanaovigente", nullable = true)
    private Boolean habilitarsomadeaulanaovigente;
    @Basic
    @Column(name = "codigochaveintegracaodigitais", nullable = true)
    private Integer codigochaveintegracaodigitais;
    @Basic
    @Column(name = "definircpfcomosenhacatraca", nullable = true)
    private Boolean definircpfcomosenhacatraca;
    @Basic
    @Column(name = "bloqueiotemporario", nullable = true)
    private Boolean bloqueiotemporario;
    @Basic
    @Column(name = "mostrarnotapordiacompetencia", nullable = true)
    private Boolean mostrarnotapordiacompetencia;
    @Basic
    @Column(name = "pontosalunoacesso", nullable = true)
    private Integer pontosalunoacesso;
    @Basic
    @Column(name = "trabalharcompontuacao", nullable = true)
    private Boolean trabalharcompontuacao;
    @Basic
    @Column(name = "observacaonfce", nullable = true, length = -1)
    private String observacaonfce;
    @Basic
    @Column(name = "retiraredicaopagamento", nullable = true)
    private Boolean retiraredicaopagamento;
    @Basic
    @Column(name = "adicionaraulasdesmarcadascontratoanterior", nullable = true)
    private Boolean adicionaraulasdesmarcadascontratoanterior;
    @Basic
    @Column(name = "conveniocobrancaprivatelabel", nullable = true)
    private Integer conveniocobrancaprivatelabel;
    @Basic
    @Column(name = "tipoparcelascobrarprivatelabel", nullable = true)
    private Integer tipoparcelascobrarprivatelabel;
    @Basic
    @Column(name = "quantidadeparcelasseguidascancelamentoforaregimerecorrencia", nullable = true)
    private Integer quantidadeparcelasseguidascancelamentoforaregimerecorrencia;
    @Basic
    @Column(name = "tipoparcelacancelamentoforaregimerecorrencia", nullable = true, length = 10)
    private String tipoparcelacancelamentoforaregimerecorrencia;
    @Basic
    @Column(name = "limiteinicialitensbipendencia", nullable = true)
    private Date limiteinicialitensbipendencia;
    @Basic
    @Column(name = "temposaidaacademia", nullable = true)
    private Integer temposaidaacademia;
    @Basic
    @Column(name = "tokensmsshortcode", nullable = true, length = -1)
    private String tokensmsshortcode;
    @Basic
    @Column(name = "permiterenovarcontratoviaapp", nullable = true)
    private Boolean permiterenovarcontratoviaapp;
    @Basic
    @Column(name = "valormensalemitirnfse", nullable = true, precision = 0)
    private Double valormensalemitirnfse;
    @Basic
    @Column(name = "habilitarreenvioautomaticoremessa", nullable = true)
    private Boolean habilitarreenvioautomaticoremessa;
    @Basic
    @Column(name = "emitirnotasomenterecorrencia", nullable = true)
    private Boolean emitirnotasomenterecorrencia;
    @Basic
    @Column(name = "emitirnomealunonotafamilia", nullable = true)
    private Boolean emitirnomealunonotafamilia;
    @Basic
    @Column(name = "cancelamentoantecipadocontratosdepoisde", nullable = true)
    private Date cancelamentoantecipadocontratosdepoisde;
    @Basic
    @Column(name = "irtelapagamentocartaocreditorecorrente", nullable = true)
    private Boolean irtelapagamentocartaocreditorecorrente;
    @Basic
    @Column(name = "enviarnfceautomatico", nullable = true)
    private Boolean enviarnfceautomatico;
    @Basic
    @Column(name = "emitirnfcesomenterecorrencia", nullable = true)
    private Boolean emitirnfcesomenterecorrencia;
    @Basic
    @Column(name = "emitirmesreferencianfce", nullable = true)
    private Boolean emitirmesreferencianfce;
    @Basic
    @Column(name = "usaintegracoescrm", nullable = true)
    private Boolean usaintegracoescrm;
    @Basic
    @Column(name = "tokenbuzzlead", nullable = true, length = 255)
    private String tokenbuzzlead;
    @Basic
    @Column(name = "urllinksitecadastro", nullable = true, length = 255)
    private String urllinksitecadastro;
    @Basic
    @Column(name = "enderecoobrigatorionfce", nullable = true)
    private Boolean enderecoobrigatorionfce;
    @Basic
    @Column(name = "cpfobrigatorionfce", nullable = true)
    private Boolean cpfobrigatorionfce;
    @Basic
    @Column(name = "tipoprodutoemissaonfce", nullable = true, length = 500)
    private String tipoprodutoemissaonfce;
    @Basic
    @Column(name = "usardataoriginalcompensacaonfse", nullable = true)
    private Boolean usardataoriginalcompensacaonfse;
    @Basic
    @Column(name = "utilizasistemaestacionamento", nullable = true)
    private Boolean utilizasistemaestacionamento;
    @Basic
    @Column(name = "produtoemissaonfcefinanceiro", nullable = true)
    private Integer produtoemissaonfcefinanceiro;
    @Basic
    @Column(name = "permitirlancarvariasparcelassaldodevedor", nullable = true)
    private Boolean permitirlancarvariasparcelassaldodevedor;
    @Basic
    @Column(name = "usarnfceporpagamento", nullable = true)
    private Boolean usarnfceporpagamento;
    @Basic
    @Column(name = "usardescricaoformapagamentonfce", nullable = true)
    private Boolean usardescricaoformapagamentonfce;
    @Basic
    @Column(name = "usardescricaoparcelanfse", nullable = true)
    private Boolean usardescricaoparcelanfse;
    @Basic
    @Column(name = "apresentarduracaoplanonfse", nullable = true)
    private Boolean apresentarduracaoplanonfse;
    @Basic
    @Column(name = "qtdexecucoesretentativa", nullable = true)
    private Integer qtdexecucoesretentativa;
    @Basic
    @Column(name = "consultardiasanterioresnfse", nullable = true)
    private Boolean consultardiasanterioresnfse;
    @Basic
    @Column(name = "consultardiasanterioresnfce", nullable = true)
    private Boolean consultardiasanterioresnfce;
    @Basic
    @Column(name = "notasautopgretroativo", nullable = true)
    private Boolean notasautopgretroativo;
    @Basic
    @Column(name = "formaspagamentonfce", nullable = true, length = 50)
    private String formaspagamentonfce;
    @Basic
    @Column(name = "formaspagamentonfse", nullable = true, length = 50)
    private String formaspagamentonfse;
    @Basic
    @Column(name = "pontosalunoacessochuva", nullable = true)
    private Integer pontosalunoacessochuva;
    @Basic
    @Column(name = "pontosalunoacessofrio", nullable = true)
    private Integer pontosalunoacessofrio;
    @Basic
    @Column(name = "pontosalunoacessocalor", nullable = true)
    private Integer pontosalunoacessocalor;
    @Basic
    @Column(name = "validarcertificado", nullable = true)
    private Boolean validarcertificado;
    @Basic
    @Column(name = "utilizarjurosvalorabsoluto", nullable = true)
    private Boolean utilizarjurosvalorabsoluto;
    @Basic
    @Column(name = "usarparceirofidelidade", nullable = true)
    private Boolean usarparceirofidelidade;
    @Basic
    @Column(name = "emailnotificacaovendasonline", nullable = true, length = -1)
    private String emailnotificacaovendasonline;
    @Basic
    @Column(name = "concessao_dia_extra", nullable = true)
    private Date concessaoDiaExtra;
    @Basic
    @Column(name = "email_movidesk", nullable = true, length = 255)
    private String emailMovidesk;
    @Basic
    @Column(name = "sincronizado_movidesk", nullable = true)
    private Boolean sincronizadoMovidesk;
    @Basic
    @Column(name = "envionotificacaonotasnfse", nullable = true)
    private Boolean envionotificacaonotasnfse;
    @Basic
    @Column(name = "envionotificacaonotasnfce", nullable = true)
    private Boolean envionotificacaonotasnfce;
    @Basic
    @Column(name = "emailsnotificacaoautomaticanotas", nullable = true, length = -1)
    private String emailsnotificacaoautomaticanotas;
    @Basic
    @Column(name = "emimportacao", nullable = true)
    private Boolean emimportacao;
    @Basic
    @Column(name = "usarconciliadora", nullable = true)
    private Boolean usarconciliadora;
    @Basic
    @Column(name = "empresaconciliadora", nullable = true, length = 20)
    private String empresaconciliadora;
    @Basic
    @Column(name = "senhaconciliadora", nullable = true, length = 20)
    private String senhaconciliadora;
    @Basic
    @Column(name = "permmarcaraulaferiado", nullable = true)
    private Boolean permmarcaraulaferiado;
    @Basic
    @Column(name = "horaaberturaferiado", nullable = true, length = 10)
    private String horaaberturaferiado;
    @Basic
    @Column(name = "horafechamentoferiado", nullable = true, length = 10)
    private String horafechamentoferiado;
    @Basic
    @Column(name = "bloquearacessoarmariovigenciavencida", nullable = true)
    private Boolean bloquearacessoarmariovigenciavencida;
    @Basic
    @Column(name = "moeda", nullable = true, length = 5)
    private String moeda;
    @Basic
    @Column(name = "locale", nullable = true, length = 5)
    private String locale;
    @Basic
    @Column(name = "configuracaonotafiscalnfse", nullable = true)
    private Integer configuracaonotafiscalnfse;
    @Basic
    @Column(name = "configuracaonotafiscalnfce", nullable = true)
    private Integer configuracaonotafiscalnfce;
    @Basic
    @Column(name = "permitirestornarcontratocomparcelaspg", nullable = true)
    private Boolean permitirestornarcontratocomparcelaspg;
    @Basic
    @Column(name = "usardigitalcomoassinatura", nullable = true)
    private Boolean usardigitalcomoassinatura;
    @Basic
    @Column(name = "cobrarmultajurostransacao", nullable = true)
    private Boolean cobrarmultajurostransacao;
    @Basic
    @Column(name = "cobrarmultajurosdco", nullable = true)
    private Boolean cobrarmultajurosdco;
    @Basic
    @Column(name = "cobrarmultajurosdcc", nullable = true)
    private Boolean cobrarmultajurosdcc;
    @Basic
    @Column(name = "addautoclientetreinoweb", nullable = true)
    private Boolean addautoclientetreinoweb;
    @Basic
    @Column(name = "validarvencimentocartaoautorizacao", nullable = true)
    private Boolean validarvencimentocartaoautorizacao;
    @Basic
    @Column(name = "diaspararetirarrelfechamentodecaixa", nullable = true)
    private Integer diaspararetirarrelfechamentodecaixa;
    @Basic
    @Column(name = "considerarsomenteparcelasplanos", nullable = true)
    private Boolean considerarsomenteparcelasplanos;
    @Basic
    @Column(name = "idexterno", nullable = true, length = 12)
    private String idexterno;
    @Basic
    @Column(name = "descmoeda", nullable = true, length = 50)
    private String descmoeda;
    @Basic
    @Column(name = "mincreditarproximopontoclubevantagens", nullable = true)
    private Integer mincreditarproximopontoclubevantagens;
    @Basic
    @Column(name = "apenasprimeiroacessoclubevantagens", nullable = true)
    private Boolean apenasprimeiroacessoclubevantagens;
    @Basic
    @Column(name = "pontuarapenascampanhasativas", nullable = true)
    private Boolean pontuarapenascampanhasativas;
    @Basic
    @Column(name = "aplicarindicacaoqlqrplano", nullable = true)
    private Boolean aplicarindicacaoqlqrplano;
    @Basic
    @Column(name = "zerarpontosaposvencimento", nullable = true)
    private Integer zerarpontosaposvencimento;
    @Basic
    @Column(name = "naocobrarmultadecontratorenovado", nullable = true)
    private Boolean naocobrarmultadecontratorenovado;
    @Basic
    @Column(name = "naocobrarmultadetodasparcelaspagas", nullable = true)
    private Boolean naocobrarmultadetodasparcelaspagas;
    @Basic
    @Column(name = "cancelamentoobrigatoriedadepagamento", nullable = true)
    private Boolean cancelamentoobrigatoriedadepagamento;
    @Basic
    @Column(name = "tiposproduto", nullable = true, length = 255)
    private String tiposproduto;
    @Basic
    @Column(name = "emitirduplicatanfse", nullable = true)
    private Boolean emitirduplicatanfse;
    @Basic
    @Column(name = "utilizardatacancelamentovalidarparcela", nullable = true)
    private Boolean utilizardatacancelamentovalidarparcela;
    @Basic
    @Column(name = "integracaospivisourcename", nullable = true, length = -1)
    private String integracaospivisourcename;
    @Basic
    @Column(name = "integracaospivipassword", nullable = true, length = -1)
    private String integracaospivipassword;
    @Basic
    @Column(name = "integracaospivisiteid", nullable = true)
    private Integer integracaospivisiteid;
    @Basic
    @Column(name = "integracaospivihabilitada", nullable = true)
    private Boolean integracaospivihabilitada;
    @Basic
    @Column(name = "gerarquitacaocancelamentoproporcional", nullable = true)
    private Boolean gerarquitacaocancelamentoproporcional;
    @Basic
    @Column(name = "cod_empresafinanceiro", nullable = true)
    private Integer codEmpresafinanceiro;
    @Basic
    @Column(name = "mostrarvaloreszeradosrel", nullable = true)
    private Boolean mostrarvaloreszeradosrel;
    @Basic
    @Column(name = "atualizardadoscadastro", nullable = true)
    private Boolean atualizardadoscadastro;
    @Basic
    @Column(name = "produtoemissaonfsefinanceiro", nullable = true)
    private Integer produtoemissaonfsefinanceiro;
    @Basic
    @Column(name = "zerarvalorcancelamentotransferencia", nullable = true)
    private Boolean zerarvalorcancelamentotransferencia;
    @Basic
    @Column(name = "creditodccbonus", nullable = true)
    private Integer creditodccbonus;
    @Basic
    @Column(name = "bloquearacessosemassinaturadigital", nullable = true)
    private Boolean bloquearacessosemassinaturadigital;
    @Basic
    @Column(name = "cobrarcreditovindi", nullable = true)
    private Boolean cobrarcreditovindi;
    @Basic
    @Column(name = "sincronizacaofinanceiro", nullable = true)
    private Timestamp sincronizacaofinanceiro;
    @Basic
    @Column(name = "qtddiaslimitecobrancaparcelasrecorrencia", nullable = true)
    private Integer qtddiaslimitecobrancaparcelasrecorrencia;
    @Basic
    @Column(name = "qtddiasrepetircobrancaparcelasrecorrencia", nullable = true)
    private Integer qtddiasrepetircobrancaparcelasrecorrencia;
    @Basic
    @Column(name = "tentativaunicadecobranca", nullable = true)
    private Boolean tentativaunicadecobranca;
    @Basic
    @Column(name = "dias_extras", nullable = true)
    private Integer diasExtras;
    @Basic
    @Column(name = "mostrardescricaoparcelarenegociada", nullable = true)
    private Boolean mostrardescricaoparcelarenegociada;
    @Basic
    @Column(name = "total_dias_solicitados", nullable = true)
    private Integer totalDiasSolicitados;
    @Basic
    @Column(name = "acessosomentecomagendamento", nullable = true)
    private Boolean acessosomentecomagendamento;
    @Basic
    @Column(name = "capacidadesimultanea", nullable = true)
    private Integer capacidadesimultanea;
    @Basic
    @Column(name = "datasuspensao", nullable = true)
    private Timestamp datasuspensao;
    @Basic
    @Column(name = "utilizaleitorcodigobarras", nullable = true)
    private Boolean utilizaleitorcodigobarras;
    @Basic
    @Column(name = "nomeusuarioamigofit", nullable = true, length = 50)
    private String nomeusuarioamigofit;
    @Basic
    @Column(name = "senhausuarioamigofit", nullable = true, length = 50)
    private String senhausuarioamigofit;
    @Basic
    @Column(name = "integracaomywellnessuser", nullable = true, length = -1)
    private String integracaomywellnessuser;
    @Basic
    @Column(name = "integracaomywellnesspassword", nullable = true, length = -1)
    private String integracaomywellnesspassword;
    @Basic
    @Column(name = "integracamywellneapikey", nullable = true, length = -1)
    private String integracamywellneapikey;
    @Basic
    @Column(name = "integracaomywellnehabilitada", nullable = true)
    private Boolean integracaomywellnehabilitada;
    @Basic
    @Column(name = "integracaomywellnessfacilityurl", nullable = true, length = -1)
    private String integracaomywellnessfacilityurl;
    @Basic
    @Column(name = "somenteumenviocartaotentativa", nullable = true)
    private Boolean somenteumenviocartaotentativa;
    @Basic
    @Column(name = "agruparparcelasporcartao", nullable = true)
    private Boolean agruparparcelasporcartao;
    @Basic
    @Column(name = "agruparparcelasporcartaovalorlimite", nullable = true, precision = 0)
    private Double agruparparcelasporcartaovalorlimite;
    @Basic
    @Column(name = "integracaomywellnessenviarvinculos", nullable = true)
    private Boolean integracaomywellnessenviarvinculos;
    @Basic
    @Column(name = "integracaomywellnessenviargrupos", nullable = true)
    private Boolean integracaomywellnessenviargrupos;
    @Basic
    @Column(name = "diavencimentocobrancapacto", nullable = true)
    private Integer diavencimentocobrancapacto;
    @Basic
    @Column(name = "empresaresponsavelcobrancapacto", nullable = true)
    private Boolean empresaresponsavelcobrancapacto;
    @Basic
    @Column(name = "dataexpiracaonfe", nullable = true)
    private Timestamp dataexpiracaonfe;
    @Basic
    @Column(name = "dataexpiracaovendasonline", nullable = true)
    private Timestamp dataexpiracaovendasonline;
    @Basic
    @Column(name = "dataexpiracaoapp", nullable = true)
    private Timestamp dataexpiracaoapp;
    @Basic
    @Column(name = "valorlimitecaixaabertovendaavulsa", nullable = true, precision = 0)
    private Double valorlimitecaixaabertovendaavulsa;
    @Basic
    @Column(name = "detalharniveismodalidades", nullable = true)
    private Boolean detalharniveismodalidades;
    @Basic
    @Column(name = "notificarwebhook", nullable = true)
    private Boolean notificarwebhook;
    @Basic
    @Column(name = "urlwebhooknotificar", nullable = true, length = 250)
    private String urlwebhooknotificar;
    @Basic
    @Column(name = "integracaomentorwebhabilitada", nullable = true)
    private Boolean integracaomentorwebhabilitada;
    @Basic
    @Column(name = "integracaomentorweburl", nullable = true, length = 255)
    private String integracaomentorweburl;
    @Basic
    @Column(name = "integracaomentorwebservico", nullable = true, length = 255)
    private String integracaomentorwebservico;
    @Basic
    @Column(name = "integracaomentorwebuser", nullable = true, length = 255)
    private String integracaomentorwebuser;
    @Basic
    @Column(name = "integracaomentorwebpassword", nullable = true, length = 255)
    private String integracaomentorwebpassword;
    @Basic
    @Column(name = "tipovigenciamywellnessgympass", nullable = true)
    private Integer tipovigenciamywellnessgympass;
    @Basic
    @Column(name = "nrdiasvigenciamywellnessgympass", nullable = true)
    private Integer nrdiasvigenciamywellnessgympass;
    @Basic
    @Column(name = "integracaoamigofithabilitada", nullable = true)
    private Boolean integracaoamigofithabilitada;
    @Basic
    @Column(name = "permitecadastrarcartaomesmoassim", nullable = true)
    private Boolean permitecadastrarcartaomesmoassim;
    @Basic
    @Column(name = "convenioverificacaocartao", nullable = true)
    private Integer convenioverificacaocartao;
    @Basic
    @Column(name = "transferida", nullable = true)
    private Boolean transferida;
    @Basic
    @Column(name = "novachave_transferencia", nullable = true, length = -1)
    private String novachaveTransferencia;
    @Basic
    @Column(name = "novocodigo_transferencia", nullable = true)
    private Integer novocodigoTransferencia;
    @Basic
    @Column(name = "utilizarmultavalorabsoluto", nullable = true)
    private Boolean utilizarmultavalorabsoluto;
    @Basic
    @Column(name = "toleranciaacessoaula", nullable = true)
    private Integer toleranciaacessoaula;
    @Basic
    @Column(name = "permitemaillingcriarboleto", nullable = true)
    private Boolean permitemaillingcriarboleto;
    @Basic
    @Column(name = "conveniocobrancapix", nullable = true)
    private Integer conveniocobrancapix;
    @Basic
    @Column(name = "bloquearsemcartaovacina", nullable = true)
    private Boolean bloquearsemcartaovacina;
    @Basic
    @Column(name = "idademinimacartaovacina", nullable = true)
    private Integer idademinimacartaovacina;
    @Basic
    @Column(name = "tipoanexocartaovacina", nullable = true)
    private Integer tipoanexocartaovacina;
    @Basic
    @Column(name = "tipoempresafinanceiro", nullable = true, length = -1)
    private String tipoempresafinanceiro;
    @Basic
    @Column(name = "datacadastrofinanceiro", nullable = true)
    private Timestamp datacadastrofinanceiro;
    @Basic
    @Column(name = "datadesativacaofinanceiro", nullable = true)
    private Timestamp datadesativacaofinanceiro;
    @Basic
    @Column(name = "enviaremailpagamento", nullable = true)
    private Boolean enviaremailpagamento;
    @Basic
    @Column(name = "cobrarmultajurospix", nullable = true)
    private Boolean cobrarmultajurospix;
    @Basic
    @Column(name = "tokenmqv", nullable = true, length = 500)
    private String tokenmqv;
    @Basic
    @Column(name = "restringirconvidadoumavezpormes", nullable = true)
    private Boolean restringirconvidadoumavezpormes;
    @Basic
    @Column(name = "produtodayuse", nullable = true)
    private Integer produtodayuse;
    @Basic
    @Column(name = "modalidadedayuse", nullable = true)
    private Integer modalidadedayuse;
    @Basic
    @Column(name = "produtodiaplus", nullable = true)
    private Integer produtodiaplus;

}
