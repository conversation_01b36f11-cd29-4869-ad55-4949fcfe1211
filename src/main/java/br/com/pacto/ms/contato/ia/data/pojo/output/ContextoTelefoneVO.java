package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ContextoTelefoneVO {

    @Schema(description = "Tipo de telefone, não nulo, com no máximo 20 caracteres.")
    private String tipotelefone;
    @Schema(description = "Número do telefone, não nulo, com no máximo 20 caracteres.")
    private String numero;
    @Schema(description = "Descrição para identificação do telefone.")
    private String descricao;
}
