package br.com.pacto.ms.contato.ia.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Data
@Builder
public class ContextoAcessoClienteVO {
    @Schema(description = "Data e hora de entrada.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date dataHoraEntrada;

    @Schema(description = "Data e hora de saida.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date dataHoraSaida;
}

