package br.com.pacto.ms.contato.ia.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.ia.data.pojo.output.IAConversaServiceVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;

import java.util.List;

public interface IAConversaService {

    void mandarMensagem(ConfiguracaoCrmFaseIAEntity configuracaoCrmFaseIAEntity, ResumoEmpresaVO resumoEmpresaVO, boolean forcarEnvio);

    ResponsePactoConversasVO<IAConversaServiceVO> inciarConversaPorFase(String chave, List<String> fases, boolean forcarEnvio, Integer empresa);

}
