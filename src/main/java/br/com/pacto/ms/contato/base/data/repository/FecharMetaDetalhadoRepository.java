package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.FecharMetaDetalhadoEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public interface FecharMetaDetalhadoRepository extends PagingAndSortingRepository<FecharMetaDetalhadoEntity, Integer> {

    @Query("SELECT fmd.cliente.codigo FROM FecharMetaDetalhadoEntity fmd " +
            "WHERE fmd.fecharMeta.aberturaMeta.empresa = :empresa " +
            "AND fmd.fecharMeta.identificadorMeta = :sigla " +
            "AND DATE(fmd.fecharMeta.aberturaMeta.dia) = CURRENT_DATE " +
            "AND fmd.cliente.codigo <> NULL ")
    Optional<Page<List<Integer>>> consultarCodigosClientesPorFaseEmpresa(Integer empresa, String sigla, Pageable pageable);

    @Query("SELECT fmd.fecharMeta.identificadorMeta FROM FecharMetaDetalhadoEntity fmd " +
            "WHERE fmd.fecharMeta.aberturaMeta.empresa = :empresa " +
            "AND fmd.cliente.codigo = :cliente " +
            "AND DATE(fmd.fecharMeta.aberturaMeta.dia) = CURRENT_DATE")
    List<String> consultarIdentificadoresMetaPorCliente(Integer empresa, Integer cliente);

    default List<FasesCRMEnum> consultarFasesCliente(Integer empresa, Integer cliente) {
        List<String> identificadorMetas = consultarIdentificadoresMetaPorCliente(empresa, cliente);
        return identificadorMetas.stream()
                .map(FasesCRMEnum::getFasePorSigla)
                .collect(Collectors.toList());
    }

    @Query("SELECT fmd.cliente.codigo FROM FecharMetaDetalhadoEntity fmd " +
            "WHERE fmd.fecharMeta.aberturaMeta.empresa = :empresa " +
            "AND  ((fmd.fecharMeta.identificadorMeta = :sigla  and fmd.fecharMeta.malaDireta.codigo IS NULL) OR (fmd.fecharMeta.identificadorMeta = 'CR' and fmd.fecharMeta.malaDireta.codigo = :codigoMeta )) " +
            "AND DATE(fmd.fecharMeta.aberturaMeta.dia) = CURRENT_DATE " +
            "AND fmd.cliente.codigo <> NULL "
    )
    Optional<Page<Integer>> consultarCodigosClientesPorFaseEmpresa(Integer empresa, String sigla, Integer codigoMeta, Pageable pageable);

    @Query("SELECT fmd.cliente.codigo FROM FecharMetaDetalhadoEntity fmd " +
            "LEFT JOIN AgendaEntity ag on ag.codigo = fmd.codigoOrigem and fmd.origem = 'AGENDA' " +
            "WHERE fmd.fecharMeta.aberturaMeta.empresa = :empresa " +
            "AND  ((fmd.fecharMeta.identificadorMeta = :sigla  and fmd.fecharMeta.malaDireta.codigo IS NULL) OR (fmd.fecharMeta.identificadorMeta = 'CR' and fmd.fecharMeta.malaDireta.codigo = :codigoMeta)) " +
            "AND ag.tipoagendamento != 'LI' " +
            "AND DATE(fmd.fecharMeta.aberturaMeta.dia) = CURRENT_DATE " +
            "AND fmd.cliente.codigo <> NULL ")
    Optional<Page<Integer>> consultarCodigosClientesPorFaseEmpresaForAgendamento(Integer empresa, String sigla, Integer codigoMeta, Pageable pageable);

}
