package br.com.pacto.ms.contato.avulso.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.input.*;

public interface HistoricoContatoService<T> {

	T buscarResumoPorCliente(Integer matricula);

	T bucarPorClienteETipoContato(Integer matricula, String tipoContato);

	T buscarPorCliente(Integer matricula);

	T salvar(HistoricoContatoDTO hcDTO);

	T atualizarObjecao(HistoricoObjecaoDTO dto);

	T salvarObjecao(HistoricoObjecaoDTO dto);

	T atualizarHistoricoComSimplesRegistro(HistoricoContatoDTO dto);

	T criarHistoricoComSimplesRegistro(HistoricoContatoDTO dto);

	T whatsApp (HistoricoContatoDTO dto, Integer matricula);

	T salvarApp ( HistoricoServicosDTO dto) throws Exception;

	T salvarEmail(HistoricoServicosDTO dto);

	T salvarSms(HistoricoServicosDTO dto, int totalCaracteres);

	T possuiSms();

	T salvarAgedamento(AgendaDTO dto);

	T salvarAgendamentoLigacao(AgendaLigacaoDTO dto);
}
