package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.ClienteEntity;
import br.com.pacto.ms.contato.avulso.data.repository.ClienteRepository;
import br.com.pacto.ms.contato.avulso.service.contract.ClienteService;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public  class ClienteServiceImpl<T> implements ClienteService<T> {

    private ClienteRepository repository;

    @Override
    public T salvarObjecao(Integer codigoCliente, Integer objecao) {
        ClienteEntity entity = repository.findById(codigoCliente).orElseThrow(DataNotMatchException::new);
        entity.setObjecao(objecao);
        return (T) repository.save(entity);
    }


    @LogExecution
	public ClienteEntity findByCode(Integer code) {
		return repository.findById(code).orElseThrow(DataNotMatchException::new);
	}
}
