package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Date;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "maladireta", schema = "public")
public class MaladiretaEntity {
    private Timestamp dataenvio;
    private Timestamp datacriacao;
    private String mensagem;
    private String titulo;
    private int remetente;
    private Integer modelomensagem;
    private int codigo;
    private Short meiodeenvio;
    private Integer empresa;
    private Date vigenteate;
    private Integer evento;
    private String sql;
    private Integer tipoagendamento;
    private Boolean excluida;
    private Boolean contatoavulso;
    private String faseenvio;
    private String opcoes;
    private Integer tipopergunta;
    private Integer codaberturameta;
    private Integer diasposvenda;
    private Boolean metaextraindividual;
    private String tipoconsultormetaextraindividual;
    private Integer quantidademinimaacessos;
    private Integer quantidademaximaacessos;
    private Integer intervalodias;
    private Boolean todasempresas;
    private Integer questionario;
    private Integer tipocancelamento;
    private Boolean importarlista;
    private String configs;
    private Boolean enviohabilitado;
    private Boolean smsmarketing;
    private Boolean statusentregabilidade;

    @Basic
    @Column(name = "dataenvio")
    public Timestamp getDataenvio() {
        return dataenvio;
    }


    @Basic
    @Column(name = "datacriacao")
    public Timestamp getDatacriacao() {
        return datacriacao;
    }


    @Basic
    @Column(name = "mensagem")
    public String getMensagem() {
        return mensagem;
    }

    @Basic
    @Column(name = "titulo")
    public String getTitulo() {
        return titulo;
    }


    @Basic
    @Column(name = "remetente")
    public int getRemetente() {
        return remetente;
    }


    @Basic
    @Column(name = "modelomensagem")
    public Integer getModelomensagem() {
        return modelomensagem;
    }


    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo")
    public int getCodigo() {
        return codigo;
    }


    @Basic
    @Column(name = "meiodeenvio")
    public Short getMeiodeenvio() {
        return meiodeenvio;
    }


    @Basic
    @Column(name = "empresa")
    public Integer getEmpresa() {
        return empresa;
    }


    @Basic
    @Column(name = "vigenteate")
    public Date getVigenteate() {
        return vigenteate;
    }


    @Basic
    @Column(name = "evento")
    public Integer getEvento() {
        return evento;
    }


    @Basic
    @Column(name = "sql")
    public String getSql() {
        return sql;
    }


    @Basic
    @Column(name = "tipoagendamento")
    public Integer getTipoagendamento() {
        return tipoagendamento;
    }


    @Basic
    @Column(name = "excluida")
    public Boolean getExcluida() {
        return excluida;
    }


    @Basic
    @Column(name = "contatoavulso")
    public Boolean getContatoavulso() {
        return contatoavulso;
    }


    @Basic
    @Column(name = "faseenvio")
    public String getFaseenvio() {
        return faseenvio;
    }


    @Basic
    @Column(name = "opcoes")
    public String getOpcoes() {
        return opcoes;
    }

    @Basic
    @Column(name = "tipopergunta")
    public Integer getTipopergunta() {
        return tipopergunta;
    }


    @Basic
    @Column(name = "codaberturameta")
    public Integer getCodaberturameta() {
        return codaberturameta;
    }


    @Basic
    @Column(name = "diasposvenda")
    public Integer getDiasposvenda() {
        return diasposvenda;
    }


    @Basic
    @Column(name = "metaextraindividual")
    public Boolean getMetaextraindividual() {
        return metaextraindividual;
    }

    @Basic
    @Column(name = "tipoconsultormetaextraindividual")
    public String getTipoconsultormetaextraindividual() {
        return tipoconsultormetaextraindividual;
    }

    @Basic
    @Column(name = "quantidademinimaacessos")
    public Integer getQuantidademinimaacessos() {
        return quantidademinimaacessos;
    }



    @Basic
    @Column(name = "quantidademaximaacessos")
    public Integer getQuantidademaximaacessos() {
        return quantidademaximaacessos;
    }


    @Basic
    @Column(name = "intervalodias")
    public Integer getIntervalodias() {
        return intervalodias;
    }

    @Basic
    @Column(name = "todasempresas")
    public Boolean getTodasempresas() {
        return todasempresas;
    }

    @Basic
    @Column(name = "questionario")
    public Integer getQuestionario() {
        return questionario;
    }

    @Basic
    @Column(name = "tipocancelamento")
    public Integer getTipocancelamento() {
        return tipocancelamento;
    }


    @Basic
    @Column(name = "importarlista")
    public Boolean getImportarlista() {
        return importarlista;
    }

    @Basic
    @Column(name = "configs")
    public String getConfigs() {
        return configs;
    }

    @Basic
    @Column(name = "enviohabilitado")
    public Boolean getEnviohabilitado() {
        return enviohabilitado;
    }


    @Basic
    @Column(name = "smsmarketing")
    public Boolean getSmsmarketing() {
        return smsmarketing;
    }

    @Basic
    @Column(name = "statusentregabilidade")
    public Boolean getStatusentregabilidade() {
        return statusentregabilidade;
    }



}
