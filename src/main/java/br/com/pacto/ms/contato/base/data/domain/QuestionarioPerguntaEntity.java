package br.com.pacto.ms.contato.base.data.domain;


import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name = "questionariopergunta", schema = "public")
@Data
public class QuestionarioPerguntaEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long codigo;

    @ManyToOne
    @JoinColumn(name = "pergunta", referencedColumnName = "codigo", foreignKey = @ForeignKey(name = "fk_questionariopergunta_pergunta"), nullable = false)
    private PerguntaEntity pergunta;

    private Integer questionario;

    @Column(name = "obrigatoria", columnDefinition = "boolean default false")
    private Boolean obrigatoria;

    @Column(name = "nrquestao")
    private Integer nrQuestao;

}