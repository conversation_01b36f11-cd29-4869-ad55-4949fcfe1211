package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.constraints.PositiveOrZero;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ConfiguracaoSistemaCrmDiasPosVendasDTO {
    @PositiveOrZero
    @Schema(description = "Número de dias", example = "1")
    private Integer nrdia;
    @Schema(description = "Descrição da configuração", example = "VALIDAR LANÇAMENTO DO PROGRAMA DE TREINO")
    private String descricao;
    @Schema(description = "Indica o status sa configuração", example = "true")
    private Boolean ativo;
    @Schema(description = "Responsável pelo contato", example = "RPF")
    private String siglaResponsavelPeloContato;
}
