package br.com.pacto.ms.contato.avulso.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;

public class TermoBloqueadoException extends CustomException {
	private static final long serialVersionUID = 1L;

	public TermoBloqueadoException() {
	}

	public TermoBloqueadoException(ExceptionMessageVO... messagesParamVO) {
		super(messagesParamVO);
	}

	public TermoBloqueadoException(String message) {
		super(message);
	}

	public TermoBloqueadoException(String message, ExceptionMessageVO... messagesParamVO) {
		super(message, messagesParamVO);
	}

	public TermoBloqueadoException(Throwable cause) {
		super(cause);
	}

	public TermoBloqueadoException(Throwable cause, ExceptionMessageVO... messagesParamVO) {
		super(cause, messagesParamVO);
	}

	public TermoBloqueadoException(String message, Throwable cause) {
		super(message, cause);
	}

	public TermoBloqueadoException(String message, Throwable cause, ExceptionMessageVO... messagesParamVO) {
		super(message, cause, messagesParamVO);
	}

}
