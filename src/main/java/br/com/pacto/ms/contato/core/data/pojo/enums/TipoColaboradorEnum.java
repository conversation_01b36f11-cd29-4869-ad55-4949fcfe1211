package br.com.pacto.ms.contato.core.data.pojo.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum TipoColaboradorEnum {
    PROFESSOR("PR", "Professor"),
    PROFESSOR_TREINO("TW", "Professor (TreinoWeb)"),
    PERSONAL_TRAINER("PT", "Personal Trainer"),
    ORIENTADOR("OR", "Orientador"),
    CONSULTOR("CO", "Consultor"),
    PERSONAL_INTERNO("PI", "Personal Interno"),
    PERSONAL_EXTERNO("PE", "Personal Externo"),
    TERCEIRIZADO("TE", "Terceirizado"),
    ESTUDIO("ES", "Estúdio"),
    FORNECEDOR("FO", "Fornecedor"),
    COORDENADOR("CR", "Coordenador"),
    MEDICO("MD", "Médico"),
    FUNCIONARIO("FC", "Funcionário"),
    ADMINISTRADOR("AD", "Administrador"),
    RESPONSAVEIS_PELA_FASE("RPF", "Responsáveis pelas fases");

    private String sigla;
    private String descricao;

    public String getName() {
        return this.name();
    }

    public static TipoColaboradorEnum getTipo(String sigla) {
        TipoColaboradorEnum tipo = null;
        for (TipoColaboradorEnum tipoColaborador : TipoColaboradorEnum.values()) {
            if (tipoColaborador.getSigla().equals(sigla)) {
                tipo = tipoColaborador;
                break;
            }
        }
        return tipo;
    }

    @JsonCreator
    public static TipoColaboradorEnum fromJson(Map<String, Object> object) {
        String codigo = (String) object.get("sigla");
        return getTipo(codigo);
    }

}
