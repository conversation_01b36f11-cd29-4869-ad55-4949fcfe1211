package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VinculoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private String tipovinculo;

	private ClienteVO clienteBean;

	private ColaboradorVO colaboradorBean;

}