package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class TermoSpamDTO {
    @Schema(description = "Termo de identificação de span", example = "PROMOÇÃO")
    public String termo;
}
