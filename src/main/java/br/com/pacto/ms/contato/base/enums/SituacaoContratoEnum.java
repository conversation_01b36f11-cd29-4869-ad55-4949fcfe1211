package br.com.pacto.ms.contato.base.enums;

import java.util.Arrays;
import java.util.Optional;

public enum SituacaoContratoEnum {

    MATRICULA("MA", "Matrícula"),
    REMATRICULA("RE", "<PERSON><PERSON>rí<PERSON>"),
    RENOVACAO("RN", "Renovação"),
    TRANSFERENCIA("TF", "Transferência");

    private String codigo;
    private String descricao;

    SituacaoContratoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static SituacaoContratoEnum findByCodigo(String codigo) {
        if(codigo == null || codigo.trim().equals("")){
            return null;
        }

        Optional<SituacaoContratoEnum> first = Arrays.stream(values())
                .filter(situacaoContratoEnum -> situacaoContratoEnum.getCodigo().equals(codigo))
                .findFirst();

        return first.orElse(null);

    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
