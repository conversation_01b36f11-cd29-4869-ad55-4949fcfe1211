package br.com.pacto.ms.contato.base.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;

public class LengthMensagemSmsExeption extends CustomException {
    private static final long serialVersionUID = -2;

    public LengthMensagemSmsExeption() {
    }

    public LengthMensagemSmsExeption(ExceptionMessageVO... messagesParamVO) {
        super(messagesParamVO);
    }

    public LengthMensagemSmsExeption(String message) {
        super(message);
    }

    public LengthMensagemSmsExeption(String message, ExceptionMessageVO... messagesParamVO) {
        super(message, messagesParamVO);
    }

    public LengthMensagemSmsExeption(Throwable cause) {
        super(cause);
    }

    public LengthMensagemSmsExeption(Throwable cause, ExceptionMessageVO... messagesParamVO) {
        super(cause, messagesParamVO);
    }

    public LengthMensagemSmsExeption(String message, Throwable cause) {
        super(message, cause);
    }

    public LengthMensagemSmsExeption(String message, Throwable cause, ExceptionMessageVO... messagesParamVO) {
        super(message, cause, messagesParamVO);
    }
}
