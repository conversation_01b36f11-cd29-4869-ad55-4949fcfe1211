package br.com.pacto.ms.contato.ia.data.pojo.output;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfiguracaoCrmIAVO {

    private Integer codigo;
    private String loginPactoConversas;
    private String senhaPactoConversas;
    private String tokenZApi;
    private String idInstanciaZApi;
    private Boolean habilitarconfigia;
    private String personalidade;
    private String informacoesAdicionaisAcademia;
}