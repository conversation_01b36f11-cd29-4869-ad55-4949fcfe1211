package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmDiasMetasDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmDiasMetasVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;

import java.util.List;

public interface ConfiguracaoCrmDiasMetasService<T, DTO> {

    List<ConfiguracaoCrmDiasMetasVO> consultar(FasesCRMEnum fase);

    List<ConfiguracaoCrmDiasMetasVO> consultar(FasesCRMEnum fase, TipoFaseCRMEnum tipoFase);
    T incluir(ConfiguracaoCrmDiasMetasDTO configuracaoCrmDiasMetasDTO);
    void excluir(Integer codigo);

    T alterar(Integer codigo, ConfiguracaoCrmDiasMetasDTO configuracaoCrmDiasMetasDTO);
}
