package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "email", schema = "public")
public class EmailEntity {
    @Basic
    @Column(name = "pessoa")
    private int pessoa;
    @Basic
    @Column(name = "emailcorrespondencia")
    private Boolean emailcorrespondencia;
    @Basic
    @Column(name = "email")
    private String email;
    @Id
    @Column(name = "codigo")
    private int codigo;
    @Basic
    @Column(name = "bloqueadobounce")
    private Boolean bloqueadobounce;
    @Basic
    @Column(name = "receberemailnovidades")
    private Boolean receberemailnovidades;

}
