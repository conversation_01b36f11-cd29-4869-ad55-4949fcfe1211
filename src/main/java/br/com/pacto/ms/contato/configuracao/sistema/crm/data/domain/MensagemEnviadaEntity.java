package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.comuns.IntegerListConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.sql.Date;
import java.util.List;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "mensagemenviada", schema = "public")
public class MensagemEnviadaEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @Column(name = "codigoempresa")
    private Integer codigoEmpresa;

    @Column(name = "nomefase")
    private String nomeFase;

    @Column(name = "dataenivo", nullable = false)
    private Date dataEnvio;

    @Column(name = "codigoscliente", nullable = false, columnDefinition = "text")
    @Convert(converter = IntegerListConverter.class)
    private List<Integer> codigosCliente;

    @Column(name = "codigometaextra")
    private Integer codigoMetaExtra;

    @Column(name = "nomemetaextra")
    private String nomeMetaExtra;

}
