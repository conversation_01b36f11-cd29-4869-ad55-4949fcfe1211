package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.contato.config.proxy.AutenticacaoProxyConfig;
import br.com.pacto.ms.contato.ia.data.pojo.input.AutenticacaoDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.AutenticacaoMsTokenDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.PersonaDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.AutenticacaoMsContentResponseVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "Autenticacao", configuration = AutenticacaoProxyConfig.class, url = "http://localhost:8100/")
public interface AutenticacaoProxy {

    @PostMapping(path = "/aut/login", produces = MediaType.APPLICATION_JSON_VALUE)
    AutenticacaoMsContentResponseVO autenticacao(@RequestBody AutenticacaoMsTokenDTO autenticacaoMsTokenDTO);

    @PostMapping(path = "/aut/v2/gt", produces = MediaType.APPLICATION_JSON_VALUE)
    AutenticacaoDTO autenticacaoV2(@RequestBody PersonaDTO personaDTO);

}

