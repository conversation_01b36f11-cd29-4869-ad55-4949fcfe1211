package br.com.pacto.ms.contato.health.data.pojo.output;

import br.com.pacto.ms.contato.health.enums.HealthCheckStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HealthCheckVO {
    @Schema(description = "Versão da api")
    private String version;
    @Schema(description = "Situação de saúde da api")
    private HealthCheckStatusEnum status;
}
