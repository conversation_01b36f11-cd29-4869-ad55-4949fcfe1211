package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.contato.config.proxy.CrmMsWebProxyConfig;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaseIAVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "CrmMs", configuration = CrmMsWebProxyConfig.class)
public interface CrmMsProxy {

    @GetMapping(path = "/v1/configuracao/fase-ia/consultar", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<List<ConfiguracaoCrmFaseIAVO>> listarConfiguracaoCrmFase(
            @RequestHeader("Authorization") String token
    );

    @PostMapping(path = "/v1/ia/conversa/fase/crm/{empresa}", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<ResponsePactoConversasVO<IAConversaServiceVO>> inciarConversaPorFase(
            @RequestHeader("Authorization") String token,
            @PathVariable("empresa") String empresa
    );
    @PutMapping(value = "/v1/ia/contextos/empresa", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<ResponsePactoConversasVO<ContextoEmpresaVO>> atualizarContextoEmpresa(Integer empresa);

    @PutMapping(value = "/v1/ia/contextos/planos", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<ResponsePactoConversasVO<ContextosPlanosVO>> atualizarContextoPlanos(Integer empresa);

    @PutMapping(value = "/v1/ia/contextos/turmas", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<ResponsePactoConversasVO<List<ContextoTurmaVO>>> consultarContextoTurmasPorEmpresa(Integer empresa);

    @PutMapping(value = "/v1/ia/contextos/fasses", produces = MediaType.APPLICATION_JSON_VALUE)
    ResponseEntity<ResponsePactoConversasVO<List<ContextosFasesVO>>> consultarContextoFases(Integer empresa);
}

