package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AberturametaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Integer colaboradorresponsavel;

	private Integer responsavelcadastro;

	private Timestamp dia;

	private Timestamp diafechamento;

	private Boolean fecharmeta;

	private Boolean metaemaberto;

	private Integer responsavelliberacaotrocacolaboradorresponsavel;

	private Boolean aberturaretroativa;

	private Integer empresa;

	private String justificativa;
}