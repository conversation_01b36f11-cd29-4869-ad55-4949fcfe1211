package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.AcessoClienteEntity;
import br.com.pacto.ms.contato.base.data.domain.ColaboradorEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface AcessoClienteRepository extends PagingAndSortingRepository<AcessoClienteEntity, Integer> {

    @Query("select a from AcessoClienteEntity a where a.cliente = :cliente ORDER BY a.codigo")
    Optional<List<AcessoClienteEntity>> consultarPorCliente(@Param("cliente") Integer cliente);
}
