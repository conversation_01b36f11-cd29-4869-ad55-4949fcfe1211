package br.com.pacto.ms.contato.ia.data.pojo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricoContatoAIDTO {

    @Schema(description = "Data do contato", example = "2024-09-10T14:30:00")
    private Timestamp data;
    @Schema(description = "Mensagem da IA", example = "AI: Oi, tudo bem? Como posso ajudar?")
    private String message;
    @Schema(description = "Código do cliente", example = "3")
    private Integer cliente;
    @Schema(description = "Fase do CRM", example = "POS_VENDA")
    private String fase;
    @Schema(description = "Tipo contato do CRM", example = "WA")
    private String tipoContato;

}
