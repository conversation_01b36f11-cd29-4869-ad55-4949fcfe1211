package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.SituacaoclientesinteticodwEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface SituacaoclientesinteticodwRepository
            extends PagingAndSortingRepository<SituacaoclientesinteticodwEntity, Integer> {

    Optional<SituacaoclientesinteticodwEntity> findByCodigocliente(Integer codigoCliente);

    @Query("SELECT s FROM SituacaoclientesinteticodwEntity s WHERE s.cpf = :cpf")
    Optional<SituacaoclientesinteticodwEntity> consultarPorCpf(String cpf);
}
