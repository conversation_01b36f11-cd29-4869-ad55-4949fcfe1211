package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.HistoricoContatoEntity;
import br.com.pacto.ms.contato.avulso.data.domain.SituacaoclientesinteticodwEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.output.SituacaoclientesinteticodwVO;
import br.com.pacto.ms.contato.avulso.data.repository.SituacaoclientesinteticodwRepository;
import br.com.pacto.ms.contato.avulso.service.contract.SituacaoclientesinteticodwService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@AllArgsConstructor
public class SituacaoclientesinteticodwServiceImpl<T> implements SituacaoclientesinteticodwService<T> {

    private SituacaoclientesinteticodwRepository repository;

    @Override
    @LogExecution
    @ObjectMapper(SituacaoclientesinteticodwVO.class)
    public T atualizarPorCliente(@NonNull HistoricoContatoEntity lastHC, Integer codigoCliente) {
        SituacaoclientesinteticodwEntity entity = null;

        // FIXME: VERIFICAR COM O ANDERSON SE ESTÁ CORRETO A LÓGICA.
        if (Objects.nonNull(lastHC)) {
            entity = repository.findByCodigocliente(codigoCliente).get();
            entity.setDataultimocontatocrm(lastHC.getDia());
            entity.setCodigoultimocontatocrm(lastHC.getCodigo());
            entity.setFaseatualcrm(lastHC.getFase());
            entity.setResponsavelultimocontatocrm(lastHC.getResponsavelcadastroByUsuario().getNome());
            repository.save(entity);
        }

        return (T) entity;
    }
}
