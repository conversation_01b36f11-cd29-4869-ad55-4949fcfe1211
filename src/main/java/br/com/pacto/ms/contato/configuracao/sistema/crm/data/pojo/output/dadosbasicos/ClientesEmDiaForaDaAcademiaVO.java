package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Positive;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClientesEmDiaForaDaAcademiaVO {

    @Positive
    private Integer nrfaltaplanomensal;

    @Positive
    private Integer nrfaltaplanotrimestral;

    @Positive
    private Integer nrfaltaplanoacimasemestral;

    @Range(min = 1, max = 8)
    private Integer nrrisco;
}
