package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.TipoColaboradorVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class VinculoFaseVO {

        @Schema(description = "Código do vinculo da fase", example = "1")
        private Integer codigo;

        @Schema(description = "Fase do crm")
        private FaseVO fase;
        @Schema(description = "Tipo de colaborador responsável pela fase do crm")
        private TipoColaboradorVO tipoColaborador;
}
