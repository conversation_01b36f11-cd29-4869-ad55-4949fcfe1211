package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CampanhaResponseVO {
    private String id_campanha;
    private String id_empresa;
    private String nome;
    private String data_inicio;
    private String data_fim;
    private String imagem;
    private String instrucao;
    private Boolean is_template;
    private String whatsapp_link;
    private String keyword;
    private String data_atualizacao;
}
