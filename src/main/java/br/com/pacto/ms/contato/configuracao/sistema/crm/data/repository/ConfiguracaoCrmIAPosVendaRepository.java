package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

 import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAPosVendaEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

 import java.util.List;

public interface ConfiguracaoCrmIAPosVendaRepository  extends PagingAndSortingRepository<ConfiguracaoCrmIAPosVendaEntity, Integer> {

  List<ConfiguracaoCrmIAPosVendaEntity> findAll();

}
