package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "passivo", schema = "public")
public class PassivoEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "nome", nullable = false, length = 50)
    private String nome;
    @Basic
    @Column(name = "telefoneresidencial", nullable = true, length = 14)
    private String telefoneresidencial;
    @Basic
    @Column(name = "telefonecelular", nullable = true, length = 14)
    private String telefonecelular;
    @Basic
    @Column(name = "telefonetrabalho", nullable = true, length = 14)
    private String telefonetrabalho;
    @Basic
    @Column(name = "responsavelcadastro", nullable = true)
    private Integer responsavelcadastro;
    @Basic
    @Column(name = "dia", nullable = true)
    private Timestamp dia;
    @Basic
    @Column(name = "observacao", nullable = true, length = -1)
    private String observacao;
    @Basic
    @Column(name = "colaboradorresponsavel", nullable = true)
    private Integer colaboradorresponsavel;
    @Basic
    @Column(name = "email", nullable = true, length = 50)
    private String email;
    @Basic
    @Column(name = "evento", nullable = true)
    private Integer evento;
    @Basic
    @Column(name = "cliente", nullable = true)
    private Integer cliente;
    @Basic
    @Column(name = "empresa", nullable = true)
    private Integer empresa;
    @Basic
    @Column(name = "nomeconsulta", nullable = true, length = 50)
    private String nomeconsulta;
    @Basic
    @Column(name = "contrato", nullable = true)
    private Integer contrato;
    @Basic
    @Column(name = "objecao", nullable = true)
    private Integer objecao;
    @Basic
    @Column(name = "lead", nullable = true)
    private Boolean lead;
    @Basic
    @Column(name = "origemsistema", nullable = true)
    private Short origemsistema;
    @Basic
    @Column(name = "metaextra", nullable = true)
    private Boolean metaextra;
}
