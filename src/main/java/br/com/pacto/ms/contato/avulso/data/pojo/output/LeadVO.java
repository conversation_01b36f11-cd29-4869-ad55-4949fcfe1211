package br.com.pacto.ms.contato.avulso.data.pojo.output;

import br.com.pacto.ms.contato.avulso.data.domain.LeadEntity;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.Date;

@Data
@Builder
public class LeadVO {
    private Integer lead;
    private LocalDate dataRegistro;

    public static LeadVO toVo(LeadEntity lead){
        return LeadVO.builder()
                .lead(lead.getCodigo())
                .dataRegistro(lead.getDataRegistro())
                .build();
    }
}
