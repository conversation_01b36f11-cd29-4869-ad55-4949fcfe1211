package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoSistemaCrmDiasPosVendasDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoSistemaCrmDiasPosVendasVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmDiasPosVendasService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RestController
@RequestMapping(value = "/v1/configuracao/posvendas")
public class ConfiguracaoPosVendasController extends BaseController {

    @Autowired
    private ConfiguracaoSistemaCrmDiasPosVendasService<ConfiguracaoSistemaCrmDiasPosVendasVO, ConfiguracaoSistemaCrmDiasPosVendasDTO> configuracaosistemaCrmDiasPosVendasService;
    @Autowired
    private RequestService requestService;

    @Operation(summary = "Consultar configuração de Pós Vendas", description = "Dias para entrar em contato com clientes Matriculados e Rematriculados")
    @GetMapping
    public ResponseEntity<ConfiguracaoSistemaCrmDiasPosVendasVO> consultarPosVendas() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoSistemaCrmDiasPosVendasVO>) super.finish(configuracaosistemaCrmDiasPosVendasService.consutlar());
    }


    @Operation(summary = "Cadastrar dias para entrar em contato com clientes Matrcuilados e Rematriculados")
    @PostMapping
    public ResponseEntity<ConfiguracaoSistemaCrmDiasPosVendasVO> incluir(@RequestBody @Valid ConfiguracaoSistemaCrmDiasPosVendasDTO dto){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoSistemaCrmDiasPosVendasVO>) super.finish(configuracaosistemaCrmDiasPosVendasService.incluir(dto));
    }

    @Operation(summary = "Alterar configuração de Pós Vendas", description = "Dias para entrar em contato com clientes Matriculados e Rematriculados")
    @PutMapping("/{codigo}")
    public ResponseEntity<ConfiguracaoSistemaCrmDiasPosVendasVO> alterarPosVendas(@Parameter(description = "Código do Registro da Configuração Pós Venda", example = "1") @PathVariable Integer codigo,
                                                                                  @RequestBody @Valid ConfiguracaoSistemaCrmDiasPosVendasDTO dto) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoSistemaCrmDiasPosVendasVO>) super.finish(configuracaosistemaCrmDiasPosVendasService.alterar(codigo, dto));
    }

    @Operation(summary = "Excluir Configuração Pós Vendas")
    @DeleteMapping("/{codigo}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<?> excluir(@Parameter(name = "codigo", description = "Código do Registro da Configuração Pós Venda.", example = "1")
                                     @PathVariable Integer codigo) {
        this.configuracaosistemaCrmDiasPosVendasService.excluir(codigo);
        return super.finish(ExceptionMessage.CONFIGURACAO_POSVENDA_EXCLUIDA);
    }

    @Operation(summary = "Listar os tipos de colaboradores")
    @GetMapping("/tipocolaborador")
    public ResponseEntity<?> getTipoColaborador() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return super.finish(configuracaosistemaCrmDiasPosVendasService.getTipoColaborador());
    }
}
