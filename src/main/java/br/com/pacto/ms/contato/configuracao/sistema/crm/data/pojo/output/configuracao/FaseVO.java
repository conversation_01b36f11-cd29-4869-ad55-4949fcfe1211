package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;


import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FaseVO {
    @Schema(description = "Nome do enumerador da fase do crm", example = "AGENDAMENTO")
    private String name;
    @Schema(description = "Descrição da fase do crm", example = "Agendamento")
    private String descricao;
    @Schema(description = "Imagem da fase do crm", example = "https://pactosolucoes.com.br/imagens/agendamento.png")
    private String imagem;
    @Schema(description = "Sigla da fase do crm", example = "AG")
    private String sigla;
    @Schema(description = "Descrição do identificador da fase", example = "Meta Agendados")
    private String identificador;
    @Schema(description = "Ordem que a fase será exibida", example = "1")
    private Integer ordemTotalizador;
    private TipoFaseVO tipoFase;
    @Schema(description = "URL da Wiki da fase do crm")
    private String urlWiki;
    @Schema(description = "Objetivo da fase do crm que é exiba na funcionalida meta extra")
    private String objetivo;

    public FaseVO(String name, String descricao, String identificador) {
        this.name = name;
        this.descricao = descricao;
        this.identificador = identificador;
    }
}
