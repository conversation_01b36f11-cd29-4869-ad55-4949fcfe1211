package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "indicado", schema = "public")
public class IndicadoEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "nomeindicado", nullable = true, length = 50)
    private String nomeindicado;
    @Basic
    @Column(name = "telefoneindicado", nullable = true, length = 14)
    private String telefoneindicado;
    @Basic
    @Column(name = "telefone", nullable = true, length = 14)
    private String telefone;
    @Basic
    @Column(name = "email", nullable = true, length = 60)
    private String email;
    @Basic
    @Column(name = "indicacao", nullable = true)
    private Integer indicacao;
    @Basic
    @Column(name = "cliente", nullable = true)
    private Integer cliente;
    @Basic
    @Column(name = "empresa", nullable = true)
    private Integer empresa;
    @Basic
    @Column(name = "origemsistema", nullable = true)
    private Short origemsistema;
    @Basic
    @Column(name = "datalancamento", nullable = true)
    private Timestamp datalancamento;
    @Basic
    @Column(name = "nomeconsulta", nullable = true, length = 50)
    private String nomeconsulta;
    @Basic
    @Column(name = "objecao", nullable = true)
    private Integer objecao;
    @Basic
    @Column(name = "lead", nullable = true)
    private Boolean lead;
    @Basic
    @Column(name = "cpf", nullable = true, length = 50)
    private String cpf;
}
