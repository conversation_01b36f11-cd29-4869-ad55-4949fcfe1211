package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ChatMessageDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.node.ObjectNode;

import br.com.pactosolucoes.commons.data.RepresentationModelData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@EqualsAndHashCode(callSuper=false)
public class HistoricoContatoVO extends RepresentationModelData<HistoricoContatoVO> implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;
	private Timestamp dia;
	private Integer cliente;
	private Integer passivo;
	private Integer indicado;
	private Integer maladireta;
	private String observacao;
	private String tipooperacao;
	private Integer responsavelcadastro;
	private Integer objecao;
	private Integer agenda;
	private String fase;
	private String resultado;
	private String tipocontato;
	private String grausatisfacao;
	private Boolean contatoavulso;
	private String resposta;
	private String opcoes;
	private Integer codigonotificacao;
	private Integer conviteaulaexperimental;
	private Date dataproximoenvio;
	private Boolean wagienvi;

	private UsuarioSimplesVO responsavelcadastroByUsuario;
	private ObjectNode tipoContatoCRM;
	private String diaPorExtenso;
	private String faseDescricao;
	private String observacaoApresentar;
	private String respostaApresentar;
	private List<ChatMessageDTO> observacaoApresentarWA;
}
