package br.com.pacto.ms.contato.avulso.service.impl;

import java.time.LocalDateTime;

import br.com.pacto.ms.contato.configuracao.sistema.commons.service.contract.ConfiguracaosistemaService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.IntegracaoCRMVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import org.springframework.stereotype.Service;

import br.com.pacto.ms.contato.avulso.service.contract.AberturametaService;
import br.com.pacto.ms.contato.avulso.service.contract.ValidacaoService;
import br.com.pacto.ms.contato.avulso.service.exception.MetaFechadaException;
import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.github.resilience4j.retry.annotation.Retry;
import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class ValidacaoServiceImpl implements ValidacaoService {

    private RequestService requestService;

    private AberturametaService<?> aberturametaService;
    private ConfiguracaoSistemaCrmService configuracaoSistemaProxy;
    private ConfiguracaosistemaService<?> configuracaosistemaService;
    @Override
    @LogExecution
    @Retry(name = "verificarCadastroContatoMeta", fallbackMethod = "verificarMetaabertaFallback")
    public void verificarMetaaberta(LocalDateTime dia) {
        DefaultConfigurationDTO dcDTO = requestService.getCurrentConfiguration();
        aberturametaService.verificarMetaAberta(dcDTO.getCompanyId(), dcDTO.getCompanyId(), dia);
        configuracaosistemaService.verificarCadastroContatoMeta();
    }

    @Override
    public boolean validaTermoSpamn() {
        //ResponseDTO<IntegracaoCRMVO> response = (ResponseDTO<IntegracaoCRMVO>) configuracaoSistemaProxy.buscarDadosIntegracao();
        //IntegracaoCRMVO integracaoDto = response.convert(IntegracaoCRMVO.class).get(0);
        return ((IntegracaoCRMVO)configuracaoSistemaProxy.buscarDadosIntegracao()).getIntegracaopacto();
    }


    public void verificarMetaabertaFallback(LocalDateTime dia, Throwable throwable) {
        throw new MetaFechadaException(throwable);
    }

}
