package br.com.pacto.ms.contato.avulso.data.proxy;

import br.com.pacto.ms.contato.base.data.pojo.input.MessageDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.net.URI;

@FeignClient(url = "${proxy.microservice.api}", name = "sendapis")
public interface ConfiguracaoApiProxy {

    @PostMapping(path = "/v1/sms/send")
    String sendSms(URI baseUrl, @RequestBody final MessageDTO m, @RequestHeader("chave") final String chave, @RequestHeader("token") final String token);

    @PostMapping(path = "/v1/jenkins/sendJenkins")
    String sendJenkins(URI baseUrl, @RequestHeader("chave")  final String chave, @RequestHeader("url")  final String url, @RequestHeader("id_mailing")  final String id_mailing, @RequestHeader("urlJenkins")  final String urlJenkins, @RequestHeader("chaveAntiga")  final String chaveAntiga);

    @PostMapping(path = "/v1/jenkins/buildJenkins")
    void buildTask(URI baseUrl, @RequestHeader("chave") final String chave,  @RequestHeader("id_mailing")  final String id_mailing, final @RequestHeader("urlJenkins") String urlJenkins);

}

