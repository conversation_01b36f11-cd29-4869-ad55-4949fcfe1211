package br.com.pacto.ms.contato.avulso.web.controller.v1;

import br.com.pacto.ms.contato.avulso.data.pojo.input.LeadDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ObjecaoDTO;
import br.com.pacto.ms.contato.avulso.service.contract.LeadService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.*;


@Validated
@RestController
@RequestMapping("/v1/lead")
@Tag(name = LEAD, description = LEAD_DESCRICAO)
public class LeadController extends BaseController {

    private final LeadService leadService;

    public LeadController(LeadService leadService) {
        this.leadService = leadService;
    }

    @Operation(summary = "Salvar Lead ", description = "Salva um lead pela IA.")
    @PostMapping
    @LogExecution
    public ResponseEntity<?> cadastrarLead(@Valid @RequestBody LeadDTO leadDTO) {
       return super.finish(this.leadService.cadastrarLead(leadDTO));
    }

}
