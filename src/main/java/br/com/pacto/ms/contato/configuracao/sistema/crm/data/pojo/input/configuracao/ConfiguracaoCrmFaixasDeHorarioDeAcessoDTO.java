package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO {

    @Schema(description = "Descrição do periodo", example = "Manha")
    private String descricao;
    @Schema(description = "Hor<PERSON>rio Início", example = "05:00")
    private String horarioinicio;
    @Schema(description = "Horário Fim", example = "11:59")
    private String horariofim;
}
