package br.com.pacto.ms.contato.base.data.domain;

import javax.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "composicaomodalidade", schema = "public")
public class ComposicaoModalidadeEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "valormensalcomposicao")
    private Double valormensalcomposicao;

    @Column(name = "precomodalidade")
    private Double precomodalidade;

    @Column(name = "composicao")
    private Integer composicao;

    @ManyToOne
    @JoinColumn(name = "modalidade", nullable = false)
    private ModalidadeEntity modalidade;

    @Column(name = "nrvezes")
    private Integer nrvezes;

    @Column(name = "duracao")
    private Integer duracao;

    @ManyToOne
    @JoinColumn(name = "horario")
    private HorarioEntity horario;
}