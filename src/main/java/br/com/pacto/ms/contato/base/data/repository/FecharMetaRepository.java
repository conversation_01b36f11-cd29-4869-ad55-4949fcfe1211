package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.FecharMetaEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import feign.Param;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import javax.transaction.Transactional;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.Date;
import java.util.List;

public interface FecharMetaRepository extends PagingAndSortingRepository<FecharMetaEntity, Integer> {

    @Query("select m from FecharMetaEntity m " +
            "inner join m.aberturaMeta am " +
            "where am.dia = current_date " +
            "and am.empresa = :empresa " +
            "and m.metaCalculada = true")
    List<FecharMetaEntity> consultarPorEmpesa(@Param("empresa") Integer empresa);

    @Query("select m from FecharMetaEntity m " +
            "join m.aberturaMeta am " +
            "where am.colaboradorresponsavel = :colaboradorResponsavel " +
            "and am.empresa = :empresa " +
            "and UPPER(m.identificadorMeta) like UPPER(CONCAT(:identificadorMeta, '%')) " +
            "and am.dia = current_date ")
    FecharMetaEntity consultarMetaPorDiaPorColaboradorResponsavel(
            @Param("empresa") Integer empresa,
            @Param("colaboradorResponsavel") Integer colaboradorResponsavel,
            @Param("identificadorMeta") String identificadorMeta);


    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = "update fecharmeta set meta = :meta, " +
            " porcentagem =:porcentagem, " +
            " metacalculada = true " +
            " where codigo =:codigo ")
    void updateFecharMeta(@Param("meta") Double meta, @Param("porcentagem") Double porcentagem, @Param("codigo") Integer codigo);

}
