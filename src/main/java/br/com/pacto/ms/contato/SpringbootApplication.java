package br.com.pacto.ms.contato;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableCaching
@EnableScheduling
@EnableFeignClients
@SpringBootApplication(scanBasePackages = {"br.com.pactosolucoes.commons","br.com.pacto.ms.comuns","br.com.pacto.ms.oamd"},
        scanBasePackageClasses = SpringbootApplication.class
)
public class SpringbootApplication {

    public static void main(String[] args) {
        SpringApplication.run(SpringbootApplication.class, args);
    }

}
