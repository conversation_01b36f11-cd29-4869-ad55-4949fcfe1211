package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.sql.Timestamp;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "aberturameta", schema = "public", catalog = "bdzillyonawakenbox")
public class AberturametaEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "colaboradorresponsavel", nullable = true)
    private Integer colaboradorresponsavel;
    @Basic
    @Column(name = "responsavelcadastro", nullable = true)
    private Integer responsavelcadastro;
    @Basic
    @Column(name = "dia", nullable = true)
    private Timestamp dia;
    @Basic
    @Column(name = "diafechamento", nullable = true)
    private Timestamp diafechamento;
    @Basic
    @Column(name = "fecharmeta", nullable = true)
    private Boolean fecharmeta;
    @Basic
    @Column(name = "metaemaberto", nullable = true)
    private Boolean metaemaberto;
    @Basic
    @Column(name = "responsavelliberacaotrocacolaboradorresponsavel", nullable = true)
    private Integer responsavelliberacaotrocacolaboradorresponsavel;
    @Basic
    @Column(name = "aberturaretroativa", nullable = true)
    private Boolean aberturaretroativa;
    @Basic
    @Column(name = "empresa", nullable = true)
    private Integer empresa;
    @Basic
    @Column(name = "justificativa", nullable = true, length = -1)
    private String justificativa;
}
