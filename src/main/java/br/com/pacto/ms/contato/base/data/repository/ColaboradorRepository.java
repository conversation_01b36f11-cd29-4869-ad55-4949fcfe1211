package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.ColaboradorEntity;
import br.com.pacto.ms.contato.base.data.domain.ColaboradorResumo;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ColaboradorRepository extends PagingAndSortingRepository<ColaboradorEntity, Integer> {

    @Query(" select new br.com.pacto.ms.contato.base.data.domain.ColaboradorResumo(u.nome, u.codigo)  " +
            "  from UsuarioColaboradorEntity  u  " +
            "  join ColaboradorEntity  c  " +
            "  on u.colaborador = c.codigo " +
            " where c.situacao = 'AT' " +
            " and  u.nome like :name  ")
    Optional<List<ColaboradorResumo>> findyByname(String name);
}
