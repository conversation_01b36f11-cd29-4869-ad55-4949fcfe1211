package br.com.pacto.ms.contato.configuracao.sistema.empresa.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "feriado", schema = "public")
public class FeriadoEntity {
    @Id
    @Column(name = "codigo")
    private int codigo;

    @Basic
    @Column(name = "descricao")
    private String descricao;
    @Basic
    @Column(name = "dia")
    private Timestamp dia;
    @Basic
    @Column(name = "mes")
    private String mes;
    @Basic
    @Column(name = "nacional")
    private Boolean nacional;
    @Basic
    @Column(name = "estadual")
    private Boolean estadual;

    @Basic
    @Column(name = "naorecorrente")
    private Boolean naorecorrente;
    @Basic
    @Column(name = "cidade")
    private Integer cidade;
    @Basic
    @Column(name = "estado")
    private Integer estado;
    @Basic
    @Column(name = "pais")
    private Integer pais;

}
