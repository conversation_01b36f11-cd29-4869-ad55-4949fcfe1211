package br.com.pacto.ms.contato.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum SituacaoParcelaEnum {
    PAGO("PG", "Pago"),
    NAOPAGO("NP", "Não Pago"),
    NAOPAGO_AFATURAR("NPF", "Não Pago a Faturar"),
    NAOPAGO_ARECEBER("NPR", "Não Pago a Receber"),
    NAOPAGO_SEMFATURAMENTO("SF", "Sem Faturamento");

    private String codigo;
    private String descricao;

    public static SituacaoParcelaEnum findByCodigo(String codigo) {
        if(codigo == null){
            return null;
        }

        for (SituacaoParcelaEnum situacao : SituacaoParcelaEnum.values()) {
            if (situacao.getCodigo().equals(codigo)) {
                return situacao;
            }
        }

        return null;
    }
}
