package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pacto.ms.comuns.memcached.MemCachedManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PactoConversaAuthCacheClearService {

    @EventListener(ApplicationReadyEvent.class)
    public void clearPactoConversaAuthCacheOnStartup() {
        log.info("Aplicação iniciada - executando limpeza das chaves do cache PACTO_CONVERSA_AUTH_SERVICE_IMPL");
        MemCachedManager.getInstance().clearPactoConversaAuthKeys();
        log.info("Limpeza das chaves do cache PACTO_CONVERSA_AUTH_SERVICE_IMPL concluída");
    }
}
