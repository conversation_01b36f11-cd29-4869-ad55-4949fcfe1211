package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.MovParcelaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface MovParcelaRepository extends PagingAndSortingRepository<MovParcelaEntity, Integer> {


    @Query("select p from MovParcelaEntity p where p.pessoa = :pessoa")
    @Transactional
    List<MovParcelaEntity> consultarPorPessoa(@Param("pessoa") Integer pessoa);
}
