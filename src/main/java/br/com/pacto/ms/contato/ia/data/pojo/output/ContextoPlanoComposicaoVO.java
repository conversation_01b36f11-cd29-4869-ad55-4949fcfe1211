package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.pojo.output.ModalidadeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ContextoPlanoComposicaoVO {

    @Schema(description = "Descrição da composição", example = "Composição A")
    private String descricao;
    @Schema(description = "Preço da composição", example = "100.0")
    private Float precocomposicao;


    @Schema(description = "Código da composição", example = "1")
    private Integer codigo;
    @Schema(description = "Composição default", example = "true")
    private List<ModalidadeVO> modalidades;
    @Schema(description = "Composição default", example = "true")
    private Boolean composicaodefault;
    @Schema(description = "Composição adicional", example = "false")
    private Boolean composicaoadicional;
    @Schema(description = "Modalidades específicas", example = "true")
    private Boolean modalidadesespecificas;
    @Schema(description = "Quantidade de modalidades", example = "5")
    private Short qtdemodalidades;
}