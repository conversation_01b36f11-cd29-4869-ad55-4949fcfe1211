package br.com.pacto.ms.contato.ia.data.pojo.output;

import lombok.Data;

import java.util.List;

@Data
public class ContextoPlanoVendidoOnlineVO {

    private int codigoPlano;
    private String urlVendaOnline;
    private boolean parcelamentoOperadora;
    private String descricao;
    private double valorMatricula;
    private double nrVezesParcelarMatricula;
    private int codigoHorario;
    private String descricaoHorario;
    private String condicaoPagamento;
    private String descricaoEncantamento;
    private List<PlanoModalidadeVO> modalidades;
    private List<Integer> diasVencimento;
    private double valorMensal;
    private double taxaAdesao;
    private double valorAnuidade;
    private double valorTotalDoPlano;
    private String mesAnuidade;
    private int diaAnuidade;
    private int mesAnuidadeOrdinal;
    private boolean anuidadeNaParcela;
    private int parcelaAnuidade;
    private int duracaoPlano;
    private int quantidadeDiasExtra;
    private String msgValidacao;
    private boolean planoPersonal;
    private List<PlanoProdutoVO> produtos;
    private List<PlanoRecorrenciaParcela> parcelas;
    private List<PlanoAnuidadeParcela> parcelasAnuidade;
    private List<Integer> empresas;
    private String inicioMinimo;
    private Boolean cobrarPrimeiraParcelaCompra;
    private int qtdCreditoPlanoCredito;
    private boolean regimeRecorrencia;
    private boolean renovavelAutomaticamente;
    private int maximoVezesParcelar;
    private List<Integer> categorias;
    private boolean apresentarPactoFlow;
    private Boolean renovarAutomaticamenteUtilizandoValorBaseContrato;
}
