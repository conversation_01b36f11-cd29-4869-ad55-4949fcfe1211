package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.pojo.output.ModeloMensagemVO;
import br.com.pacto.ms.contato.base.data.repository.ModelomensagemRepository;
import br.com.pacto.ms.contato.base.service.contract.ModeloMensagemService;
import br.com.pacto.ms.contato.core.data.pojo.enums.MeioEnvioEnum;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
public  @Service class ModeloMensagemServiceImpl<T> implements ModeloMensagemService <T> {

    private ModelomensagemRepository repository;

    @Override
    @LogExecution
    public T personalizarTagNomePessoa(String msg, String nome) {
        String mensagem = msg.replace("TAG_NOME", nome);
        return (T) mensagem.replace("TAG_PNOME", getPrimeiroNome(nome));
    }

    @Override
    @LogExecution
    @ObjectMapper(ModeloMensagemVO.class)
    public T buscarModeloMensagemPorMeioEnvio() {
        return (T) repository.findModelomensagemEntitiesBymeiodeenvio((short) MeioEnvioEnum.SMS.getCodigo());
    }

    @Override
    @LogExecution
    @ObjectMapper(ModeloMensagemVO.class)
    public T buscarModeloMensagemPorMeioEnvio(int meio) {
        return (T) repository.findModelomensagemEntitiesBymeiodeenvio((short) meio);
    }

    @Override
    @LogExecution
    @ObjectMapper(ModeloMensagemVO.class)
    public T buscarModeloMensagemPorCodigo(int codigo) {
        return (T) repository.findModelomensagemEntitiesBycodigo(codigo);
    }

    public static String getPrimeiroNome(String nome) {
        String nomes[] = nome.split(" ");
        if (nomes.length <= 1) {
            return nome;
        }
        return nomes[0];
    }

}

