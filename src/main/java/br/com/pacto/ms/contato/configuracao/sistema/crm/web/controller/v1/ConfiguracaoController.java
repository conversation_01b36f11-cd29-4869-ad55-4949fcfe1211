package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoSistemaCrmDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ConfiguracaoCRMDadosBasicosDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoSistemaCrmVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RestController
@RequestMapping(value = "/v1/configuracao")
public class ConfiguracaoController extends BaseController {

    @Autowired
    private ConfiguracaoSistemaCrmService<ConfiguracaoSistemaCrmVO, ConfiguracaoCRMDadosBasicosDTO> configuracaosistemacrmService;
    @Autowired
    private RequestService requestService;

    @Operation(summary = "Consultar configurações do modulo CRM")
    @LogExecution
    @GetMapping
    public ResponseEntity<ConfiguracaoSistemaCrmVO> consultarConfiguracaoCrm() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoSistemaCrmVO>) super.finish(configuracaosistemacrmService.consultar());
    }

    @Operation(summary = "Atlerar configurações do modulo CRM")
    @LogExecution
    @PutMapping
    public ResponseEntity<ConfiguracaoSistemaCrmVO> alterarConfiguracaoCrm(@RequestBody @Valid ConfiguracaoSistemaCrmDTO dto){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoSistemaCrmVO>) super.finish(configuracaosistemacrmService.alterar(dto));
    }
}
