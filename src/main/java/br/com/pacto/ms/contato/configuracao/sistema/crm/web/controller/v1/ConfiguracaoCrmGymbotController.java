package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.comuns.data.pojo.output.DepartamentoVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmIADTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoGymbotVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.GymbotTokenVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmIAGymbotService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@Validated
@Tag(name = "Configurações do Gymbot", description = "Gestão de configurações para gymbot")
@RequestMapping(value = "/v1/configuracao/gymbot")
@RestController
@RequiredArgsConstructor
@SuppressWarnings("all")
public class ConfiguracaoCrmGymbotController extends BaseController {

    private final ConfiguracaoCrmIAGymbotService configuracaoCrmIAGymbotService;

    @Operation(summary = "Consultar departamentos ")
    @GetMapping("/departamentos")
    public ResponseEntity<List<DepartamentoVO>> obtemDepartamentos(
            @Parameter(name = "codigoEmpresa", description = "Codigo da empresa responsavel atual", example = "1")
            @RequestParam(required = false) Integer codigoEmpresa,
            @Parameter(name = "token", description = "Tokem responsavel pela integracao do gymbot", example = "hash-token")
            @RequestParam(required = false) String tokenGymbot
    ) {
        try{
        return (ResponseEntity<List<DepartamentoVO>>) super.finish(
                configuracaoCrmIAGymbotService.obterDepartamentos(codigoEmpresa, tokenGymbot));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Atualizar token gymbot")
    @PostMapping("/atualizar-token")
    public ResponseEntity<GymbotTokenVO> atualizaToken(
            @Parameter(name = "codigoEmpresa", description = "Codigo da empresa responsavel atual", example = "1")
            @RequestParam(required = false) Integer codigoEmpresa,
            @Parameter(name = "token", description = "Tokem responsavel pela integracao do gymbot", example = "hash-token")
            @RequestParam(required = false) String tokenGymbot
    ) {
        try {
            return ResponseEntity.ok(configuracaoCrmIAGymbotService.atualizaToken(codigoEmpresa, tokenGymbot));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Consultar token gymbot")
    @GetMapping("/consultar-token")
    public ResponseEntity<GymbotTokenVO> consultaToken(
            @Parameter(name = "codigoEmpresa", description = "Codigo da empresa responsavel atual", example = "1")
            @RequestParam(required = false) Integer codigoEmpresa
    ) {
        try {
            return ResponseEntity.ok(configuracaoCrmIAGymbotService.consultarTokenGymbot(codigoEmpresa));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "enviar configuracao gymbot para selecionar o departamento e a instrução")
    @PostMapping("/enviar-configuracao-gymbot")
    public ResponseEntity<ConfiguracaoCrmIADTO> salvarConfiguracaoGymbot(
            @RequestBody ConfiguracaoGymbotVO configuracaoGymbotVO) {
        try {
            return (ResponseEntity<ConfiguracaoCrmIADTO>) super.finish(
                    configuracaoCrmIAGymbotService.salvarConfiguracaoGymbot(configuracaoGymbotVO));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }

    @Operation(summary = "Remover configuracao gymbot")
    @GetMapping("/remover-token")
    public ResponseEntity<GymbotTokenVO> removerConfiguracaoGymbot(
            @Parameter(name = "codigoEmpresa", description = "Codigo da empresa responsavel atual", example = "1")
            @RequestParam(required = false) Integer codigoEmpresa
    ) {
        try {
            configuracaoCrmIAGymbotService.removerConfiguracaoGymbot(codigoEmpresa);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.internalServerError().build();
        }
    }


}
