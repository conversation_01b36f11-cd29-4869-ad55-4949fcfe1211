package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.handler;

import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pactosolucoes.commons.data.vo.ResponseImplLegacyVO;
import br.com.pactosolucoes.commons.data.vo.ResponseImplVO;
import br.com.pactosolucoes.commons.data.vo.ResponseVO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static br.com.pacto.ms.contato.configuracao.sistema.commons.web.handler.message.ExceptionMessage.CONFIGURACAO_IA_EXCEPTION;
import static br.com.pactosolucoes.commons.enums.message.APIStructureMessages.PROCESS_FINISHED_ERROR;

@ControllerAdvice
@RequiredArgsConstructor
public class ConfiguracaoExceptionHandler {

    private final RequestService requestService;

    @ExceptionHandler(ConfiguracaoIAException.class)
    public ResponseEntity<ResponseVO> handleConfiguracaoException(ConfiguracaoIAException ex) {
        ResponseVO responseVO = null;

        if (requestService.getCurrentConfiguration().getReponseLegacy()) {
            responseVO = ResponseImplLegacyVO.init()
                    .addMessage(CONFIGURACAO_IA_EXCEPTION);
        } else {
            responseVO = ResponseImplVO.init()
                    .addMessage(CONFIGURACAO_IA_EXCEPTION)
                    .addMessage(PROCESS_FINISHED_ERROR);
        }

        return new ResponseEntity<ResponseVO>(responseVO, null,CONFIGURACAO_IA_EXCEPTION.getStatusCode());
    }
}
