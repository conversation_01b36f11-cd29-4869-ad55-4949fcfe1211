package br.com.pacto.ms.contato.avulso.data.pojo.input;

import br.com.pactosolucoes.commons.util.annotation.CpfCnpj;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndicadoDTO {

    @Size(max = 50)
    private String nomeindicado;

    @Size(max = 14)
    private String telefoneindicado;

    @Size(max = 14)
    private String telefone;

    @Size(max = 60)
    private String email;

    private Integer indicacao;

    private Integer cliente;

    private Integer empresa;

    private Short origemsistema;

    private Timestamp datalancamento;

    @Size(max = 50)
    private String nomeconsulta;

    private Integer objecao;

    private Boolean lead;

    @Size(max = 50)
    @CpfCnpj
    private String cpf;
}
