package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.comuns.data.pojo.output.DepartamentoVO;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoGymbot;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoRedeIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmIADTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoGymbotVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.GymbotTokenVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoRedeIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmIAGymbotService;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
import br.com.pacto.ms.contato.ia.service.impl.PactoConversasUrlResolverService;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ConfiguracaoCrmIAGymbotServiceImpl<T, DTO> implements ConfiguracaoCrmIAGymbotService<T, DTO> {

    private final static String CANAL = "gymbot";
    private final PactoConversasIAProxy pactoConversasIAProxy;
    private final RequestService requestService;
    private final ConfiguracaoRedeIARepository configuracaoRedeIARepository;
    private final ConfiguracaoCrmIARepository configuracaoCrmIARepository;
    private final PactoConversasUrlResolverService urlResolverService;

    @Override
    public GymbotTokenVO atualizaToken(Integer codigoEmpresa, String gymbotToken) {
        if (Objects.isNull(gymbotToken) || gymbotToken.isEmpty()) {
            throw new ConfiguracaoIAException("Token do Gymbot não informado");
        }

        HashMap<String, Object> json = new HashMap<>();
        json.put("token", gymbotToken);
        json.put("save_webhook", true);
        json.put("webhook_enabled", true);
        
        String requestUri = urlResolverService.getPactoConversasUrl();
        ResponsePactoConversasVO<String> pactoConversasVO =
                pactoConversasIAProxy.atualizarToken(URI.create(requestUri), identificadorEmpresa(codigoEmpresa), CANAL, json);
        return GymbotTokenVO.builder()
                .status(pactoConversasVO.getSuccess())
                .token(gymbotToken)
                .build();
    }

    @Override
    public GymbotTokenVO consultarTokenGymbot(Integer codigoEmpresa) {
        try {
            String requestUri = urlResolverService.getPactoConversasUrl();
            HashMap<String, String> pactoConversasVO =
                    pactoConversasIAProxy.consultarToken(URI.create(requestUri), identificadorEmpresa(codigoEmpresa), CANAL);

            return GymbotTokenVO.builder()
                    .token(pactoConversasVO.get("token"))
                    .build();
        } catch (FeignException e) {
            if (e.status() == 500) {
                log.error("Msg: Empresa não contem token gymbot : ", e.getMessage());
            }
        } catch (Exception e) {
            log.error("Msg: Erro ao consultar token do gymbot ", e.getMessage());
        }
        return GymbotTokenVO.builder().build();
    }

    @Override
    public List<DepartamentoVO> obterDepartamentos(Integer codigoEmpresa, String gymbotToken) {
        List<DepartamentoVO> listaDepartamentos = new ArrayList<>();
        if (Objects.isNull(gymbotToken) || gymbotToken.isEmpty()) {
            return listaDepartamentos;
        }
        try {
            String requestUri = urlResolverService.getPactoConversasUrl();
            listaDepartamentos = pactoConversasIAProxy.obterDepartamentos(
                    URI.create(requestUri),
                    identificadorEmpresa(codigoEmpresa),
                    CANAL);

            return listaDepartamentos;
        } catch (FeignException e) {
            if (e.status() == 500) {
                return listaDepartamentos;
            }
            throw new ConfiguracaoIAException("Erro ao obter departamentos do Gymbot: " + e.getMessage());
        }
    }


    public String identificadorEmpresa(Integer empresa) {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + empresa;
    }

    @Override
    public ConfiguracaoCrmIADTO salvarConfiguracaoGymbot(ConfiguracaoGymbotVO configuracaoGymbotVO) {
        if (Objects.isNull(configuracaoGymbotVO.getTokenGymbot()) || configuracaoGymbotVO.getTokenGymbot().isEmpty()) {
            throw new ConfiguracaoIAException("Token do Gymbot não informado");
        }

        if (Objects.isNull(configuracaoGymbotVO.getDepartamento()) || configuracaoGymbotVO.getDepartamento().getId() == null) {
            throw new ConfiguracaoIAException("Departamento não informado");
        }

        String requestUri = urlResolverService.getPactoConversasUrl();
        pactoConversasIAProxy.salvarConfiguracaoGymbotConvesasIA(URI.create(requestUri),
                identificadorEmpresa(configuracaoGymbotVO.getCodigoEmpresa()),
                CANAL,
                configuracaoGymbotVO.getDepartamento()
        );

        List<ConfiguracaoCrmIAEntity> configuracoes = configuracaoCrmIARepository
                .findByCodigoEmpresa(configuracaoGymbotVO.getCodigoEmpresa())
                .orElseThrow(() -> new IllegalStateException("Nenhuma configuração encontrada para a empresa com código: "
                        + configuracaoGymbotVO.getCodigoEmpresa()));

        if (configuracoes.isEmpty()) {
            throw new RuntimeException("Não existe configuracao para o codigo da empresa");
        }

        ConfiguracaoCrmIAEntity configuracaoCrmIAEntity = configuracoes.get(0);

        configuracaoCrmIAEntity.setCodigo(configuracaoGymbotVO.getCodigoConfiguracao());
        configuracaoCrmIAEntity.setCodigoEmpresa(configuracaoGymbotVO.getCodigoEmpresa());
        ConfiguracaoGymbot configuracaoGymbot = getConfiguracaoGymbot(configuracaoGymbotVO);
        configuracaoCrmIAEntity.setConfiguracaoGymbot(configuracaoGymbot);

        configuracaoCrmIARepository.save(configuracaoCrmIAEntity);

        ConfiguracaoRedeIAEntity redeIAEntity = configuracaoRedeIARepository
                .findAll()
                .stream()
                .findFirst()
                .orElse(new ConfiguracaoRedeIAEntity());
        return ConfiguracaoCrmIADTO.toDto(configuracaoCrmIAEntity, redeIAEntity);
    }

    @Override
    public void removerConfiguracaoGymbot(Integer codigoEmpresa) {
        ConfiguracaoCrmIAEntity configuracaoCrmIAEntity = configuracaoCrmIARepository
                .findByCodigoEmpresa(codigoEmpresa).get().get(0);

        ConfiguracaoGymbot configuracaoGymbot = getConfiguracaoGymbot(new ConfiguracaoGymbotVO());
        configuracaoCrmIAEntity.setConfiguracaoGymbot(configuracaoGymbot);

        configuracaoCrmIARepository.save(configuracaoCrmIAEntity);

    }


    @NotNull
    private static ConfiguracaoGymbot getConfiguracaoGymbot(ConfiguracaoGymbotVO configuracaoGymbotVO) {
        ConfiguracaoGymbot configuracaoGymbot = new ConfiguracaoGymbot();
        configuracaoGymbot.setTokenGymbot(configuracaoGymbotVO.getTokenGymbot());
        configuracaoGymbot.setDescricaoDepartamento(configuracaoGymbotVO.getDepartamento().getDescricao());
        configuracaoGymbot.setIdDepartamento(configuracaoGymbotVO.getDepartamento().getId().toString());
        configuracaoGymbot.setHabilitarGymbot(configuracaoGymbotVO.getHabilitarGymbot());
        return configuracaoGymbot;
    }

}
