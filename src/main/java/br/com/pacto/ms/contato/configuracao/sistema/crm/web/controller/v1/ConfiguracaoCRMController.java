package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_CRM;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_CRM_DESCRICAO;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ConfiguracaoCRMDadosBasicosDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.ConfiguracaoCRMDadosBasicosVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@Tag(name = CONFIGURACAO_CRM, description = CONFIGURACAO_CRM_DESCRICAO)

@RestController
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RequestMapping(value = "/v1/crm")
public class ConfiguracaoCRMController extends BaseController {

    @Autowired
    private ConfiguracaoSistemaCrmService<ConfiguracaoCRMDadosBasicosVO, ConfiguracaoCRMDadosBasicosDTO> dadosBasicoService;

    @Operation(summary = "Configuração dos dados Básicos", description = "Realiza a atualização das configuração dos dados básicos do CRM.")
    @PutMapping("/dadosBasicos")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<?> atualizarDadosBasicos(@Valid @RequestBody ConfiguracaoCRMDadosBasicosDTO dto) {
        return super.finish(dadosBasicoService.salvarDadosBasicos(dto));
    }

    @Operation(summary = "Configuração dos dados Básicos", description = "Busca as configuração dos dados básicos do CRM.")
    @GetMapping("/dadosBasicos")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<?> buscarDadosBasicos() {
        return super.finish(dadosBasicoService.buscarDadosBasicos());
    }

    @Operation(summary = "Configuração dos dados Básicos", description = "Busca as configuração dos dados básicos do CRM.")
    @GetMapping("/integracao")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<?> buscarDadosIntegracao() {
        return super.finish(dadosBasicoService.buscarDadosIntegracao());
    }
}
