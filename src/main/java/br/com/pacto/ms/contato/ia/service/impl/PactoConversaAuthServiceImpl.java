package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pacto.ms.comuns.memcached.MemCachedManager;
import br.com.pacto.ms.comuns.memcached.enums.ContextoCacheEnum;
import br.com.pacto.ms.comuns.memcached.enums.IdentificadorCacheEnum;
import br.com.pacto.ms.contato.ia.data.pojo.input.AuthResponsePactoConversaDTO;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.ApiProxy;
import br.com.pacto.ms.contato.ia.service.contract.PactoConversaAuthService;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.var;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class PactoConversaAuthServiceImpl implements PactoConversaAuthService {

    @Autowired
    private ApiProxy apiProxy;

    @Autowired
    private RequestService requestService;

    @Autowired
    private PactoConversasUrlResolverService urlResolverService;

    @Value("${conversas.ia.key}")
    private String apiKey;

    @Value("${conversas.ia.token-by-company}")
    private boolean tokenByCompany;

    private static final Log log = LogFactory.getLog(PactoConversaAuthServiceImpl.class);

    private String key;

    public String getAuthTokenPactoConversa() {
        String chave = this.requestService.getCurrentConfiguration().getCompanyKey();
        if(this.tokenByCompany){
            chave = chave + "-" + this.requestService.getCurrentConfiguration().getCompanyId();
        }

        Integer unidade = this.requestService.getCurrentConfiguration().getCompanyId();
        this.key = chave;

        if (this.tokenByCompany && unidade == null)
            throw new DataNotFoundException("Undiade da empresa não encontrado, ao gerar o token para o pacto conversas");

        if (chave == null)
            throw new DataNotFoundException("Chave da empresa não encontrada, ao gerar o token para o pacto conversas");

        String token = MemCachedManager.getInstance().ler(ContextoCacheEnum.PACTO_CONVERSA_AUTH_SERVICE_IMPL.toString(), IdentificadorCacheEnum.KEY.toString(), this.key);

        if (token == null) {
            log.info("Token Conversas.ai não foi encontrado em cache, gerando novo token para empresa: " + this.key);
            AuthResponsePactoConversaDTO authToken = generateAuthToken();
            MemCachedManager.getInstance().gravar(ContextoCacheEnum.PACTO_CONVERSA_AUTH_SERVICE_IMPL.toString(), authToken.getExpiration(), IdentificadorCacheEnum.KEY.toString(), this.key , authToken.getAuthToken());
            token = authToken.getAuthToken();
        }else{
            String obfuscatedToken = token.length() > 6
                    ? token.substring(0, 3) + "****" + token.substring(token.length() - 3)
                    : "****";
            log.info("Token Conversas.ai encontrado em cache: " + obfuscatedToken);
        }

        return token;
    }

    private AuthResponsePactoConversaDTO generateAuthToken() {
        String pactoConversasApiUrl = urlResolverService.getPactoConversasUrl();
        if (pactoConversasApiUrl == null)
            throw new DataNotFoundException("URL do serviço Pacto Conversas não encontrada, ao gerar o token do pacto conversas");

        Map<String, String> requestBody = new HashMap<>();
        requestBody.put("api_key", apiKey);
        requestBody.put("empresa", this.key);
        var response = apiProxy.mandarPost(pactoConversasApiUrl + "/auth/", null, requestBody, AuthResponsePactoConversaDTO.class);
        if (response != null && response.getAuthToken() != null) {
            return response;
        }
        throw new RuntimeException("Falha ao obter o token de autenticação");
    }

}
