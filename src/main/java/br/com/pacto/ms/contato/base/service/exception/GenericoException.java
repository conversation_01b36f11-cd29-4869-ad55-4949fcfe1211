package br.com.pacto.ms.contato.base.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;

public class GenericoException extends CustomException {
	private static final long serialVersionUID = -9192943455755903832L;

	public GenericoException() {
	}

	public GenericoException(ExceptionMessageVO... messagesParamVO) {
		super(messagesParamVO);
	}

	public GenericoException(String message) {
		super(message);
	}

	public GenericoException(String message, ExceptionMessageVO... messagesParamVO) {
		super(message, messagesParamVO);
	}

	public GenericoException(Throwable cause) {
		super(cause);
	}

	public GenericoException(Throwable cause, ExceptionMessageVO... messagesParamVO) {
		super(cause, messagesParamVO);
	}

	public GenericoException(String message, Throwable cause) {
		super(message, cause);
	}

	public GenericoException(String message, Throwable cause, ExceptionMessageVO... messagesParamVO) {
		super(message, cause, messagesParamVO);
	}

}
