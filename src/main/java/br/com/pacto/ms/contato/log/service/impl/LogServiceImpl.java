package br.com.pacto.ms.contato.log.service.impl;

import br.com.pacto.ms.contato.log.data.domain.LogEntity;
import br.com.pacto.ms.contato.log.data.pojo.output.LogPorMinutoVO;
import br.com.pacto.ms.contato.log.data.pojo.output.LogVO;
import br.com.pacto.ms.contato.log.data.repository.LogRepository;
import br.com.pacto.ms.contato.log.service.contract.LogService;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import br.com.pactosolucoes.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
public class LogServiceImpl<T> implements LogService<T> {

    @Autowired
    private LogRepository logRepository;

    @Override
    public void incluir(LogEntity logEntity) {
        this.logRepository.save(logEntity);
    }
}
