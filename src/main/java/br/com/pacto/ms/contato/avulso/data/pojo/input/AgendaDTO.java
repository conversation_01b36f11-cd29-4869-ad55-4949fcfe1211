package br.com.pacto.ms.contato.avulso.data.pojo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class AgendaDTO extends HistoricoContatoDTO {
    @Schema(description = "Hora de agendamento", example = "15", required = true)
    private String hora;

    @Schema(description = "Minuto de agendamento", example = "30", required = true)
    private String minuto;

    @Schema(description = "Hora formatada", example = "15:30")
    private String horaformatada;

    @Schema(description = "Flag para agendamento anterior")
    private boolean anterior;

    @Schema(description = "Número de dias para o agendamento")
    private int nrDias;

    @Schema(description = "Data de agendamento", example = "2024-09-12T12:25:59.261Z", required = true)
    private Timestamp dataagendamento;

    @Schema(description = "Data do lançamento do agendamento", example = "2024-09-12T12:25:59.261Z", required = true)
    private Timestamp datalancamento;

    @Schema(description = "Tipo de agendamento", example = "LI", required = true)
    private String tipoagendamento;

    @Schema(description = "Modalidade do agendamento")
    private Integer modalidade;

    @Schema(description = "Colaborador responsável", example = "2", required = true)
    private Integer colaboradorresponsavel;

    @Schema(description = "Responsável pelo cadastro", example = "2", required = true)
    private Integer responsavelcadastro;

    @Schema(description = "Data de comparecimento ao agendamento")
    private Timestamp datacomparecimento;

    @Schema(description = "Responsável pelo comparecimento")
    private Integer responsavelcomparecimento;

    @Schema(description = "Empresa", example = "1", required = true)
    private Integer empresa;

    @Schema(description = "Flag para convite de aula experimental")
    private Integer conviteaulaexperimental;

    @Schema(description = "Flag para reposição")
    private Integer reposicao;

    @Schema(description = "Flag para uso do GymPass", defaultValue = "false")
    private Boolean gympass;

    @Schema(description = "Tipo de professor")
    private String tipoprofessor;

    @Schema(description = "Código do professor")
    private Integer codigoprofessor;

    @Schema(description = "Horário da aula do aluno")
    private Integer alunohorarioturma;
}
