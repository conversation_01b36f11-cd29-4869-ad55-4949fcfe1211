package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleNotificationsDTO {

    private EventDay event_day;
    private IntermediateDays intermediate_days;
    private SameDay same_day;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventDay {
        private List<NotificationBeforeAfter> notifications_before;
        private List<NotificationBeforeAfter> notifications_after;
        private List<NotificationAtTime> notifications_at;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class IntermediateDays {
        private List<NotificationBeforeAfter> notifications_before;
        private List<NotificationBeforeAfter> notifications_after;
        private List<NotificationAtTime> notifications_at;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SameDay {
        private List<NotificationBeforeAfter> notifications_before;
        private List<NotificationBeforeAfter> notifications_after;
        private List<NotificationAtTime> notifications_at;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationBeforeAfter {
        private String message_instructions;
        private int time;
        private String unit;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class NotificationAtTime {
        private String message_instructions;
        private String time;
    }

}

