package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.avulso.data.domain.*;
import br.com.pacto.ms.contato.avulso.data.repository.ClienteRepository;
import br.com.pacto.ms.contato.avulso.data.repository.ConfigTotalPassRepository;
import br.com.pacto.ms.contato.avulso.data.repository.EmpresaRepository;
import br.com.pacto.ms.contato.avulso.data.repository.HistoricoContatoRepository;
import br.com.pacto.ms.contato.base.data.domain.*;
import br.com.pacto.ms.contato.base.data.repository.*;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.AutenticacaoException;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaseIAVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmFaseIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ProdutoRepository;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.ia.data.pojo.output.AlunoVO;
import br.com.pacto.ms.contato.ia.data.pojo.input.AutenticacaoMsTokenDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import br.com.pacto.ms.contato.ia.data.proxy.proxy.*;
import br.com.pacto.ms.contato.ia.service.contract.ContextoService;
import br.com.pacto.ms.contato.ia.service.impl.exceptions.ContextoException;
import br.com.pacto.ms.contato.ia.service.impl.PactoConversasUrlResolverService;
import br.com.pacto.ms.contato.log.data.repository.UsuarioPerfilAcessoRepository;
import br.com.pacto.ms.contato.log.data.repository.UsuarioRepository;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import br.com.pactosolucoes.utils.DateUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.jetbrains.annotations.NotNull;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ContextoServiceImpl implements ContextoService {

    private static final Log log = LogFactory.getLog(ContextoServiceImpl.class);
    @Autowired
    private EmpresaRepository empresaRepository;
    @Autowired
    private PlanoRepository planoRepository;
    @Autowired
    private TurmaRepository turmaRepository;
    @Autowired
    private ModelMapper mapper;
    @Autowired
    private ZillyonWebProxy zillyonWebProxy;
    @Autowired
    private ClienteRepository clienteRepository;
    @Autowired
    private MalaDiretaRepository malaDiretaRepository;
    @Autowired
    private MovParcelaRepository movParcelaRepository;
    @Autowired
    private QuestionarioClienteRepository questionarioClienteRepository;
    @Autowired
    private QuestionarioPerguntaRepository questionarioPerguntaRepository;
    @Autowired
    private RequestService requestService;
    @Autowired
    private PactoConversasUrlResolverService urlResolverService;
    @Autowired
    private HistoricoContatoRepository historicoContatoRepository;
    @Autowired
    private ContratoRepository contratoRepository;
    @Autowired
    private AcessoClienteRepository acessoClienteRepository;
    @Autowired
    FecharMetaDetalhadoRepository fecharMetaDetalhadoRepository;
    @Autowired
    private TreinoCustomProxy treinoCustomProxy;
    @Autowired
    private PactoConversasIAProxy pactoConversasIAProxy;
    @Autowired
    private ConfiguracaoCrmIARepository configuracaoCrmIARepository;
    @Autowired
    private ConfiguracaoCrmFaseIARepository configuracaoCrmFaseIARepository;
    @Autowired
    private ProdutoRepository produtoRepository;
    @Autowired
    private UsuarioRepository usuarioRepository;
    @Autowired
    private ConfigTotalPassRepository configTotalPassRepository;
    @Autowired
    private AutenticacaoProxy autenticacaoProxy;
    @Value("${discovery.vendas-online-url}")
    private String vendasOnlineUrl;
    @Autowired
    private CidadeRepository cidadeRepository;
    @Autowired
    private UsuarioPerfilAcessoRepository usuarioPerfilAcessoRepository;

    @Override
    public List<ContextoTurmaVO> consultarContextoTurmasPorEmpresa(Integer empresa) {
        List<ContextoTurmaVO> turmaVOS = new ArrayList<>();

        Optional<List<TurmaEntity>> turmaEntities = this.turmaRepository.consultarTurmasAtivasPorEmpresa(empresa);
        if (turmaEntities.isPresent()) {
            turmaVOS = turmaEntities.get().stream()
                    .map(turmaEntity -> {
                        ContextoTurmaVO contextoTurmaVO = this.mapper.map(turmaEntity, ContextoTurmaVO.class);
                        contextoTurmaVO.setPermitiraulaexperimental(contextoTurmaVO.getAulacoletiva() != null && contextoTurmaVO.getAulacoletiva() ? true : contextoTurmaVO.getPermitiraulaexperimental());
                        return contextoTurmaVO;
                    })
                    .collect(Collectors.toList());
        }

        return turmaVOS;
    }

    @Override
    public ContextosPlanosVO consultarContextosPlanos(Integer empresa) {
        ContextosPlanosVO contextosPlanosVO = new ContextosPlanosVO();
        List<ContextoPlanoVendidoOnlineVO> planosComVendaOnline = this.zillyonWebProxy.consultarPlanos(empresa);
        String chave = requestService.getCurrentConfiguration().getCompanyKey();
        List<Integer> codigosPlanosVendasOnline = new ArrayList<>();
        if (planosComVendaOnline != null && planosComVendaOnline.size() > 0) {
            String urlVendasOnline = urlVendasOnline();
            Integer codigoUsuario = getCodigoUsuario(empresa);
            planosComVendaOnline.forEach(plano -> plano.setUrlVendaOnline(urlVendasOnline + "/checkout?un=" + empresa + "&k=" + chave + "&pl=" + plano.getCodigoPlano() + "&us=" + codigoUsuario));
            codigosPlanosVendasOnline = planosComVendaOnline.stream().map(ContextoPlanoVendidoOnlineVO::getCodigoPlano).collect(Collectors.toList());
        }

        contextosPlanosVO.setPlanosComVendaOnline(planosComVendaOnline);
        List<PlanoEntity> planoEntities;
        if (!codigosPlanosVendasOnline.isEmpty()) {
            planoEntities = this.planoRepository.consultarPlanosAtivos(DateUtils.getDataZerada(new Date()), empresa, codigosPlanosVendasOnline);
        } else {
            planoEntities = this.planoRepository.consultarPlanosAtivos(DateUtils.getDataZerada(new Date()), empresa);
        }

        List<ContextoPlanoVendaConsultorVO> planosComVendaBalcao = planoEntities != null ? planoEntities
                .stream()
                .map(plano -> this.mapper.map(plano, ContextoPlanoVendaConsultorVO.class))
                .collect(Collectors.toList()) : null;

        contextosPlanosVO.setPlanosComVendaConsultor(planosComVendaBalcao);

        return contextosPlanosVO;
    }

    private Integer getCodigoUsuario(Integer empresa) {
        Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities = this.configuracaoCrmIARepository.obterPorCodigoEmpresa(empresa);
        if (!configuracaoCrmIAEntities.isPresent() || configuracaoCrmIAEntities.get().isEmpty() || configuracaoCrmIAEntities.get().size() < 1) {
            throw new DataNotFoundException("Nâo foi possível encontrar a configuração do CRM IA da empresa de código " + empresa);
        }

        return Optional.ofNullable(usuarioRepository.getCodigoUsuario(configuracaoCrmIAEntities.get().get(0).getLoginPactoConversas()))
                .orElseThrow(() -> new DataNotFoundException("Nâo foi possível encontrar o usuário da configuração do CRM IA da empresa " + empresa));
    }

    @Deprecated
    public List<ContextoFaseVO> consultarContextosFases(List<ConfiguracaoCrmFaseIAVO> configuracoesCrmFaseIA) {
        return configuracoesCrmFaseIA.stream().map(configuracaoCrmFaseIA -> ContextoFaseVO.builder()
                .name(configuracaoCrmFaseIA.getFase().name())
                .instrucao_ia(configuracaoCrmFaseIA.getDescricao())
                .codigo(configuracaoCrmFaseIA.getFase().getCodigo())
                .descricao(configuracaoCrmFaseIA.getFase().getDescricao())
                .build()
        ).collect(Collectors.toList());
    }


    public String identificadorEmpresa(Integer empresa) {
        return requestService.getCurrentConfiguration().getCompanyKey() + "-" + empresa;
    }

    @Override
    public ResponsePactoConversasVO<ContextoEmpresaVO> atualizarContextoEmpresa(Integer empresa, Boolean matriz, Boolean rede, String chaveMatriz) {
        ContextoEmpresaVO contextoEmpresaVO = this.consultarContextoEmpresa(empresa);
        contextoEmpresaVO.setChaveMatriz(chaveMatriz);
        contextoEmpresaVO.setMatriz(matriz);
        contextoEmpresaVO.setRede(rede);
        setConfiguracaoIA(contextoEmpresaVO, empresa);
        String identificadorEmpresa = this.identificadorEmpresa(empresa);
        URI uri = URI.create(urlResolverService.getPactoConversasUrl());
        ResponsePactoConversasVO<ContextoEmpresaVO> responsePactoConversasVO =
                this.pactoConversasIAProxy.atualizarContextoEmpresa(uri, identificadorEmpresa, contextoEmpresaVO);
        responsePactoConversasVO.setContexto(contextoEmpresaVO);
        return responsePactoConversasVO;
    }

    @Override
    public ResponsePactoConversasVO<ContextoEmpresaVO> atualizarContextoEmpresa(Integer empresa, Boolean rede) {
        return atualizarContextoEmpresa(empresa, rede, null, null);
    }

    private void setConfiguracaoIA(ContextoEmpresaVO contextoEmpresaVO, Integer empresa) {
        Optional<List<ConfiguracaoCrmIAEntity>> config = this.configuracaoCrmIARepository.obterPorCodigoEmpresa(empresa);
        if (config.isPresent() && !config.get().isEmpty() && contextoEmpresaVO != null) {
            ConfiguracaoCrmIAEntity configuracaoCrmIAEntity = config.get().get(0);
            contextoEmpresaVO.setZapiToken(configuracaoCrmIAEntity.getTokenZApi());
            contextoEmpresaVO.setZapiIdInstancia(configuracaoCrmIAEntity.getIdInstanciaZApi());
            contextoEmpresaVO.setUsuarioPactoLogin(configuracaoCrmIAEntity.getLoginPactoConversas());
            contextoEmpresaVO.setUsuarioPactoSenha(configuracaoCrmIAEntity.getSenhaPactoConversas());
            contextoEmpresaVO.setProposito(configuracaoCrmIAEntity.getInformacoesAdicionaisAcademia());
            contextoEmpresaVO.setEmailResponsavelConversasAI(configuracaoCrmIAEntity.getEmailResponsavelConversasAI());
            contextoEmpresaVO.setTelefoneResponsavelConversasAI(configuracaoCrmIAEntity.getTelefoneResponsavelConversasAI());
            contextoEmpresaVO.setDesabilitarAgendamentoAulasExperimentais(
                    configuracaoCrmIAEntity.getDesabilitarAgendamentoAulasExperimentais());
            boolean habilitarIntegracaoGymbot = false;
            if (configuracaoCrmIAEntity.getConfiguracaoGymbot() != null) {
                Boolean habilitarGymbot = configuracaoCrmIAEntity.getConfiguracaoGymbot().getHabilitarGymbot();
                if (habilitarGymbot != null) {
                    habilitarIntegracaoGymbot = habilitarGymbot;
                }
            }
            contextoEmpresaVO.setHabilitarIntegracaoGymbot(habilitarIntegracaoGymbot);
        }
    }

    @Override
    public ResponsePactoConversasVO<List<ContextoTurmaVO>> atualizarContextoTurmas(Integer empresa) {
        List<ContextoTurmaVO> turmaVOS = this.consultarContextoTurmasPorEmpresa(empresa);
        if (turmaVOS == null || turmaVOS.isEmpty()) {
            return new ResponsePactoConversasVO<>();
        }
        URI uri = URI.create(urlResolverService.getPactoConversasUrl());

        String identificador = this.identificadorEmpresa(empresa);
        ResponsePactoConversasVO<List<ContextoTurmaVO>> response = pactoConversasIAProxy.atualizarContextoTurma(uri, identificador, turmaVOS);

        response.setContexto(turmaVOS);
        return response;
    }

    @Override
    public ResponsePactoConversasVO<ContextosPlanosVO> atualizarContextoPlanos(Integer empresa) {
        ContextosPlanosVO contextosPlanosVO = this.consultarContextosPlanos(empresa);
        ResponsePactoConversasVO<ContextosPlanosVO> responsePactoConversasVO = this.pactoConversasIAProxy.atualizarContextoPlanos(URI.create(urlResolverService.getPactoConversasUrl()), this.identificadorEmpresa(empresa), contextosPlanosVO);

        responsePactoConversasVO.setContexto(contextosPlanosVO);
        return responsePactoConversasVO;
    }

    @Override
    public ResponsePactoConversasVO<ResponseEnviarMensagemVO> atualizarContextoPersonalidade(Integer empresa) {
        Optional<List<ConfiguracaoCrmIAEntity>> config = configuracaoCrmIARepository.obterPorCodigoEmpresa(empresa);
        if (config.isPresent() && !config.get().isEmpty()) {
            return this.pactoConversasIAProxy.atualizarContextoPersonalidade(URI.create(urlResolverService.getPactoConversasUrl()), this.identificadorEmpresa(empresa),
                    ConfiguracaoCrmIAVO.builder()
                            .personalidade(config.get().get(0).getPersonalidade())
                            .build());

        }
        return null;
    }

    @Override
    public List<ContextoFaseVO> consultarContextoFases(Integer empresa) {
        return getContextoFaseVOS(empresa);
    }

    @NotNull
    private List<ContextoFaseVO> getContextoFaseVOS(Integer empresa) {
        List<ConfiguracaoCrmFaseIAEntity> configuracaoCrmIAEntities = this.configuracaoCrmFaseIARepository.findAllActivateAndCompany(empresa);
        return configuracaoCrmIAEntities
                .stream().map(configuracaoCrmFaseIA -> ContextoFaseVO.builder()
                        .codigo(configuracaoCrmFaseIA.getFase() != null ? configuracaoCrmFaseIA.getFase().getCodigo() : null)
                        .name(configuracaoCrmFaseIA.getFase() != null ? configuracaoCrmFaseIA.getFase().name() : formatarNomeMeta(configuracaoCrmFaseIA.getNomeMetaExtra()))
                        .instrucao_ia(configuracaoCrmFaseIA.getDescricao())
                        .descricao(configuracaoCrmFaseIA.getFase() != null && configuracaoCrmFaseIA.getFase().getDescricao() != null
                                ? configuracaoCrmFaseIA.getFase().getDescricao()
                                : configuracaoCrmFaseIA.getNomeMetaExtra())
                        .build()
                ).collect(Collectors.toList());
    }

    @Override
    public ResponsePactoConversasVO<List<ContextoFaseVO>> atualizarContextoFases(Integer empresa) {
        List<ContextoFaseVO> contextosFases = getContextoFaseVOS(empresa);
        ResponsePactoConversasVO<List<ContextoFaseVO>> responsePactoConversasVOContextoFases = new ResponsePactoConversasVO<>();
        if (!contextosFases.isEmpty()) {
            responsePactoConversasVOContextoFases = this.pactoConversasIAProxy.atualizarContextoFases(URI.create(urlResolverService.getPactoConversasUrl()), this.identificadorEmpresa(empresa), contextosFases);
            responsePactoConversasVOContextoFases.setContexto(contextosFases);
        }
        return responsePactoConversasVOContextoFases;
    }

    private static String formatarNomeMeta(String nomeMetaExtra) {
        if (nomeMetaExtra == null || nomeMetaExtra.isEmpty()) {
            return "";
        }

        String semNumerosESpeciais = nomeMetaExtra.replaceAll("[^a-zA-Z\\s]", "");

        return semNumerosESpeciais.trim().replaceAll("\\s+", "_").toUpperCase();
    }

    @Override
    public ContextoEmpresaVO consultarContextoEmpresa(Integer empresa) {
        EmpresaEntity empresaEntity = empresaRepository.findById(empresa).orElseThrow(DataNotFoundException::new);
        List<ContextoPlanoVendidoOnlineVO> planosComVendaOnline = this.zillyonWebProxy.consultarPlanos(empresa);
        String chave = requestService.getCurrentConfiguration().getCompanyKey();
        Integer codigoUsuario = getCodigoUsuario(empresa);
        Optional<String> nomeCidadeEEstado = cidadeRepository.buscaNomeCidadeEEstadoPorCodigo(empresaEntity.getCidade());
        ContextoEmpresaVO contextoEmpresaVO = this.mapper.map(empresaEntity, ContextoEmpresaVO.class);
        contextoEmpresaVO.setNomeFantasia(empresaEntity.getNome());
        nomeCidadeEEstado.ifPresent(nome -> {
            String[] partes = nome.split(",");
            if (partes.length == 2) {
                String cidade = partes[0].trim();
                String estado = partes[1].trim();
                contextoEmpresaVO.setNomeCidade(cidade);
                contextoEmpresaVO.setNomeEstado(estado);
            }
        });
        if (planosComVendaOnline != null && planosComVendaOnline.size() > 0) {

            String urlSiteVenda = urlVendasOnline();
            contextoEmpresaVO.setUrlLojaVendaOnline(urlSiteVenda + "/loja?un=" + empresa + "&k=" + chave + "&us=" + codigoUsuario);
            contextoEmpresaVO.setUrlPlanosLojaVendaOnline(urlSiteVenda + "/planos?un=" + empresa + "&k=" + chave + "&us=" + codigoUsuario);
            contextoEmpresaVO.setUrlProdutosLojaVendaOnline(urlSiteVenda + "/produtos?un=" + empresa + "&k=" + chave + "&us=" + codigoUsuario);
            contextoEmpresaVO.setUrlAgendaAulasLojaVendaOnline(urlSiteVenda + "/agenda-aulas?un=" + empresa + "&k=" + chave + "&us=" + codigoUsuario);
            contextoEmpresaVO.setUrlAgendaAulasOnline(urlSiteVenda + "/agenda-aulas?un=" + empresa + "&k=" + chave + "&le=1" + "&us=" + codigoUsuario);
        }
        verificaIntegracaoTotalPassGympass(contextoEmpresaVO, empresaEntity);
        setConfiguracaoIA(contextoEmpresaVO, empresa);
        return contextoEmpresaVO;
    }

    private void verificaIntegracaoTotalPassGympass(
            ContextoEmpresaVO contextoEmpresaVO,
            EmpresaEntity empresaEntity
    ) {
        Optional<ConfigTotalPassEntity> configTotalPassEntity = configTotalPassRepository.findByEmpresaCodigo(empresaEntity.getCodigo());
        contextoEmpresaVO.setExisteTotalPass(configTotalPassEntity.isPresent() && !configTotalPassEntity.get().getInativo());

        if (Objects.nonNull(empresaEntity.getCodigogympass())) {
            contextoEmpresaVO.setExisteGymPass(true);
        }
    }

    @Override
    public List<ProdutoVO> consultarContextoProdutos(Integer empresa) {
        List<ProdutoVO> produtoVOS = this.produtoRepository.getAllProdutosVendasOnline().orElse(Collections.emptyList());
        if (produtoVOS != null && produtoVOS.size() > 0) {
            String urlSiteVenda = urlVendasOnline();
            Integer codigoUsuario = getCodigoUsuario(empresa);
            produtoVOS.forEach(produtoVO -> {
                produtoVO.setUrlVendaOnline(urlSiteVenda + "/checkout?un=" + empresa + "&k=" + requestService.getCurrentConfiguration().getCompanyKey() + "&pr=" + produtoVO.getCodigo() + "&us=" + codigoUsuario);
            });
        }
        return produtoVOS;
    }

    @Override
    public ResponsePactoConversasVO<List<ProdutoVO>> atualizarContextoProdutos(Integer empresa) {
        List<ProdutoVO> produtoVOS = this.consultarContextoProdutos(empresa);
        if (produtoVOS == null || produtoVOS.isEmpty()) {
            return new ResponsePactoConversasVO<>();
        }
        return this.pactoConversasIAProxy.atualizarContextoProdutos(URI.create(urlResolverService.getPactoConversasUrl()), this.identificadorEmpresa(empresa), produtoVOS);
    }

    private String urlVendasOnline() {
        if (this.vendasOnlineUrl != null && !this.vendasOnlineUrl.isEmpty()) {
            return this.vendasOnlineUrl;

        }

        return (this.requestService.getClienteDiscovery().getServiceUrls().getVendasOnlineUrl() != null && !this.requestService.getClienteDiscovery().getServiceUrls().getVendasOnlineUrl().isEmpty())
                ? this.requestService.getClienteDiscovery().getServiceUrls().getVendasOnlineUrl() :
                "https://vendas.online.sistemapacto.com.br";
    }

    @Override
    public ResponsePactoConversasVO<ContextosAlunoVO> atualizarContextoAluno(AlunoVO alunoVO) {
        ContextosAlunoVO contextoAlunoVO = consultarContextosAluno(alunoVO.getCodigoEmpresa(), alunoVO.getCliente(), true);
        if (Objects.isNull(contextoAlunoVO.getAluno())) {
            throw new ContextoException("Aluno não encontrado no codigo :" + alunoVO.getCliente());
        }
        URI uri = URI.create(urlResolverService.getPactoConversasUrl());
        return this.pactoConversasIAProxy.atualizarContextoAluno(uri, this.identificadorEmpresa(alunoVO.getCodigoEmpresa()), contextoAlunoVO);
    }

    private String normalizarNumeroTelefone(String telefone) {
        assert telefone != null;
        telefone = telefone.replaceAll("[^0-9]", "");
        if (!telefone.startsWith("+55") || !telefone.startsWith("55")) {
            telefone = "+55" + telefone;
        }

        return telefone;
    }

    @Override
    public ContextosAlunoVO consultarContextosAluno(Integer empresa, Integer aluno) {
        return consultarContextosAluno(empresa, aluno, true);
    }

    @Override
    @ExceptionHandler({
            ContextoException.class,
            AutenticacaoException.class,
            ConfiguracaoIAException.class
    })
    public ContextosAlunoVO consultarContextosAluno(Integer empresa, Integer aluno, Boolean contextoTreino) {
        return consultarContextosAluno(empresa, aluno, contextoTreino, null);
    }

    @Override
    @ExceptionHandler({
            ContextoException.class,
            AutenticacaoException.class,
            ConfiguracaoIAException.class
    })
    public ContextosAlunoVO consultarContextosAluno(Integer empresa, Integer aluno, Boolean contextoTreino, Integer codigoMetaExtra) {
        ContextosAlunoVO contexto = new ContextosAlunoVO();
        try {
            this.clienteRepository.consultarPorCodigo(aluno).ifPresent(cliente -> {
                ContextoAlunoVO contextoAlunoVO = this.mapper.map(cliente, ContextoAlunoVO.class);
                List<FasesCRMEnum> fasesCRMEnums = this.fecharMetaDetalhadoRepository.consultarFasesCliente(empresa, cliente.getCodigo());
                fasesCRMEnums.sort(Comparator.comparing(FasesCRMEnum::getOrdemTotalizador));
                List<String> fasesCrm = fasesCRMEnums.stream().map(FasesCRMEnum::getName).collect(Collectors.toList());
                contexto.setAluno(contextoAlunoVO);
                if(codigoMetaExtra != null && codigoMetaExtra > 0){
                    Optional<MaladiretaEntity> maladiretaEntity = this.malaDiretaRepository.findById(codigoMetaExtra);
                    if(maladiretaEntity.isPresent()){
                        String titulo = maladiretaEntity.get().getTitulo();
                        String faseMetaExtra = formatarNomeMeta(titulo);
                        contexto.getAluno().setFase_crm(faseMetaExtra);
                    }else{
                        log.info("Não foi encontrado a meta extra com código "+codigoMetaExtra+" ao consultar contexto do aluno para meta extra.");
                        contexto.getAluno().setFase_crm(fasesCRMEnums.isEmpty() ? null : fasesCRMEnums.get(0).getName());
                    }
                }else{
                    contexto.getAluno().setFase_crm(fasesCRMEnums.isEmpty() ? null : fasesCRMEnums.get(0).getName());
                }
                contexto.getAluno().setFases_crm(fasesCrm);
                String telefonePessoa = (cliente.getPessoa() != null && cliente.getPessoa().getTelefones() != null && !cliente.getPessoa().getTelefones().isEmpty())
                        ? normalizarNumeroTelefone(cliente.getPessoa().getTelefones().iterator().next().getNumero())
                        : null;
                contexto.getAluno().getPessoa().setTelefonesconsulta(telefonePessoa);

                List<MovParcelaEntity> parcelaEntities = this.movParcelaRepository.consultarPorPessoa(cliente.getPessoa().getCodigo());
                List<ContextoParcelaVO> contextoParcelaVOS = parcelaEntities.stream()
                        .map(movParcelaEntity -> this.mapper.map(movParcelaEntity, ContextoParcelaVO.class))
                        .collect(Collectors.toList());
                contexto.setParcelas(contextoParcelaVOS);

                List<HistoricoContatoEntity> historicoContatoEntities = this.historicoContatoRepository.consultarPorCliente(cliente.getCodigo());
                List<ContextoContatoVO> historicoContatoVOS = historicoContatoEntities.stream()
                        .map(historicoContatoEntity -> {
                            ContextoContatoVO contextoContatoVO = this.mapper.map(historicoContatoEntity, ContextoContatoVO.class);
                            FasesCRMEnum fasesCRMEnum = FasesCRMEnum.getFasePorSigla(historicoContatoEntity.getFase());
                            ContextoFaseCRMVO faseCRMVO = fasesCRMEnum == null ? null : this.mapper.map(fasesCRMEnum, ContextoFaseCRMVO.class);
                            contextoContatoVO.setFase(faseCRMVO);
                            return contextoContatoVO;
                        })
                        .collect(Collectors.toList());
                contexto.setHistoricoContatos(historicoContatoVOS);

                Optional<List<ContratoEntity>> contratoEntities = this.contratoRepository.consultarPorPessoa(cliente.getPessoa().getCodigo());
                if (contratoEntities.isPresent()) {
                    List<ContextoContratoVO> contextoContatoVOS = contratoEntities.get().stream().map(contrato -> this.mapper.map(contrato, ContextoContratoVO.class)).collect(Collectors.toList());
                    contexto.setContratos(contextoContatoVOS);
                }

                AutenticacaoMsContentResponseVO autenticacaoMsContentResponseVO = getAutenticacaoMsContentResponseVO(requestService.getCurrentConfiguration().getCompanyKey(), empresa);
                atualizarContextoTreino(empresa, contextoTreino, contexto, autenticacaoMsContentResponseVO);

                List<ContextoAcessoClienteVO> acessos = new ArrayList<>();
                acessoClienteRepository.consultarPorCliente(cliente.getCodigo()).ifPresent(acessosCliente -> {
                    acessosCliente.forEach(acesso -> {
                        acessos.add(
                                ContextoAcessoClienteVO.builder()
                                        .dataHoraEntrada(acesso.getDthrEntrada())
                                        .dataHoraSaida(acesso.getDthrSaida())
                                        .build()
                        );
                    });
                });
                contexto.setAcessos(acessos);

                contexto.setBoletimDeVisita(new BoletimDeVisitaVO());
                List<QuestionarioClienteEntity> questionarioClienteEntity = questionarioClienteRepository.consultarPorQuesrionarioCliente(cliente.getCodigo());
                List<QuestionarioClienteVO> questionarioClienteVO = questionarioClienteEntity.stream()
                        .map(questionarioCliente -> this.mapper.map(questionarioCliente, QuestionarioClienteVO.class))
                        .collect(Collectors.toList());
                contexto.getBoletimDeVisita().setQuestionarioCliente(questionarioClienteVO);


                Set<Integer> questionariosUnicos = questionarioClienteEntity.stream()
                        .map(QuestionarioClienteEntity::getQuestionario)
                        .collect(Collectors.toSet());
                List<Integer> listaQuestionarios = new ArrayList<>(questionariosUnicos);
                List<QuestionarioPerguntaEntity> todos = new ArrayList<>();
                for (Integer questionario : listaQuestionarios) {
                    List<QuestionarioPerguntaEntity> questionarioPerguntaEntities = questionarioPerguntaRepository.buscarPerguntas(questionario);
                    todos.addAll(questionarioPerguntaEntities);
                }
                List<QuestionarioPerguntaVO> questionarioPerguntaVO = todos.stream()
                        .map(questionarioPergunta -> this.mapper.map(questionarioPergunta, QuestionarioPerguntaVO.class))
                        .collect(Collectors.toList());

                contexto.getBoletimDeVisita().setQuestionarioPergunta(questionarioPerguntaVO);

            });
        } catch (Exception e) {
            throw new ConfiguracaoIAException("Erro de contexto: " + e.getMessage(), e);
        }
        return contexto;
    }

    private void atualizarContextoTreino(Integer empresa, Boolean contextoTreino, ContextosAlunoVO contexto, AutenticacaoMsContentResponseVO autenticacaoMsContentResponseVO) {
        try {
            if (contextoTreino) {
                ContextoProgramasVO contextoProgramasVO = treinoCustomProxy.obterAlunoPorMatricula(Integer.valueOf(contexto.getAluno().getMatricula()), empresa, autenticacaoMsContentResponseVO.getContent().getToken());
                contexto.setProgramasTreino(contextoProgramasVO.getContent());
            }

            if (contextoTreino) {
                ContextoAvaliacoesFisicasVO contextoAvaliacoesFisicasVO = treinoCustomProxy.obterAvaliacaoFisicaPorMatricula(Integer.valueOf(contexto.getAluno().getMatricula()), empresa, autenticacaoMsContentResponseVO.getContent().getToken());
                contexto.setAvaliacaoFisica(contextoAvaliacoesFisicasVO.getContent());
            }
        } catch (Exception e) {
            log.error("Erro ao atualizar contexto treino", e);
            e.printStackTrace();
        }

    }

    private AutenticacaoMsContentResponseVO getAutenticacaoMsContentResponseVO(String chaveEmpresa, Integer empresa) {
        try {
            Optional<List<ConfiguracaoCrmIAEntity>> config = configuracaoCrmIARepository.findByCodigoEmpresa(empresa);
            if (!config.isPresent() || config.get().isEmpty()) {
                throw new DataNotFoundException("Nâo foi possível encontrar a configuração do CRM IA");
            }
            ConfiguracaoCrmIAEntity configuracaoCrmIA = config.get().stream().findFirst().get();

            AutenticacaoMsTokenDTO authMs = AutenticacaoMsTokenDTO
                    .builder()
                    .chave(chaveEmpresa)
                    .username(configuracaoCrmIA.getLoginPactoConversas())
                    .senha(configuracaoCrmIA.getSenhaPactoConversas())
                    .build();

            return this.autenticacaoProxy.autenticacao(authMs);
        } catch (Exception e) {
            log.error("Erro ao autenticar usuario");
            throw new AutenticacaoException("Erro ao autenticar usuario", e);
        }
    }

    @Override
    public List<ContextosAlunoVO> consultarContextosAlunosPorFase(Integer empresa, FasesCRMEnum fase, Pageable pageable) {
        Optional<Page<List<Integer>>> paginaClientes = this.fecharMetaDetalhadoRepository.consultarCodigosClientesPorFaseEmpresa(empresa, fase.getSigla(), pageable);

        if (paginaClientes.isPresent() && paginaClientes.get().hasContent()) {
            List<Integer> listaClientes = paginaClientes.get().getContent().stream()
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            return listaClientes.stream()
                    .map(cliente -> consultarContextosAluno(empresa, cliente))
                    .collect(Collectors.toList());
        }

        return new ArrayList<>();
    }

    @Override
    public CodigosAlunoPaginaVO consultarCodigosAlunosPorFaseComPagina(Integer empresa, FasesCRMEnum fase, Integer codigoMetaExtra, Pageable pageable) {
        String sigla = fase != null ? fase.getSigla() : null;
        Optional<Page<Integer>> paginaCodigosClientes = fase == FasesCRMEnum.AGENDAMENTO
                ? this.fecharMetaDetalhadoRepository.consultarCodigosClientesPorFaseEmpresaForAgendamento(empresa, sigla, codigoMetaExtra, pageable)
                : this.fecharMetaDetalhadoRepository.consultarCodigosClientesPorFaseEmpresa(empresa, sigla, codigoMetaExtra, pageable);

        CodigosAlunoPaginaVO codigosAlunoVO = new CodigosAlunoPaginaVO();
        if (paginaCodigosClientes.isPresent()) {
            codigosAlunoVO.setListaCodigoClientes(paginaCodigosClientes.get().toList());
            codigosAlunoVO.setTotalRegistros(paginaCodigosClientes.get().getTotalPages());
            return codigosAlunoVO;
        }
        return codigosAlunoVO;
    }

    @Override
    @Transactional
    public ContextoPlanoVendaConsultorVO consultarContextoPlano(Integer plano) {
        PlanoEntity planoEntity = this.planoRepository.consultarPorCodigo(plano)
                .orElseThrow(() -> new DataNotFoundException("O plano " + plano + " não existe"));
        return this.mapper.map(planoEntity, ContextoPlanoVendaConsultorVO.class);
    }

    @Override
    public void atualizarContextosConversasIA(Integer codigoEmpresa, Boolean rede) {
        atualizarContextoEmpresa(codigoEmpresa, rede);
        atualizarContextoFases(codigoEmpresa);
        atualizarContextoTurmas(codigoEmpresa);
        atualizarContextoPlanos(codigoEmpresa);
        atualizarContextoPersonalidade(codigoEmpresa);
    }
}
