package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoEmailFechamentoMetaDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


@Data
@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
public class ConfiguracaoCrmFaixasDeHorarioDeAcessoVO extends ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO {

    @Schema(description = "Código da configuração Faixas De Horario De Acesso", example = "1")
    private Integer codigo;

}
