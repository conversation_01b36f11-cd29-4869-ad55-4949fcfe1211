package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ProcessarMetaDiariaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.proxy.ZillyonWebCrmProxy;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ZwCrmService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.exception.ProcessarMetaDiariaException;
import feign.FeignException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ZwCrmServiceImpl implements ZwCrmService {

    @Autowired
    private ZillyonWebCrmProxy zillyonWebCrmProxy;

    private static final Logger log = LoggerFactory.getLogger(ZwCrmServiceImpl.class);

    public ProcessarMetaDiariaVO processarMetaDiaria() throws ProcessarMetaDiariaException {
        try {
            zillyonWebCrmProxy.processarMetaDiaria();
            return new ProcessarMetaDiariaVO("Meta diária processada com sucesso.");
        } catch (FeignException e) {
            int status = e.status();
            String mensagem;
            switch (status) {
                case 400:
                    mensagem = "Chave inválida ou ausente.";
                    break;
                case 401:
                    mensagem = "Autenticação inválida.";
                    break;
                case 403:
                    mensagem = "Permissão negada.";
                    break;
                case 404:
                    mensagem = "Recurso não encontrado.";
                    break;
                case 500:
                    mensagem = "Erro interno do servidor.";
                    break;
                default:
                    mensagem = "Erro desconhecido: " + e.getMessage();
                    break;
            }
            log.error("Falha ao processar meta: " + mensagem);
            new ProcessarMetaDiariaException(status, mensagem);
        }
        return null;
    }

}
