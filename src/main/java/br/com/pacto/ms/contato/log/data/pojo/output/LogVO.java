package br.com.pacto.ms.contato.log.data.pojo.output;

import br.com.pactosolucoes.commons.data.RepresentationModelData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogVO extends RepresentationModelData {
    @Schema(description = "Código do log", example = "1")
    private Integer codigo;
    @Schema(description = "Nome da entidade", example = "configuracaosistema")
    private String nomeEntidade;
    @Schema(description = "Descrição da entidade", example = "Configuração do sistema")
    private String nomeEntidadeDescricao;
    @Schema(description = "Chave primária da entidade", example = "1")
    private String chavePrimaria;
    @Schema(description = "Nome do campo alterado", example = "nrdiasavencer")
    private String nomeCampo;
    @Schema(description = "Descrição do campo", example = "Número de dias para vencimento")
    private String nomeCampoDescricao;
    @Schema(description = "Valor do campo antes da alteração", example = "28")
    private String valorCampoAnterior;
    @Schema(description = "Valor do campo após a alteração", example = "10")
    private String valorCampoAlterado;
    @Schema(description = "Data da alteração", example = "2021-01-01 10:01:22")
    private Date dataAlteracao;
    @Schema(description = "Nome do responsável pela alteração", example = "João da Silva")
    private String responsavelAlteracao;
    @Schema(description = "Identificador da operação realizada", example = "ALTERAÇÃO")
    private String operacao;
    @Schema(description = "Código da pessoa da pessoa", example = "1")
    private Integer pessoa;
    @Schema(description = "Identificador do cliente", example = "1")
    private Integer cliente;

}
