package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoEmailFechamentoMetaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmFaixasDeHorarioDeAcessoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RestController
@RequestMapping(value = "/v1/configuracao/horario-acesso")
public class ConfiguracaoCrmFaixasDeHorarioDeAcessoController extends BaseController {

    @Autowired
    private ConfiguracaoCrmFaixasDeHorarioDeAcessoService<ConfiguracaoCrmFaixasDeHorarioDeAcessoVO, ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO> service;
    @Autowired
    private RequestService requestService;

    @Operation(summary = "Consultar lista de Faixas de Horário de Acesso")
    @GetMapping
    public ResponseEntity<ConfiguracaoEmailFechamentoMetaVO> consultar() {
        return (ResponseEntity<ConfiguracaoEmailFechamentoMetaVO>) super.finish(service.consultar());
    }

    @Operation(summary = "Cadastrar Faixas de Horário de Acesso")
    @PostMapping
    public ResponseEntity<ConfiguracaoEmailFechamentoMetaVO> incluir(@RequestBody @Valid ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO configuracaoCrmFaixasDeHorarioDeAcessoDTO) throws Exception {
        return (ResponseEntity<ConfiguracaoEmailFechamentoMetaVO>) super.finish(service.incluir(configuracaoCrmFaixasDeHorarioDeAcessoDTO));
    }

    @Operation(summary = "Alterar faixa de Horário de Acesso")
    @PutMapping(value = "{codigo}")
    public ResponseEntity<ConfiguracaoEmailFechamentoMetaVO> alterar(
            @PathVariable Integer codigo,
            @RequestBody @Valid ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO configuracaoCrmFaixasDeHorarioDeAcessoDTO) throws Exception {
        return (ResponseEntity<ConfiguracaoEmailFechamentoMetaVO>) super.finish(service.alterar(codigo, configuracaoCrmFaixasDeHorarioDeAcessoDTO));
    }

    @Operation(summary = "Excluir Faixas de Horário de Acesso")
    @DeleteMapping("/{codigo}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<?> excluir(@Parameter(name = "codigo", description = "Código da do registro de Faixas de Horário de Acesso", example = "1")
                                                                         @PathVariable Integer codigo){
        this.service.excluir(codigo);
        return super.finish(ExceptionMessage.FAIXAHORARIO_EXCLUIDO);
    }

}
