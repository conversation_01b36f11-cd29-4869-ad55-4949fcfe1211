package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cidade", schema = "public")
public class CidadeEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "estado", nullable = false)
    private EstadoEntity estado;

    @Column(name = "nome", nullable = false)
    private String nome;

    @Column(name = "nomesemacento", nullable = false)
    private String nomesemacento;

    @Column(name = "codigomunicipio")
    private String codigomunicipio;
}
