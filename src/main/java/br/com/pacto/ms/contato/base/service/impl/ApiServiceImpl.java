package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.avulso.data.proxy.ConfiguracaoApiProxy;
import br.com.pacto.ms.contato.avulso.data.proxy.NotificacaoAppProxy;
import br.com.pacto.ms.contato.base.data.pojo.input.MalaDiretaDTO;
import br.com.pacto.ms.contato.base.data.pojo.input.MessageDTO;
import br.com.pacto.ms.contato.base.service.contract.ApiService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.IntegracaoCRMVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.MeioEnvioEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoAgendamentoEnum;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.net.URI;

@AllArgsConstructor
public  @Service
class ApiServiceImpl<T> implements ApiService {

    private ConfiguracaoApiProxy configuracaoApiProxy;
    private NotificacaoAppProxy notificacaoAppProxy;
    private RequestService requestService;

    @Override
    public String sendSms(MessageDTO msg, String key, String token) {
        URI uri = URI.create(requestService.getClienteDiscovery().getServiceUrls().getApiZwUrl());
        return   configuracaoApiProxy.sendSms(uri, msg, key, token);
    }

    @Override
    public void sendJenkins(String chave, String urlJenkins, String id_mailing, String urlMailling, String chaveAntiga) {
        URI uri = URI.create(requestService.getClienteDiscovery().getServiceUrls().getApiZwUrl());
        configuracaoApiProxy.sendJenkins(uri, chave, urlJenkins, id_mailing, urlMailling, chaveAntiga);
    }

    @Override
    public void updateJenkinsInstantaneo(MalaDiretaDTO malaDireta, IntegracaoCRMVO confCrm, String key, MeioEnvioEnum m, TipoAgendamentoEnum t) {
        String chave = String.format("%s_%s_%s",key, m.getDescricao(), t.name());
        URI uri = URI.create(requestService.getClienteDiscovery().getServiceUrls().getApiZwUrl());
        configuracaoApiProxy.sendJenkins(uri, chave, confCrm.getUrlMailing(), Integer.toString(malaDireta.getCodigo()), confCrm.getUrlJenkins(), "tete");
        configuracaoApiProxy.buildTask(uri, chave, Integer.toString(malaDireta.getCodigo()), confCrm.getUrlJenkins());
    }

    @Override
    public String enviarNotificacaoApp(String key, String idCliente, String titulo, String textoCRM, String opcoes) {
        URI uri = URI.create(requestService.getClienteDiscovery().getServiceUrls().getTreinoUrl() + "/prest");
        return notificacaoAppProxy.gerarNotificacao(uri, key, idCliente, titulo, textoCRM, opcoes);
    }
}
