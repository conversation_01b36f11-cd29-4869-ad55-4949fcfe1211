package br.com.pacto.ms.contato.base.data.domain;

import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import br.com.pacto.ms.contato.base.enums.SituacaoParcelaEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "movparcela", schema = "public")
public class MovParcelaEntity {

    @Id
    @Column(name = "codigo")
    private int codigo;
    private String descricao;
    private Integer vendaavulsa;
    private Double valorparcela;
    private SituacaoParcelaEnum situacao;
    private Date datavencimento;
    private Date dataregistro;
    private Integer nrtentativas;
    private Date datacobranca;
    private Integer pessoa;

}
