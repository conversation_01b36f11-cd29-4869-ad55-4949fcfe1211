package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoEmailFechamentoMetaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoEmailFechamentoMetaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoEmailFechamentoMetaService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.ConfiguracaoMessages;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.websocket.server.PathParam;
import java.util.List;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações do modulo CRM")
@RestController
@RequestMapping(value = "/v1/configuracao/email")
public class ConfiguracaoEmailFechamentoMetaController extends BaseController {

    @Autowired
    private ConfiguracaoEmailFechamentoMetaService<ConfiguracaoEmailFechamentoMetaVO, ConfiguracaoEmailFechamentoMetaDTO> service;
    @Autowired
    private RequestService requestService;

    @Operation(summary = "Consultar emails de fechamento de meta do CRM")
    @LogExecution
    @GetMapping
    public ResponseEntity<ConfiguracaoEmailFechamentoMetaVO> consultar() {
        requestService.getCurrentConfiguration().setReponseLegacy(true);
        return (ResponseEntity<ConfiguracaoEmailFechamentoMetaVO>) super.finish(service.consultar());
    }

    @Operation(summary = "Alterar um email de fechamento de meta do CRM")
    @LogExecution
    @PutMapping("/{codigo}")
    public ResponseEntity<ConfiguracaoEmailFechamentoMetaVO> alterar(@Parameter(description = "Código do registro do email") @PathVariable Integer codigo,
                                                                     @RequestBody @Valid ConfiguracaoEmailFechamentoMetaDTO configuracaoEmailFechamentoMetaDTO){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoEmailFechamentoMetaVO>) super.finish(service.alterar(codigo, configuracaoEmailFechamentoMetaDTO));
    }

    @Operation(summary = "Cadastrar email de fechamento de meta do CRM")
    @LogExecution
    @PostMapping
    public ResponseEntity<ConfiguracaoEmailFechamentoMetaVO> incluir(@RequestBody @Valid ConfiguracaoEmailFechamentoMetaDTO configuracaoEmailFechamentoMetaDTO){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ConfiguracaoEmailFechamentoMetaVO>) super.finish(service.incluir(configuracaoEmailFechamentoMetaDTO));
    }

    @Operation(summary = "Excluir email de fechamento de meta do CRM")
    @LogExecution
    @DeleteMapping("/{codigo}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<?> excluir(@Parameter(name = "codigo", description = "Código da do registro de email de fechamento de meta", example = "1")
                                                                         @PathVariable Integer codigo){
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        this.service.excluir(codigo);
        return super.finish(ConfiguracaoMessages.EMAIL_FECHAMENTO_META_REGISTRADO);
    }
}
