package br.com.pacto.ms.contato.base.enums;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SituacaoParcelaEnumConverter implements AttributeConverter<SituacaoParcelaEnum, String> {

    @Override
    public String convertToDatabaseColumn(SituacaoParcelaEnum situacaoClienteEnum) {
        if(situacaoClienteEnum == null){
            return null;
        }
        return situacaoClienteEnum.getCodigo();
    }

    @Override
    public SituacaoParcelaEnum convertToEntityAttribute(String id) {
        return SituacaoParcelaEnum.findByCodigo(id);
    }
}
