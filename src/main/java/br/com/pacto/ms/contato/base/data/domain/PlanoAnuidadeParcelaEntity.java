package br.com.pacto.ms.contato.base.data.domain;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.ManyToOne;
import javax.persistence.JoinColumn;

@Entity
@Data
@Table(name = "planoanuidadeparcela", schema = "public")
public class PlanoAnuidadeParcelaEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "planorecorrencia", nullable = false)
    private PlanoRecorrenciaEntity planoRecorrencia;

    @Column(name = "numero")
    private Integer numero;

    @Column(name = "valor")
    private Double valor;

    @Column(name = "parcela")
    private Integer parcela;
}
