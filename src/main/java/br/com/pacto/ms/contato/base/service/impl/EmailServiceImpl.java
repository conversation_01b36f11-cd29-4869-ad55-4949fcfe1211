package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.domain.PessoaEmail;
import br.com.pacto.ms.contato.base.data.pojo.output.EmailVO;
import br.com.pacto.ms.contato.base.data.repository.EmailRepository;
import br.com.pacto.ms.contato.base.service.contract.EmailService;
import br.com.pacto.ms.contato.base.service.exception.EmailException;
import br.com.pactosolucoes.commons.util.OptionalUtils;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
public @Service class EmailServiceImpl<T> implements EmailService<T> {

    private EmailRepository repository;
    

    @Override
    @LogExecution
    @ObjectMapper(EmailVO.class)
    public T buscarPorMatricula(int matricula) {
    	return (T) new OptionalUtils<List<PessoaEmail>>().orElseThrow(repository.buscarPorMatricula(matricula), EmailException::new);
    }

    @Override
    @LogExecution
    @ObjectMapper(EmailVO.class)
    public T buscarPorCliente(int cliente) {
    	return (T) new OptionalUtils<List<PessoaEmail>>().orElseThrow(repository.buscarPorCliente(cliente), EmailException::new);
    }
}
