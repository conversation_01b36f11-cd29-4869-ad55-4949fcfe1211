package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.contato.config.proxy.ZillyonWebProxyConfig;
import br.com.pacto.ms.contato.ia.data.pojo.output.ContextoPlanoVendidoOnlineVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "ZillyonWeb", configuration = ZillyonWebProxyConfig.class, url = "http://localhost:8200/ZillyonWeb")
public interface ZillyonWebProxy {

    @GetMapping(path = "/prest/negociacao?operacao=CONSULTAR_PLANOS")
    List<ContextoPlanoVendidoOnlineVO> consultarPlanos(@RequestParam final Integer empresa);

}

