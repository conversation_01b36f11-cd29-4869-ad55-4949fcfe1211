package br.com.pacto.ms.contato.configuracao.sistema.empresa.data.repository;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;

import org.springframework.data.repository.PagingAndSortingRepository;

import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.domain.FeriadoEntity;

public interface FeriadoRepository extends PagingAndSortingRepository<FeriadoEntity,Integer> {
    Optional<List<FeriadoEntity>> findByDia(Timestamp dia);
}

