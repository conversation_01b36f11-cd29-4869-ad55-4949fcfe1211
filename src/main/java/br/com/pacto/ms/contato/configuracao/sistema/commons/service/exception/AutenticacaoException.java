package br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class AutenticacaoException extends CustomException {

    public AutenticacaoException(String message) {
        super(message);
    }

    public AutenticacaoException(ExceptionMessageVO... messagesParamVO) {
        super(messagesParamVO);
    }
    public AutenticacaoException(String message, Throwable cause) {
        super(message, cause);
    }
}
