package br.com.pacto.ms.contato.base.web.controller.v1;

import br.com.pacto.ms.contato.base.data.pojo.output.TelefoneVO;
import br.com.pacto.ms.contato.base.service.contract.TelefoneService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CLIENTES;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CLIENTES_DESCRICAO;

@Validated
@RestController
@RequestMapping("/v1/generico/telefone")
@Tag(name = CLIENTES, description = CLIENTES_DESCRICAO)
public class TelefoneController extends BaseController {
    @Autowired
    private RequestService requestService;

    @Autowired
    private TelefoneService<TelefoneVO> hcService;

    @Operation(summary = "Busca os telefones peloa matricula e tipo",
            description = "Busca os telefones pelos tipos, RE, CE ...")
    @LogExecution
    @GetMapping("/telefoneTipo/{tipo}/matricula/{matricula}")
    public ResponseEntity<?> buscarPorNomeTipo(@PathVariable String tipo, @PathVariable int matricula) {
        return super.finish(hcService.buscarTelefonePorTipo(matricula, tipo));
    }

    @Operation(summary = "Busca os telefones através da matricula ",
            description = "Busca os telefones por matriccula ")
    @LogExecution
    @GetMapping("fixo/matricula/{matricula}")
    public ResponseEntity<?> buscarTelefoneFixo(@PathVariable int matricula) {
        return super.finish(hcService.buscarTelefoneFixo(matricula));
    }
}
