package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogTagTransform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaofaseia", schema = "public")
@EntityListeners(LogListener.class)
@LogTagTransform(ConfiguracaoCrmIATagTransform.class)
public class ConfiguracaoCrmFaseIAEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @Basic
    @Column(name = "codigoempresa")
    private Integer codigoEmpresa;

    @Column
    private FasesCRMEnum fase;

    @Basic
    @Column(name = "descricao")
    private String descricao;

    @Basic
    @Column(name = "habilitar")
    private Boolean habilitar;

    @Basic
    @Column(name = "codigometaextra")
    private Integer codigoMetaExtra;

    @Basic
    @Column(name = "nomemetaextra")
    private String nomeMetaExtra;

    @Basic
    @Column(name = "mensagensextras")
    private String mensagensExtras;

}
