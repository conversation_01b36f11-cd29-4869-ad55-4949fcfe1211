package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ContextoEnderecoVO {

    @Schema(description = "Código identificador do endereço.")
    private Integer codigo;

    @Schema(description = "Tipo do endereço.")
    private String tipoEndereco;

    @Schema(description = "CEP do endereço.")
    private String cep;

    @Schema(description = "Bairro do endereço.")
    private String bairro;

    @Schema(description = "Número do endereço.")
    private String numero;

    @Schema(description = "Complemento do endereço.")
    private String complemento;

    @Schema(description = "Endereço.")
    private String endereco;

    @Schema(description = "Indica se o endereço é de correspondência.")
    private Boolean enderecocorrespondencia;
}
