package br.com.pacto.ms.contato.base.data.domain;

import br.com.pacto.ms.contato.base.enums.SituacaoContratoEnum;
import br.com.pacto.ms.contato.base.enums.SituacaoEnum;
import lombok.Data;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

@Data
@Entity
@Table(name = "contrato", schema = "public")
public class ContratoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private Boolean bolsa;
    private String nomemodalidades;
    private Timestamp datamatricula;
    private Timestamp dataprevistarenovar;
    private Timestamp datarenovarrealizada;
    private Timestamp dataprevistarematricula;
    private Timestamp datarematricularealizada;
    private SituacaoContratoEnum situacaocontrato;
    private SituacaoEnum situacao;
    private String situacaorenovacao;
    private String situacaorematricula;
    private String observacao;
    private Float valorfinal;
    private Timestamp vigenciaate;
    private Timestamp vigenciaateajustada;
    private Timestamp vigenciade;
    @ManyToOne
    @JoinColumn(name = "plano")
    private PlanoEntity plano;
    private Integer consultor;
    private Boolean renovavelautomaticamente;
    private Boolean vendacreditotreino;
    private Integer pessoa;
}
