package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TipoColaboradorVO {
    @Schema(description = "Nome do enumerador do tipo de colaborador", example = "PROFESSOR")
    private String name;
    @Schema(description = "Sigla do tipo de colaborador", example = "PR")
    private String sigla;
    @Schema(description = "Descrição do tipo de colaborador", example = "Professor")
    private String descricao;
}
