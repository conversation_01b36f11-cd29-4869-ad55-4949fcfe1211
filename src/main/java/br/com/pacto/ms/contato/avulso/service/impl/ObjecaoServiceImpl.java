package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.ObjecaoEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.avulso.data.pojo.input.HistoricoObjecaoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.HistoricoContatoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ObjecaoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ObjecaoVO;
import br.com.pacto.ms.contato.avulso.data.repository.ObjecaoRepository;
import br.com.pacto.ms.contato.avulso.data.repository.factory.HistoricoObjecaoDefinitivaFactory;
import br.com.pacto.ms.contato.avulso.service.contract.ObjecaoService;
import br.com.pactosolucoes.commons.data.PagingAndSortingData;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@AllArgsConstructor
public @Service class ObjecaoServiceImpl<T> implements ObjecaoService<T> {

    private ObjecaoRepository repository;
    private ModelMapper mapper;

    private HistoricoObjecaoDefinitivaFactory factory;

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    public T salvar(ObjecaoDTO objecao) {
        return (T) repository.save(mapper.map(objecao, ObjecaoEntity.class));
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    public T atualizar(Integer id, ObjecaoDTO objecao) {
        repository.findById(id).orElseThrow(DataNotMatchException::new);
        objecao.setCodigo(id);
        return (T) repository.save(mapper.map(objecao, ObjecaoEntity.class));
    }

    @Override
    @LogExecution
    public void excluir(Integer id) {
        repository.delete(ObjecaoEntity.builder().codigo(id).build());
    }

    @Override
    @LogExecution
    public void excluir(List<Integer> ids) {
        repository.deleteAllById(ids);
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    @Cacheable(value = "objecao-todos")
    public T buscarTodos(PagingAndSortingData pageAndSortingData) {
        return (T) repository.findAll(pageAndSortingData.get());
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    @Cacheable(value = "objecao-tipo", key="#tipo")
    public T buscarPorTipo(PagingAndSortingData pageAndSortingData, String tipo) {
        return (T) repository
                    .findByTipogrupoContainingIgnoreCase(tipo, pageAndSortingData.get());
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    @Cacheable(value = "objecao-somenteAtivos-tipo")
    public T buscarSomenteAtivosPorTipo(PagingAndSortingData pageAndSortingData, String tipo) {
        return (T) repository
                .findByTipogrupoContainingIgnoreCaseAndAtivo(tipo, true, pageAndSortingData.get());
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    @Cacheable(value = "objecao-grupo", key="#grupo")
    public T buscarPorGrupo(PagingAndSortingData pageAndSortingData, String grupo) {
        return (T) repository
                .findByGrupoContainingIgnoreCase(grupo, pageAndSortingData.get());
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    @Cacheable(value = "objecao-descricao", key="#descricao")
    public T buscarPorDescricao(PagingAndSortingData pageAndSortingData, String descricao) {
        return (T) repository
                .findByDescricaoContainingIgnoreCase(descricao, pageAndSortingData.get());
    }

    @Override
    public String buscarPorTipoReagendamento() {
        return "Reagendamento para dia: ####### data do agendamento";
    }

    @Override
    public String buscarPorTipoAgendamento() {
        return "getAgendaVO().qualResultadoAgendamento(); &&&&& efetuar consulta no servico";
    }

    @Override
    @LogExecution
    public String buscarPorTipoOB(HistoricoContatoDTO historicoContato) {
        String retorno;
        if (historicoContato.getObjecao() != null && historicoContato.getObjecao() > 0) {
            retorno = ("Objeção: " + repository
                    .findById(historicoContato.getObjecao())
                    .orElseThrow(DataNotMatchException::new)
                    .getDescricao());
        } else if (historicoContato.getFase() != null && !historicoContato.getFase().equalsIgnoreCase(FasesCRMEnum.VISITA_RECORRENTE.getSigla())) {
            retorno = "Simples Registro";
        } else {
            retorno = "Simples Registro";
        }
        return retorno;
    }

    @Override
    public String buscarPorTipoSR(String tipoContato) {
        HashMap<String, String> contatos = new HashMap<>();
        contatos.put("CS", "Envio de SMS");
        contatos.put("AP", "Envio APP");
        contatos.put("SR", "Simples Registro");

        return Objects.nonNull(contatos.get(tipoContato)) ?
                                contatos.get(tipoContato) :
                                contatos.get("SR");
    }

    @Override
    @LogExecution
    @ObjectMapper(ObjecaoVO.class)
    @Cacheable(value = "objecao-id", key="#id")
    public T buscarPorId(Integer id) {
       return (T) repository.findById(id);
    }

    @Override
    @LogExecution
    public void salvarObjecaoDefinitiva(HistoricoObjecaoDTO dto) {
        if (dto.getTipocontato().equalsIgnoreCase("OB")) {
            Optional<ObjecaoEntity> opt = repository.findById(dto.getCodigoObjecao());
            if (opt.isPresent() && opt.get().getTipogrupo().equalsIgnoreCase("OD")) {
                factory.get(dto).salvarObjecao(dto.getCodigoClienteIndicadoPassivo(), dto.getCodigoObjecao());
            }
        }
    }
}
