package br.com.pacto.ms.contato.base.data.domain;


import br.com.pacto.ms.contato.avulso.data.domain.EmpresaEntity;
import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Entity
@Data
@Table(name = "plano", schema = "public")
public class PlanoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "descricao", length = 50)
    private String descricao;

    @Column(name = "bolsa")
    private boolean bolsa;

    @Column(name = "ingressoate")
    private Date ingressoate;

    @Column(name = "vigenciaate")
    private Date vigenciaate;

    @Column(name = "vigenciade")
    private Date vigenciade;

    @ManyToOne
    @JoinColumn(name = "empresa")
    private EmpresaEntity empresa;

    @Column(name = "percentualmultacancelamento")
    private Double percentualmultacancelamento;

    @Column(name = "descontoantecipado")
    private Integer descontoantecipado;

    @Column(name = "recorrencia")
    private boolean recorrencia;

    @Column(name = "site")
    private Boolean site;

    @Column(name = "renovavelautomaticamente")
    private Boolean renovavelautomaticamente;

    @Column(name = "vendacreditotreino")
    private Boolean vendacreditotreino;

    @Column(name = "qtdsemanasano")
    private Integer qtdsemanasano;

    @Column(name = "pontos")
    private Integer pontos;

    @Column(name = "creditotreinonaocumulativo")
    private Boolean creditotreinonaocumulativo;

    @Column(name = "permitiracessosomentenaempresavendeucontrato")
    private Boolean permitiracessosomentenaempresavendeucontrato;

    @Column(name = "planopersonal")
    private Boolean planopersonal;

    @Column(name = "convidadospormes")
    private Integer convidadospormes;

    @Column(name = "idexterno", length = 12)
    private String idexterno;

    @ManyToOne
    @JoinColumn(name = "planotipo")
    private PlanoTipoEntity planotipo;

    @Column(name = "creditosessao")
    private boolean creditosessao;

    @Column(name = "maximovezesparcelar")
    private Integer maximovezesparcelar;

    @Column(name = "aceitadescontoextra")
    private Boolean aceitadescontoextra;

    @Column(name = "restringevendaporcategoria")
    private Boolean restringevendaporcategoria;

    @Column(name = "permitiracessoredeempresa")
    private Boolean permitiracessoredeempresa;

    @Column(name = "diadomesdescontoboletopagantecipado")
    private Integer diadomesdescontoboletopagantecipado;

    @Column(name = "permitepagarcomboleto")
    private Boolean permitepagarcomboleto;

    @Column(name = "valordescontoboletopagantecipado")
    private Double valordescontoboletopagantecipado;

    @Column(name = "porcentagemdescontoboletopagantecipado")
    private Double porcentagemdescontoboletopagantecipado;

    @OneToOne(mappedBy = "plano", cascade = CascadeType.ALL, orphanRemoval = true)
    @ToString.Exclude
    private PlanoRecorrenciaEntity planoRecorrencia;

//    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
//    @JoinColumn(name = "plano")
//    private List<PlanoModalidadeEntity> modalidades;
//
//    @OneToMany(mappedBy = "plano", cascade = CascadeType.ALL, orphanRemoval = true)
//    @ToString.Exclude
//    private List<PlanoComposicaoEntity> composicoes;
//
//    @OneToMany(mappedBy = "plano", cascade = CascadeType.ALL, orphanRemoval = true)
//    @ToString.Exclude
//    private List<PlanoProdutoSugeridoEntity> planoprodutossugeridos;
//
//    @OneToMany(mappedBy = "plano", cascade = CascadeType.ALL, orphanRemoval = true)
//    private List<PlanoExcecaoEntity> excecoes;
}
