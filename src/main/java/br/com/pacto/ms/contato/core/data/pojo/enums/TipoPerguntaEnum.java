package br.com.pacto.ms.contato.core.data.pojo.enums;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum TipoPerguntaEnum {

    OBJETIVA(0,"Pergunta Objetiva"),
    DISSERTATIVA(1,"Pergunta Dissertativa"),
    SIMPLES(2,"Mensagem Simples");

    private Integer codigo;
    private String descricao;


    public static List<ObjectNode> getListTipoPerguntaEnuM(String filter){
        ObjectMapper map = new ObjectMapper();
        List<ObjectNode> list = new ArrayList<>();
        Arrays.stream(TipoPerguntaEnum.values()).forEach(item ->{
                    ObjectNode obj = map.createObjectNode();
                    if (!filter.equals("") && item.getDescricao().toLowerCase().contains(filter)) {
                        obj.put("codigo", item.getCodigo());
                        obj.put("descricao", item.getDescricao());
                        list.add(obj);
                        return;
                    }else{
                        if(filter.equals("")){
                            obj.put("codigo", item.getCodigo());
                            obj.put("descricao", item.getDescricao());
                            list.add(obj);
                        } }
                }
        );
        return list;
    }

    public static TipoPerguntaEnum getTipoPergunta(Integer codigo) {
        return Arrays.stream(TipoPerguntaEnum.values())
                .filter(tp -> tp.getCodigo().compareTo(codigo) == 0)
                .findFirst().orElseGet(null);
    }
}
