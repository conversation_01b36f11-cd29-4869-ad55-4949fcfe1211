package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Collection;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "objecao", schema = "public")
public class ObjecaoEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "descricao", nullable = true, length = -1)
    private String descricao;
    @Basic
    @Column(name = "grupo", nullable = true, length = 50)
    private String grupo;
    @Basic
    @Column(name = "comentario", nullable = true, length = -1)
    private String comentario;
    @Basic
    @Column(name = "tipogrupo", nullable = true, length = 2)
    private String tipogrupo;
    @Basic
    @Column(name = "ativo", nullable = true)
    private Boolean ativo;

}
