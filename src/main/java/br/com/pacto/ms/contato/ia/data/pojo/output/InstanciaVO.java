package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InstanciaVO {
    @Schema(description = "Id da instância", example = "AFE21312DSAFJ123")
    String id;
    @Schema(description = "Token da instância", example = "AFE21312DSAFJ123")
    String token;
    @Schema(description = "identificador da instancia", example = "AFE21312DSAFJ123")
    Long due;
    @Schema(description = "Boolean value de status que retorna da requiscao", example = "true")
    Boolean value;
}
