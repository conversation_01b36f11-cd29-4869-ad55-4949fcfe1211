package br.com.pacto.ms.contato.avulso.data.pojo.input;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.*;

import java.sql.Timestamp;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ReceptivoFiltro {
    @Parameter(
        name = "dia",
        description = "Dia do cadastro do Receptivo.",
        example = "2024-09-10T12:26:22.683+00:00"
    )
    private Timestamp dia;
    private Integer objecao;
    private Integer colaboradorResponsavel;
    private Integer pagina;
    private Integer quantidade;
}
