package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.pojo.output.QuestionarioVO;
import br.com.pacto.ms.contato.base.service.contract.QuestionarioService;
import br.com.pacto.ms.contato.base.data.repository.QuestionarioRepository;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
public @Service class QuestionarioServiceImpl<T> implements QuestionarioService {
    private QuestionarioRepository repository;

    @Override
    @LogExecution
    @ObjectMapper(QuestionarioVO.class)
    public T buscarPortipo( String tipo, String nome) {
        return (T) repository
                .findQuestionarioEntitiesByTipoquestionarioAndNomeinterno(tipo,nome);
    }

    @Override
    @LogExecution
    @ObjectMapper(QuestionarioVO.class)
    public T buscarPortipo( String tipo) {
        return (T) repository
                .findQuestionarioEntitiesByTipoquestionario(tipo);
    }
}
