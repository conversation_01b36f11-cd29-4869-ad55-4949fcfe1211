package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import lombok.*;


@AllArgsConstructor
@Getter
@Setter
@NoArgsConstructor
@Builder
public class ConfiguracaoCrmFaseIAVO {

    private Integer codigo;
    private FasesCRMEnum fase;
    private String descricao;
    private Boolean habilitar;
    private Boolean habilitarconfigia;
    private Integer codigoMetaExtra;
    private String nomeMetaExtra;
    private Integer codigoEmpresa;

    public ConfiguracaoCrmFaseIAEntity toEntity() {
        return ConfiguracaoCrmFaseIAEntity.builder()
                .fase(this.getFase())
                .codigo(this.getCodigo())
                .descricao(this.getDescricao())
                .habilitar(this.getHabilitar())
                .nomeMetaExtra(this.getNomeMetaExtra())
                .codigoEmpresa(this.getCodigoEmpresa())
                .codigoMetaExtra(this.getCodigoMetaExtra())
                .build();
    }
}