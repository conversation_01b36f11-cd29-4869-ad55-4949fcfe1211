package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "telefone", schema = "public")
public class TelefoneEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String tipotelefone;
    private String numero;
    private String descricao;
    private String ddi;
    private Boolean recebersms;
    private Boolean usarnonodigitowapp;
    private  int pessoa;

    @Basic
    @Column(name = "tipotelefone")
    public String getTipotelefone() {
        return tipotelefone;
    }

    @Basic
    @Column(name = "numero")
    public String getNumero() {
        return numero;
    }


    @Column(name = "codigo")
    public Integer getCodigo() {
        return codigo;
    }

    @Basic
    @Column(name = "descricao")
    public String getDescricao() {
        return descricao;
    }

    @Basic
    @Column(name = "ddi")
    public String getDdi() {
        return ddi;
    }

    @Basic
    @Column(name = "recebersms")
    public Boolean getRecebersms() {
        return recebersms;
    }


    @Basic
    @Column(name = "usarnonodigitowapp")
    public Boolean getUsarnonodigitowapp() {
        return usarnonodigitowapp;
    }


    @Basic
    @Column(name = "pessoa")
    public int getPessoa() {
        return pessoa;
    }

}
