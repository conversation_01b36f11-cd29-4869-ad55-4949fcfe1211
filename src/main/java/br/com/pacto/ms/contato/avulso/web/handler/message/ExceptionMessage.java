package br.com.pacto.ms.contato.avulso.web.handler.message;

import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.OK;

import br.com.pactosolucoes.utils.enums.APIMessages;
import org.springframework.http.HttpStatus;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum ExceptionMessage implements APIMessages {


	META_FECHADA_EXCEPTION(-100, "metafechada.exception", BAD_REQUEST),
	EMAIL_EXCEPTION(-101, "email.exception", BAD_REQUEST),
	TELEFONE_EXCEPTION(-101, "telefone.exception", BAD_REQUEST),
	TERMO_BLOQUIEIO_EXCEPTION(-102, "termo.bloqueio.exception", BAD_REQUEST),
	TOTAL_CARACTERES_EXCEPTION(-103, "total.caracteres.exception", BAD_REQUEST),
	AGENDAMENTO_EXISTENTE_EXCEPTION(-104, "agendamento.existente.exceptio", BAD_REQUEST),
	DIAS_LIMITE_AGENDAMENTO_EXCEPTION(-105,"agendamento.limite.dias", BAD_REQUEST ),
	HORA_MAIOR_QUE_23_EXCEPTION(-106, "agendamento.hora.maior", BAD_REQUEST),
	MINUTO_MAIOR_QUE_59_EXCEPTION(-107, "agendamento.minuto.maior", BAD_REQUEST),
	AGENDAMENTO_DIA_INFORMADO_EXCEPTION(-108, "agendamento.dia.informado", BAD_REQUEST),
	HISTORICO_CONTATO_SMS_EMPRESA_NAO_POSSUI_SMS_EXCEPTION(-109, "historicocontatoc.sms.sua.empresa.nao.possui.sms", BAD_REQUEST),
	HISTORICO_CONTATO_SMS_TELEFONE_NAO_INFORMADO(-110, "historicocontatoc.telefone.nao.informado", BAD_REQUEST),
	HISTORICO_CONTATO_SMS_NAO_ENVIADO(-111, "historicocontatoc.sms.nao.enviado", BAD_REQUEST),
	HISTORICO_CONTATO_EMAIL_NAO_INFORMADO(-112, "historicocontatoc.email.nao.informado", BAD_REQUEST),
	HISTORICO_CONTATO_APP_NOTIFICACAO_NAO_ENVIADA(-112, "historicocontatoc.app.notificacao.nao.enviada", BAD_REQUEST),
	HORARIO_INICIAL_INVALIDO(-114, "faixahorario.horario.inicial", BAD_REQUEST),
	HORARIO_FINAL_INVALIDO(-115, "faixahorario.horario.final", BAD_REQUEST),
	PERIODO_EXISTENTE(-116, "faixahorario.periodo.existente", BAD_REQUEST),
	FAIXAHORARIO_CONCOMITANTES(-117, "faixahorario.concomitantes", BAD_REQUEST),
	FAIXAHORARIO_EXCLUIDO(-118, "faixahorario.excluido", OK),
	CONFIGURACAO_DIAS_METAS_EXCLUSAO(-113, "configuracao.dias.metas.exclusao", BAD_REQUEST),
	CONFIGURACAO_POSVENDA_EXCLUIDA(-113, "posvenda.notificacao.excluido", OK),
	CONFIGURACAO_POSVENDA_EXCLUSAO_NEGADA(-114, "posvenda.notificacao.exclusao.negada", BAD_REQUEST),
	CONFIGURACAO_IA_EXCLUSAO_NEGADA(-115, "ia.notificacao.exclusao.negada", BAD_REQUEST),
	CLIENTE_NAO_ENCONTRADO(-119, "cliente.nao.encontrado", BAD_REQUEST),
	;

	private Integer code;
	private String message;
	private HttpStatus statusCode;
	
	

}
