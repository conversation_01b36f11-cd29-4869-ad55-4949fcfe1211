package br.com.pacto.ms.contato.avulso.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UsuarioSimplesVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Boolean administrador;

	private String nome;

	private String tipousuario;

	private Date ultimoacesso;

	private String username;

}