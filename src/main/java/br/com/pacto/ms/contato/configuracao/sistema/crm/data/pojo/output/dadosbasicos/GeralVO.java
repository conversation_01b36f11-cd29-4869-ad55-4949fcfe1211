package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.PositiveOrZero;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeralVO {

    private Boolean abertosabado;
    private Boolean abertodomingo;
    private Boolean batermetatodasacoes;
    private Boolean apresentarcolaboradoresportipocolaborador;
    private Boolean apresentarcolaboradoresinativos;
    private Boolean agendamentoparametaconsultor;
    private Boolean autorrenovavelentrarenovacao;

    @PositiveOrZero
    private Short nrdiascontarresultado;

    @PositiveOrZero
    private Integer nrdiasparaclientepreverenovacao;

    @PositiveOrZero
    private Integer nrdiasparaclientepreverenovacaomaiorummes;

    @PositiveOrZero
    private Integer nrdiasparaclientepreveperda;

    @PositiveOrZero
    private Integer nrcreditostreinorenovar;
}
