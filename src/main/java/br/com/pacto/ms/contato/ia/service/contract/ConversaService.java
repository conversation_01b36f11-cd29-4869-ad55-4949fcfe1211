package br.com.pacto.ms.contato.ia.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.ia.data.pojo.input.HistoricoContatoAIDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.IndicadorDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ContextosAlunoVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.HistoricoContatoAIVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponseStatusVO;

public interface ConversaService {

    ResponsePactoConversasVO<ContextosAlunoVO> inciarConversa(Integer cliente, ResumoEmpresaVO resumoEmpresaVO, String nomeFase, String nomeMetaExtra, Integer codigoMetaExtra);

    ResponsePactoConversasVO<ContextosAlunoVO> inciarPosVendaConversaPorCpf(String cpf);

    ResponsePactoConversasVO<ContextosAlunoVO> inciarPosVendaConversaPorCpf(String cpf, String linkAppTreino);

    HistoricoContatoAIVO salvarConversa(HistoricoContatoAIDTO dto);

    ResponsePactoConversasVO<ResponseStatusVO> receberIndicadoresBI(IndicadorDTO dto);

}
