package br.com.pacto.ms.contato.configuracao.sistema.empresa.service.impl;

import java.util.Date;

import br.com.pactosolucoes.utils.enums.DiasDaSemanaEnum;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import br.com.pacto.ms.contato.avulso.data.repository.EmpresaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.ConfiguracaoCRMDadosBasicosVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.pojo.input.EmpresaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.pojo.output.EmpresaDataUtilVO;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.pojo.output.EmpresaVO;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.service.contract.ConfiguracaoEmpresaService;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.service.contract.FeriadoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;

import static br.com.pactosolucoes.utils.DateUtils.*;

@Service
@AllArgsConstructor
public class ConfiguracaoEmpresaServiceImpl <T> implements ConfiguracaoEmpresaService <T> {

    private ConfiguracaoSistemaCrmService configuracaosistemacrmService;
    private FeriadoService feriadoService;
    private EmpresaRepository empresaRepository;

    private ModelMapper modelMapper;

    @Override
    @LogExecution
    @ObjectMapper(EmpresaDataUtilVO.class)
    public T obterDataCalculadaDiasUteis(Date dataContrato, boolean anterior, int nrDias, int empresa) {
        EmpresaDTO empresadto= modelMapper.map(this.obterEmpresa(empresa), EmpresaDTO.class);
        int nrDiasCalcular = 0;
        Date dataCalcular = dataContrato;
        while (nrDiasCalcular < nrDias) {
            if (anterior) {
                dataCalcular = obterDataAnterior(dataCalcular, 1);
            } else {
                dataCalcular = obterDataFutura2(dataCalcular, 1);
            }

            if (verificarDiaAcademiaAberta(dataCalcular, empresadto)){
                nrDiasCalcular++;
            }
        }
        EmpresaDataUtilVO dateUtil = EmpresaDataUtilVO.builder().dataUtil(dataCalcular).build();
        return (T) dateUtil;
    }

    @Override
    @ObjectMapper(EmpresaDTO.class)
    public T obterEmpresa(int empresa) {
        return (T) empresaRepository.findById(empresa).get();
    }

    private Boolean verificarDiaAcademiaAberta(Date dia, EmpresaDTO empresa) {
        Boolean aberto = Boolean.TRUE;
        ConfiguracaoCRMDadosBasicosVO confg =  (ConfiguracaoCRMDadosBasicosVO) configuracaosistemacrmService.buscarDadosBasicos();
        if (getDiaDaSemana(dia, DiasDaSemanaEnum.SABADO) && ! confg.getGeral().getAbertosabado()) {
            aberto = Boolean.FALSE;
        }
        if (getDiaDaSemana(dia, DiasDaSemanaEnum.DOMINGO) && ! confg.getGeral().getAbertosabado()) {
            aberto = Boolean.FALSE;
        }
        if (feriadoService.validarFeriadoPorEmpresaParaCalculoAberturaMeta(empresa, dia)) {
            aberto = Boolean.FALSE;
        }
        return aberto;
    }

}
