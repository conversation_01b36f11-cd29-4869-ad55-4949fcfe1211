package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class QrCodeVO {
    @Schema(description = "Bolean validador pra qrcode da requisição.", example = "true")
    private boolean isQrCode;
    @Schema(description = "Conteudo em base64 da imagem.", example = "data:image/png;base64...")
    private String imageContent;
    @Schema(description = "Status da conexao com a instancia", example = "connected")
    private String status;
}
