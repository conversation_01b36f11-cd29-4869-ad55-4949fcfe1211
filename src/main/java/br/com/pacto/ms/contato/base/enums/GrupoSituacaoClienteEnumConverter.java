package br.com.pacto.ms.contato.base.enums;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class GrupoSituacaoClienteEnumConverter implements AttributeConverter<GrupoSituacaoEnum, String> {

    @Override
    public String convertToDatabaseColumn(GrupoSituacaoEnum situacaoClienteEnum) {
        if(situacaoClienteEnum == null){
            return null;
        }
        return situacaoClienteEnum.getCodigo();
    }

    @Override
    public GrupoSituacaoEnum convertToEntityAttribute(String id) {
        return GrupoSituacaoEnum.findByCodigo(id);
    }
}
