package br.com.pacto.ms.contato.log.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class LogResumidoVO {
    @Schema(description = "Código do log", example = "1")
    private Integer codigo;
    @Schema(description = "Nome da entidade", example = "configuracaosistema")
    private String nomeEntidade;
    @Schema(description = "Descrição da entidade", example = "Configuração do sistema")
    private String nomeEntidadeDescricao;
    @Schema(description = "Chave primária da entidade", example = "1")
    private String chavePrimaria;
    @Schema(description = "Nome do campo alterado", example = "nrdiasavencer")
    private String nomeCampo;
    @Schema(description = "Valor do campo antes da alteração", example = "28")
    private String valorCampoAnterior;
    @Schema(description = "Valor do campo após a alteração", example = "10")
    private String valorCampoAlterado;
}
