package br.com.pacto.ms.contato.base.data.pojo.output;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
public class MailingAgendamentoVO {
    private int codigo;
    private Integer ocorrencia;
    private String cron;
    private Timestamp ultimaexecucao;
    private Timestamp datainicial;
}
