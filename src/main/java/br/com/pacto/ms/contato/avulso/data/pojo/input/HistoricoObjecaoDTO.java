package br.com.pacto.ms.contato.avulso.data.pojo.input;

import br.com.pacto.ms.contato.core.data.pojo.enums.TipoHistoricoObjecaoEnum;
import lombok.*;

import javax.validation.constraints.PositiveOrZero;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class HistoricoObjecaoDTO extends HistoricoContatoDTO {


    private TipoHistoricoObjecaoEnum tipo;

    @PositiveOrZero
    private Integer codigoClienteIndicadoPassivo;

}
