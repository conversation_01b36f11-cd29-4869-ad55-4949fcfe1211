package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogFieldDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "faixahorarioacessocliente", schema = "public")
@EntityListeners(LogListener.class)
public class ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Column
    @LogFieldDescription("Descrição da faixa de horário")
    private String descricao;
    @Column
    @LogFieldDescription("Início do horário de acesso")
    private String horarioinicio;
    @Column
    @LogFieldDescription("Fim do horário de acesso")
    private String horariofim;

}
