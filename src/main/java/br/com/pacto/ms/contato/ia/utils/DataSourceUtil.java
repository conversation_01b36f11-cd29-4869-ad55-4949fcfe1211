package br.com.pacto.ms.contato.ia.utils;

import br.com.pactosolucoes.commons.data.domain.OAMDCompany;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;

import javax.persistence.EntityManager;
import javax.persistence.EntityManagerFactory;
import javax.persistence.EntityTransaction;
import javax.sql.DataSource;
import java.util.function.Function;

public class DataSourceUtil {

    public static DataSource createDataSource(OAMDCompany companyConfig) {
        DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName("org.postgresql.Driver");
        dataSource.setUrl(buildJdbcUrl(companyConfig));
        dataSource.setUsername(companyConfig.getUserBD());
        dataSource.setPassword(companyConfig.getPasswordBD());
        return dataSource;
    }

    private static String buildJdbcUrl(OAMDCompany companyConfig) {
        return String.format("jdbc:postgresql:" +
                        "//%s:%d/%s",
                companyConfig.getHostBD(),
                companyConfig.getPorta(),
                companyConfig.getNomeBD());
    }

    public static EntityManager getEntityManager(DataSource dataSource, String... pacotesEntidades) {
        LocalContainerEntityManagerFactoryBean emf = new LocalContainerEntityManagerFactoryBean();
        emf.setDataSource(dataSource);
        emf.setPackagesToScan(pacotesEntidades);
        emf.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
        emf.afterPropertiesSet();
        return emf.getObject().createEntityManager();
    }

    public static void executeWithDynamicDataSource(DataSource dataSource, Runnable task, String... pacotesEntidades) {
        LocalContainerEntityManagerFactoryBean emf = new LocalContainerEntityManagerFactoryBean();
        emf.setDataSource(dataSource);
        emf.setPackagesToScan(pacotesEntidades);
        emf.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
        emf.afterPropertiesSet();

        EntityManagerFactory entityManagerFactory = emf.getObject();
        EntityManager entityManager = entityManagerFactory.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        try {
            transaction.begin();
            task.run();
            transaction.commit();
        } catch (Exception e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            throw new RuntimeException("Erro ao executar com DataSource dinâmico", e);
        } finally {
            entityManager.close();
        }
    }

    public static <T> T executeWithDynamicDataSourceV2(DataSource dataSource, Function<EntityManager, T> task, String... pacotesEntidades) {
        LocalContainerEntityManagerFactoryBean emf = new LocalContainerEntityManagerFactoryBean();
        emf.setDataSource(dataSource);
        emf.setPackagesToScan(pacotesEntidades);
        emf.setJpaVendorAdapter(new HibernateJpaVendorAdapter());
        emf.afterPropertiesSet();

        EntityManagerFactory entityManagerFactory = emf.getObject();
        EntityManager entityManager = entityManagerFactory.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();
        try {
            transaction.begin();
            T result = task.apply(entityManager);
            transaction.commit();
            return result;
        } catch (Exception e) {
            if (transaction.isActive()) {
                transaction.rollback();
            }
            throw new RuntimeException("Erro ao executar com DataSource dinâmico", e);
        } finally {
            entityManager.close();
        }
    }

}
