package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.pojo.output.PlanoAnuidadeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ContextoPlanoVendaConsultorVO {
    @Schema(description = "Código do plano", example = "1")
    private Integer codigo;
    @Schema(description = "Define se o plano é plano de bolsa ou não", example = "true")
    private boolean bolsa;
    @Schema(description = "Dia que define a data inicial que o plano pode ser vendido", example = "2023-01-01")
    private Date ingressoate;
    @Schema(description = "Dia de validade final do plano", example = "2022-11-01")
    private Date vigenciaate;
    @Schema(description = "Dia de validade inicial do plano", example = "2023-11-01")
    private Date vigenciade;
    @Schema(description = "Nome do plano", example = "PLANO FAZ TUDO")
    private String descricao;
    @Schema(description = "Percentual de multa por cancelamento", example = "10.5")
    private Double percentualmultacancelamento;
    @Schema(description = "Desconto para pagamento antecipado", example = "5")
    private Integer descontoantecipado;
    @Schema(description = "Indica se o plano tem recorrência", example = "true")
    private boolean recorrencia;
    @Schema(description = "Venda de crédito para treino", example = "true")
    private Boolean vendacreditotreino;
    @Schema(description = "Pontos acumulados no plano", example = "100")
    private Integer pontos;
    @Schema(description = "Permitir acesso somente na empresa que vendeu o contrato", example = "true")
    private Boolean permitiracessosomentenaempresavendeucontrato;
    @Schema(description = "Plano é personalizado", example = "false")
    private Boolean planopersonal;
    @Schema(description = "Convidados por mês", example = "2")
    private Integer convidadospormes;
    @Schema(description = "Dia do mês para desconto no pagamento antecipado via boleto", example = "5")
    private Integer diadomesdescontoboletopagantecipado;
    @Schema(description = "Permite o pagamento do plano via boleto", example = "true")
    private Boolean permitepagarcomboleto;
    @Schema(description = "Valor de desconto para pagamento antecipado via boleto", example = "50.00")
    private Double valordescontoboletopagantecipado;
    @Schema(description = "Porcentagem de desconto para pagamento antecipado via boleto", example = "10.0")
    private Double porcentagemdescontoboletopagantecipado;
    private List<PlanoAnuidadeVO> anuidades;
    private List<ContextoPlanoModalidadeVO> modalidades;
    private List<ContextoPlanoComposicaoVO> composicoes;
    private List<ContextoPlanoProdutoSugeridoVO> planoprodutossugeridos;
    private List<PlanoExcecaoVO> excecoes;
}
