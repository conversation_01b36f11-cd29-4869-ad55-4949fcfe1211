package br.com.pacto.ms.contato.avulso.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.input.AgendaDTO;

public interface AgendaService <T> {
    void validarHorario(AgendaDTO agendaDTO);
    void verificaExisteAgendamentoDiaHoraMinuto(AgendaDTO agendaDTO);
    void validaDiasLimiteAgendamento(AgendaDTO agendaDTO);
    void validaAgendamento(AgendaDTO agendaDTO);
    T <PERSON>lvar(AgendaDTO agendaDTO);
}
