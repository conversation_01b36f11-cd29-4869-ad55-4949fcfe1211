package br.com.pacto.ms.contato.base.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;

public class TelefoneException extends CustomException {
    private static final long serialVersionUID = -9192943455755903832L;

    public TelefoneException() {
    }

    public TelefoneException(ExceptionMessageVO... messagesParamVO) {
        super(messagesParamVO);
    }

    public TelefoneException(String message) {
        super(message);
    }

    public TelefoneException(String message, ExceptionMessageVO... messagesParamVO) {
        super(message, messagesParamVO);
    }

    public TelefoneException(Throwable cause) {
        super(cause);
    }

    public TelefoneException(Throwable cause, ExceptionMessageVO... messagesParamVO) {
        super(cause, messagesParamVO);
    }

    public TelefoneException(String message, Throwable cause) {
        super(message, cause);
    }

    public TelefoneException(String message, Throwable cause, ExceptionMessageVO... messagesParamVO) {
        super(message, cause, messagesParamVO);
    }

}
