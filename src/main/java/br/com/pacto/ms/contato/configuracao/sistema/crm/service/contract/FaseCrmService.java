package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;

import java.util.List;

public interface FaseCrmService<T, DTO> {
    T consultar(List<TipoFaseCRMEnum> tipoFaseCRMEnum);

    T consultarCodName(List<TipoFaseCRMEnum> tipoFaseCRMEnum, String descricao);

    T consultarOrdenacaoMetas();

    T consultarOrdenacaoMetasPadrao();
}
