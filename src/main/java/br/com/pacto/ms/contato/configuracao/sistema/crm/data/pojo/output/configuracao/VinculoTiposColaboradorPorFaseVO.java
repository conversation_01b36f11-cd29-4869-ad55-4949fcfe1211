package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.TipoColaboradorVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.modelmapper.ModelMapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class VinculoTiposColaboradorPorFaseVO {

        @Schema(description = "Fase do crm")
        private FaseVO fase;
        @Schema(description = "Tipos de colaboradores responsável pela fase do crm")
        private List<TipoColaboradorVO> tiposColaboradores;

        public VinculoTiposColaboradorPorFaseVO(FasesCRMEnum fasesCRMEnum, TipoColaboradorEnum... tiposColaboradoresEnum) {
                ModelMapper modelMapper = new ModelMapper();
                this.fase = modelMapper.map(fasesCRMEnum,FaseVO.class);
                this.tiposColaboradores = new ArrayList<>();
                if(tiposColaboradoresEnum.length > 0){
                        this.tiposColaboradores = Arrays.stream(tiposColaboradoresEnum)
                                .map(tipo -> modelMapper.map(tipo,TipoColaboradorVO.class))
                                .collect(Collectors.toList());
                }
        }
}
