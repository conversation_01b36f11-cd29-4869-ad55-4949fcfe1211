package br.com.pacto.ms.contato.core.data.pojo.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum FasesCRMEnum {

    AGENDAMENTO("Agendamentos Presenciais", 1, "agendamentos_hoje.png", "AG", "Meta Agendados", 2, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:Agendamentos", "Para atingir essa meta deve marcar o comparecimento, reagendar, fechar uma negociação com o aluno ou uma objeção.", "Agend.<br>Presenciais"),
    LIGACAO_AGENDADOS_AMANHA("Agendados de Amanhã", 14, "ligacao_amanha.png", "LA", "Ligação para Agendados de Amanhã", 3, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:Agendados_de_Amanhã", "Para atingir essa meta é necessário efetuar um contato ou reagendar o aluno.", "Agend. de<br>Amanhã"),
    VINTE_QUATRO_HORAS("Visitantes 24h", 2, "vinteQuatroHoras.png", "HO", "Meta 24 Horas",1, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:24_Horas", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Visitantes<br>24h"),
    RENOVACAO("Renovação", 3, "renovacoes.png", "RE", "Meta Renovação", 4, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:Renovação", "Para atingir essa meta o resultado do contato deve gerar um agendamento de aula, visita ou ligação.", "Renovação"),
    DESISTENTES("Desistentes", 11, "perdas.png", "PE", "Meta Desistentes", 5, TipoFaseCRMEnum.VENDAS, "Indicador_de_Retenção:Desistentes", "Para atingir essa meta, o resultado do contato deve ser algum agendamento ou objeção.", "Desistentes"),
    VISITANTES_ANTIGOS("Visitantes Antigos", 19, "visitantes_antigos.png", "VA", "Meta Visitantes Antigos", 6, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:Visitantes_Antigos", "Para atingir essa meta, basta realizar qualquer agendamento ou uma objeção.", "Visitantes<br>Antigos"),
    EX_ALUNOS("Ex-Alunos", 17, "agendamentos_hoje.png", "EX", "Meta Ex-Alunos", 7, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:Ex-Alunos", "Para atingir essa meta, o resultado do contato deve ser um agendamento ou uma objeção.", "Ex-Alunos"),
    INDICACOES("Indicações", 7, "indicacoes.png", "IN", "Meta Indicados", 8, TipoFaseCRMEnum.VENDAS, "Indicador_de_Vendas:Indicações", "Para atingir essa meta é necessário registrar uma indicação e realizar qualquer agendamento ou uma objeção.", "Indicações"),

    CONVERSAO_INDICADOS("Conversão de Indicados", 6, "conversao_agendados.png", "CI", "Meta Conversão de Indicados", 9, TipoFaseCRMEnum.VENDAS, "", "", "Conversão<br>Indicados"),
    CONVERSAO_AGENDADOS("Conversão de Agendados", 13, "conversao_agendados.png", "CV", "Meta Conversão de Agendados", 10, TipoFaseCRMEnum.VENDAS, "", "", "Conversão<br>Agendados"),
    CONVERSAO_EX_ALUNOS("Conversão de Ex-Alunos", 18, "conversao_agendados.png", "CE", "Meta Conversão de Ex-Alunos", 11, TipoFaseCRMEnum.VENDAS, "", "", "Conversão<br>Ex-Alunos"),
    CONVERSAO_VISITANTES_ANTIGOS("Conversão de Visitantes Antigos", 5, "conversao_agendados.png", "CA", "Meta Conversão de Visitantes Antigos", 12, TipoFaseCRMEnum.VENDAS, "", "Para atingir essa meta o resultado do contato deve ser agendamento ou objeção.", "Conversão<br>Visitantes Antigos"),
    CONVERSAO_DESISTENTES("Conversão de Desistentes", 21, "conversao_agendados.png", "CD", "Meta Conversão de Desistentes", 13, TipoFaseCRMEnum.VENDAS, "", "", "Conversão<br>Desistentes"),
    CONVERSAO_PASSIVO("Conversão de Receptivo", 20, "conversao_agendados.png", "CT", "Meta Conversão de Receptivo", 26, TipoFaseCRMEnum.VENDAS, "", "", "Conversão<br>Receptivo"),

    GRUPO_RISCO("Grupo de Risco", 8, "grupo_de_risco.png", "RI", "Meta Grupo de Risco", 8, TipoFaseCRMEnum.RETENCAO, "Indicador_de_Retenção:Grupo_de_Risco", "Para atingir essa meta basta realizar qualquer contato, agendamento ou objeção.", "Grupo Risco"),
    VENCIDOS("Vencidos", 12, "vencidos.png", "VE", "Meta Vencidos", 9, TipoFaseCRMEnum.RETENCAO, "Indicador_de_Vendas:Vencidos", "Para atingir essa meta deve realizar qualquer agendamento, renovação do contrato ou objeção.", "Vencidos"),
    POS_VENDA("Pós Venda", 4, "pos_vendas.png", "PV", "Meta Pós Venda", 10, TipoFaseCRMEnum.RETENCAO, "Indicador_de_Retenção:Pós-Venda", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Pós Venda"),
    FALTOSOS("Faltosos", 10, "faltantes.png", "FA", "Meta Faltosos", 11, TipoFaseCRMEnum.RETENCAO, "Indicador_de_Retenção:Faltosos", "Para atingir essa meta basta realizar qualquer contato, agendamento ou objeção.", "Faltosos"),
    ANIVERSARIANTES("Aniversariantes", 9, "aniversariantes.png", "AN", "Meta Aniversariantes", 12, TipoFaseCRMEnum.RETENCAO, "Indicador_de_Retenção:Aniversariantes", "Para atingir essa meta basta realizar qualquer contato, agendamento ou objeção.", "Aniversariantes"),

    ULTIMAS_SESSOES("Últimas Sessões", 15, "sessoes.png", "SF", "Últimas Sessões", 14, TipoFaseCRMEnum.ESTUDIO, "AgendaStudio:SessoesFinais", "", "Últimas Sessões"),
    SEM_AGENDAMENTO("Sessões sem agenda", 16, "sem_agendamento.png", "SA", "Sem agendamento", 15, TipoFaseCRMEnum.ESTUDIO, "AgendaStudio:SemAgendamento", "", "Sessões sem agenda"),


    //NOVOS INDICADORES

    AGENDAMENTOS_LIGACOES("Agendamentos de Ligações", 23, "sem_agendamento.png", "AL", "Sem agendamento", 23, TipoFaseCRMEnum.VENDAS, "AgendaStudio:SemAgendamento", "Ligações agendadas para o dia.", "Agendamentos de Ligações"),
    INDICACOES_SEM_CONTATO("Indicações sem Contato", 22, "sem_agendamento.png", "IS", "Sem agendamento", 22, TipoFaseCRMEnum.INDICADORES, "AgendaStudio:SemAgendamento", "Para realizar esse indicador, basta realizar qualquer agendamento ou uma objeção.", "Indicações sem Contato"),
    PASSIVO("Receptivo", 24, "sem_agendamento.png", "CP", "Sem agendamento", 24, TipoFaseCRMEnum.INDICADORES, "AgendaStudio:SemAgendamento", "Registrar os contatos que a academia recebe para que posteriormente possa ser realizada ações de vendas.", "Receptivo"),

    ALUNO_GYMPASS("Aluno Wellhub", 26, "conversao_agendados.png", "GY", "Aluno GymPass", 26, TipoFaseCRMEnum.VENDAS, "AlunoGymPass", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Aluno GymPass"),

    //CRM-EXTRA
    CRM_EXTRA("Meta Extra", 25, "sem_agendamento.png", "CR", "Sem agendamento", 25, TipoFaseCRMEnum.CRMEXTRA, "AgendaStudio:SemAgendamento", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Meta Extra"),

    //LEAD
    LEADS_HOJE("Leads Hoje", 27, "vinteQuatroHoras.png", "LH", "Meta Leads",27, TipoFaseCRMEnum.LEAD, "Indicador_de_Vendas:24_Horas", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Leads Hoje"),

    LEADS_ACUMULADAS("Leads Acumuladas", 28, "vinteQuatroHoras.png", "LC", "Meta Leads",28, TipoFaseCRMEnum.LEAD, "Indicador_de_Vendas:24_Horas", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma objeção.", "Leads Acumulado"),

    VISITA_RECORRENTE("Visitas recorrentes", 29, "visita.png", "VR", "Visitas recorrentes", 29, TipoFaseCRMEnum.VENDAS, "", "Registrar as visitas recorrentes", "Visitas recorrentes"),

    CONVERSAO_LEAD("Conversão de Lead", 30, "conversao_agendados.png", "CL", "Meta Conversão de Lead", 30, TipoFaseCRMEnum.VENDAS, "", "", "Conversão<br>Lead"),

    ALUNO_ULTIMO_ACESSO_GYMPASS("Últ. Acesso Wellhub", 31, "visitantes_antigos.png", "UG", "Últ. Acesso GymPass", 31, TipoFaseCRMEnum.VENDAS, "Último Acesso GymPass", "Para atingir essa meta é necessário realizar qualquer contato, agendamento ou objeção.", "Último Acesso GymPass"),
    FILA_ESPERA_TURMA_CRM("Fila de espera turma", 32, "visitantes_antigos.png", "FT", "Fila de espera turma", 32, TipoFaseCRMEnum.RETENCAO, "Fila de espera turma", "Para atingir essa meta o resultado do contato deve ser um agendamento ou uma obje็ใo.", "Fila de espera turma");

    private String descricao;
    private Integer codigo;
    private String imagem;
    private String sigla;
    private String identificador;
    private Integer ordemTotalizador;
    private TipoFaseCRMEnum tipoFase;
    private String urlWiki;
    private String objetivo;
    private String descricaoCurtaBI;

    public String getName(){
        return this.name();
    }

    public static FasesCRMEnum getFase(Integer codigo) {
        if(codigo == null){
            return null;

        }
        
        return Arrays.stream(FasesCRMEnum.values())
                .filter(f -> f.getCodigo().compareTo(codigo) == 0)
                .findFirst().orElse(null);
    }

    public static FasesCRMEnum getFasePorSigla(String sigla) {
        if(sigla == null || sigla.trim().isEmpty()){
            return null;
        }

        return Arrays.stream(FasesCRMEnum.values())
                .filter(f -> f.getSigla().equalsIgnoreCase(sigla))
                .findFirst()
                .orElse(null);
    }

    public static String getIdentificador(String sigla) {
        FasesCRMEnum fase = getFasePorSigla(sigla);
        return Objects.nonNull(fase) ? fase.getDescricao() : "";
    }

    public static FasesCRMEnum getFasePorDescricaoBI(String descricaoCurtaBI) {
        return Arrays.stream(FasesCRMEnum.values())
                .filter(f -> f.getDescricaoCurtaBI().equalsIgnoreCase(descricaoCurtaBI))
                .findFirst().orElseGet(null);
    }

    public static String retornarSiglas(){
        StringBuilder siglas = new StringBuilder();
        for (FasesCRMEnum object : FasesCRMEnum.values()) {
            if (siglas.toString().equals("")){
                siglas.append("'").append(object.getSigla()).append("'");
            }else{
                siglas.append(",'").append(object.getSigla()).append("'");
            }
        }
        return siglas.toString();
    }

    public static FasesCRMEnum getFaseByName(String name) {
        if(name == null || name.trim().isEmpty()){
            return null;
        }
        return Arrays.stream(FasesCRMEnum.values())
                .filter(f -> f.name().equalsIgnoreCase(name))
                .findFirst().orElse(null);
    }

    public static  List<ObjectNode>  getListFaseCRM(String filter){
        ObjectMapper map = new ObjectMapper();
        List<ObjectNode> list = new ArrayList<>();
        Arrays.stream(FasesCRMEnum.values()).forEach(item ->{
                        ObjectNode obj = map.createObjectNode();
                        if (!filter.equals("") && item.getDescricao().toLowerCase().contains(filter)) {
                            obj.put("sigla", item.getSigla());
                            obj.put("descricao", item.getDescricao());
                            list.add(obj);
                            return;
                        }else{
                            if(filter.equals("")){
                                obj.put("sigla", item.getSigla());
                                obj.put("descricao", item.getDescricao());
                                list.add(obj);
                            } }
                   }
            );
        return list;
    }

    @JsonCreator
    public static FasesCRMEnum fromJson(Object input) {
        if (input instanceof String) {
            return fromJsonByName((String) input);
        } else if (input instanceof Map) {
            return fromJsonBySigla((Map<String, Object>) input);
        }
        throw new IllegalArgumentException("Formato inválido para enum FasesCRMEnum: " + input);
    }

    public static FasesCRMEnum fromJsonBySigla(Map<String, Object> object) {
        String codigo = (String) object.get("sigla");
        return getFasePorSigla(codigo);
    }

    public static FasesCRMEnum fromJsonByName(String name) {
        return valueOf(name);
    }

}
