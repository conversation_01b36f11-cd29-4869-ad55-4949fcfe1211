package br.com.pacto.ms.contato.base.data.domain;

import javax.persistence.*;
import lombok.Data;

@Entity
@Data
@Table(name = "horario", schema = "public")
public class HorarioEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "horariodefault")
    private Boolean horariodefault;

    @Column(name = "domingo")
    private Boolean domingo;

    @Column(name = "segunda")
    private Boolean segunda;

    @Column(name = "terca")
    private Boolean terca;

    @Column(name = "quarta")
    private Boolean quarta;

    @Column(name = "quinta")
    private Boolean quinta;

    @Column(name = "sexta")
    private Boolean sexta;

    @Column(name = "sabado")
    private Boolean sabado;

    @Column(name = "livre")
    private Boolean livre;

    @Column(name = "descricao", length = 45, nullable = false)
    private String descricao;

    @Column(name = "correspondencia_zd", length = 100)
    private String correspondenciaZd;

    @Column(name = "ativo", columnDefinition = "boolean default true")
    private Boolean ativo;
}