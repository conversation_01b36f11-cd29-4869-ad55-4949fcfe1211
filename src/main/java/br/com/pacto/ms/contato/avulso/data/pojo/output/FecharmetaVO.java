package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FecharmetaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Timestamp dataregistro;

	private String identificadormeta;

	private String justificativa;

	private float meta;

	private float metaatingida;

	private Boolean metacalculada;

	private String nomemeta;

	private float porcentagem;

	private Integer repescagem;

	private AberturametaVO aberturametaBean;

	private MaladiretaVO maladiretaBean;

	private List<FecharmetadetalhadoVO> fecharmetadetalhados;

}