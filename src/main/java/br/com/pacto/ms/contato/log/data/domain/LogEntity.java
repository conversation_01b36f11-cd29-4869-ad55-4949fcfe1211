package br.com.pacto.ms.contato.log.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "log", schema = "public")
public class LogEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;
    private String nomeEntidade;
    private String nomeEntidadeDescricao;
    private String chavePrimaria;
    private String chavePrimariaEntidadeSubordinada;
    private String nomeCampo;
    private String nomeCampoDescricao;
    private String valorCampoAnterior;
    private String valorCampoAlterado;
    @Temporal(TemporalType.TIMESTAMP)
    private Date dataAlteracao;
    private String responsavelAlteracao;
    private String operacao;
    private Integer pessoa;
    private Integer cliente;
    private String tags;

}

