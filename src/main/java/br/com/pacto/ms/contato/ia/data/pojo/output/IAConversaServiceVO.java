package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IAConversaServiceVO {

    @Schema(description = "Configuração IA habilitada", example = "true")
    private Boolean configuracaoIaHabilitada;

    @Schema(description = "Módulo ZW habilitado", example = "true")
    private Boolean moduloZwHabilitado;

    @Schema(description = "Módulo CRM habilitado", example = "true")
    private Boolean moduloCrmHabilitado;

    @Schema(description = "Módulo Treino habilitado", example = "true")
    private Boolean moduloTreinoHabilitado;

}
