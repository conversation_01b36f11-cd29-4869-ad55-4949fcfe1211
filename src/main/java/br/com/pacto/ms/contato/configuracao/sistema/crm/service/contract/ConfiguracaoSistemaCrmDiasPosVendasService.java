package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoSistemaCrmDiasPosVendasDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoSistemaCrmDiasPosVendasVO;

import javax.validation.Valid;
import java.util.List;

public interface ConfiguracaoSistemaCrmDiasPosVendasService<T, DTO> {
    List<ConfiguracaoSistemaCrmDiasPosVendasVO> consutlar();
    T incluir(ConfiguracaoSistemaCrmDiasPosVendasDTO configuracaoSistemaCrmDiasPosVendasDTO);
    void excluir(Integer codigo);
    T getTipoColaborador();

    T alterar(Integer codigo, @Valid ConfiguracaoSistemaCrmDiasPosVendasDTO dto);
}
