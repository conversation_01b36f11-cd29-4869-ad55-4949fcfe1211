package br.com.pacto.ms.contato.configuracao.sistema.crm.service.exception;

import br.com.pactosolucoes.commons.exception.CustomException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST)
public class ProcessarMetaDiariaException extends CustomException {

    public ProcessarMetaDiariaException(String message) {
        super(message);
    }

    public ProcessarMetaDiariaException(int status, String message) {
        super(status, message);
    }

}
