package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCampanhaDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponseStatusVO;

import java.util.List;

 public interface ConfiguracaoCampanhaService {
     List<ConfiguracaoCampanhaDTO> obterCampanhas(Integer empresa);

     ResponseStatusVO salvarCampanha(ConfiguracaoCampanhaDTO configuracaoCampanhaDTO);

     ResponseStatusVO alterarCampanha(String id, ConfiguracaoCampanhaDTO dto);

     void excluirCampanha(String id, Integer empresa);
}