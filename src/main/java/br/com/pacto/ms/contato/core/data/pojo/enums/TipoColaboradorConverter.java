package br.com.pacto.ms.contato.core.data.pojo.enums;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class TipoColaboradorConverter implements AttributeConverter<TipoColaboradorEnum, String> {

    @Override
    public String convertToDatabaseColumn(TipoColaboradorEnum tipoColaboradorEnum) {
        if(tipoColaboradorEnum == null){
            return null;
        }
        return tipoColaboradorEnum.getSigla();
    }

    @Override
    public TipoColaboradorEnum convertToEntityAttribute(String sigla) {
        if(sigla == null){
            return null;
        }
        return TipoColaboradorEnum.getTipo(sigla);
    }
}

