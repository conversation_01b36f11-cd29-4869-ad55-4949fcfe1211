package br.com.pacto.ms.contato.avulso.service.exception;

import br.com.pactosolucoes.commons.exception.CustomException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class CadastroLeadException extends CustomException {

    public CadastroLeadException(String message) {
        super(message);
    }
}
