package br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class ConfiguracaoIAException extends CustomException {

    public ConfiguracaoIAException(String message) {
        super(message);
    }

    public ConfiguracaoIAException(ExceptionMessageVO... messagesParamVO) {
        super(messagesParamVO);
    }
    public ConfiguracaoIAException(String message, Throwable cause) {
        super(message, cause);
    }

}
