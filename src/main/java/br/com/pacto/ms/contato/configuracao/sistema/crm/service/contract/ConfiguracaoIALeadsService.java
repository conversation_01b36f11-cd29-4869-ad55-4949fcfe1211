package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.LeadsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ScheduleNotificationsDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;

public interface ConfiguracaoIALeadsService {

    ResponsePactoConversasVO<ScheduleNotificationsDTO> enviarNotificacaco(LeadsDTO dto);

    ResponsePactoConversasVO<ScheduleNotificationsDTO> obterNotificacoes(Integer codigoEmpresa);
}
