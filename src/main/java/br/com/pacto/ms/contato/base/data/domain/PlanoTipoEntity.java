package br.com.pacto.ms.contato.base.data.domain;

import lombok.Data;

import javax.persistence.*;

@Entity
@Data
@Table(name = "planotipo", schema = "public")
public class PlanoTipoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "nome")
    private String nome;

    @Column(name = "ativo")
    private boolean ativo;

    @Column(name = "tipo", length = 10)
    private String tipo;
}