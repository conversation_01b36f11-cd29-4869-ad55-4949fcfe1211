package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.ContratoEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ContratoRepository extends PagingAndSortingRepository<ContratoEntity, Integer> {

    @Query("select c from ContratoEntity c where c.pessoa = :pessoa")
    Optional<List<ContratoEntity>> consultarPorPessoa(Integer pessoa);
}
