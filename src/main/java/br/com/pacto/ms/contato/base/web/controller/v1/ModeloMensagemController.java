package br.com.pacto.ms.contato.base.web.controller.v1;

import br.com.pacto.ms.contato.base.data.pojo.output.ModeloMensagemVO;
import br.com.pacto.ms.contato.base.service.contract.ModeloMensagemService;
import br.com.pacto.ms.contato.core.data.pojo.enums.MeioEnvioEnum;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Validated
@RestController
@RequestMapping("/v1/generico/modeloMensagem")
@Tag(name = "Modelo de Mensagem")
public class ModeloMensagemController  extends BaseController {


    @Autowired
    private RequestService requestService;

    @Autowired
    private ModeloMensagemService<ModeloMensagemVO> hcService;

    @Operation(summary = "Busca o modelo de mensagem para ser disparado ",
            description = "Busca o modelo de mensagem para ser disparado")
    @LogExecution
    @GetMapping("modeloSms")
    public ResponseEntity<?> buscarPorMeioEvnio() {
        return super.finish( hcService.buscarModeloMensagemPorMeioEnvio((short) MeioEnvioEnum.SMS.getCodigo()));
    }

    @Operation(summary = "Busca o modelo de mensagem para ser disparado ",
            description = "Busca o modelo de mensagem para ser disparado")
    @LogExecution
    @GetMapping("modeloEmail")
    public ResponseEntity<?> buscarPorMeioEvnioEmail() {
        return super.finish( hcService.buscarModeloMensagemPorMeioEnvio((short) MeioEnvioEnum.EMAIL.getCodigo()));
    }

    @Operation(summary = "Busca o modelo de mensagem para ser disparado  através do código",
            description = "Busca o modelo de mensagem para ser disparado  através do código")
    @LogExecution
    @GetMapping("modeloSms/codigo/{codigo}")
    public ResponseEntity<?> buscarPorCodigo(@PathVariable int codigo) {
        return super.finish( hcService.buscarModeloMensagemPorCodigo(codigo));
    }
}
