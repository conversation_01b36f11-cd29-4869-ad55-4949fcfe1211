package br.com.pacto.ms.contato.base.web.controller.v1;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CLIENTES;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CLIENTES_DESCRICAO;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.COLABORADOR;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.COLABORADOR_DESCRICAO;


import br.com.pacto.ms.contato.base.data.domain.ColaboradorResumo;
import br.com.pacto.ms.contato.base.data.pojo.output.EmailVO;
import br.com.pacto.ms.contato.base.data.pojo.output.PessoaVO;
import br.com.pacto.ms.contato.base.service.contract.ColaboradorService;
import br.com.pacto.ms.contato.base.service.contract.EmailService;
import br.com.pacto.ms.contato.base.service.contract.PessoaService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

@Validated
@RestController
@RequestMapping("/v1/generico/pessoa")
public class PessoaController extends BaseController  {

    @Autowired
    private RequestService requestService;

    @Autowired
    private PessoaService<PessoaVO> hcService;
    @Autowired
    private EmailService<EmailVO> hcServiceEmail;

    @Autowired
    private ColaboradorService<ColaboradorResumo> hcServiceColaborador;


    @Tag(name = CLIENTES, description = CLIENTES_DESCRICAO)
    @Operation(summary = "Busca informações do cliente por matricula ",
            description = "Busca informações do cliente por matricula ")
    @LogExecution
    @GetMapping("/matricula/{matricula}")
    public ResponseEntity<?> buscaPessoaMatricula(@PathVariable int matricula) {
        return super.finish( hcService.buscarPorMatricula(matricula));
    }

    @Tag(name = CLIENTES, description = CLIENTES_DESCRICAO)
    @Operation(summary = "Busca email do cliente por matricula ",
            description = "Busca email do cliente por matricula ")
    @LogExecution
    @GetMapping("/email/{matricula}")
    public ResponseEntity<?> buscaEmailaMatricula(@PathVariable int matricula) {
        return super.finish( hcServiceEmail.buscarPorMatricula(matricula));
    }

@Tag(name = COLABORADOR, description = COLABORADOR_DESCRICAO)
    @Operation(summary = "Busca o colaborador  ",
            description = "Busca os dados do colaborador")
    @LogExecution
    @GetMapping("/colaborador")
    public ResponseEntity<?> buscarColaborador() {
        JSONObject filter = requestService.getCurrentConfiguration().getFilters();
        if (Objects.nonNull(filter) && filter.has("quicksearchValue") && !filter.get("quicksearchValue").toString().equals("null")) {
            return super.finish( hcServiceColaborador.buscarColaboradorPorNome(filter.get("quicksearchValue").toString()));
        }
        return super.finish( hcServiceColaborador.buscarColaboradorPorNome("%%"));

    }
}
