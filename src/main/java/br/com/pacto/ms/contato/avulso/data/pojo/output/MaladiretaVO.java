package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import br.com.pacto.ms.contato.base.data.domain.MaladiretaEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MaladiretaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Integer codaberturameta;

	private String configs;

	private Boolean contatoavulso;

	private Timestamp datacriacao;

	private Timestamp dataenvio;

	private Integer diasposvenda;

	private Boolean enviohabilitado;

	private Integer evento;

	private Boolean excluida;

	private String faseenvio;

	private Boolean importarlista;

	private Integer intervalodias;

	private Integer meiodeenvio;

	private String mensagem;

	private Boolean metaextraindividual;

	private Integer modelomensagem;

	private String opcoes;

	private Integer quantidademaximaacessos;

	private Integer quantidademinimaacessos;

	private Integer questionario;

	private Boolean smsmarketing;

	private String sql;

	private Integer tipoagendamento;

	private Integer tipocancelamento;

	private String tipoconsultormetaextraindividual;

	private Integer tipopergunta;

	private String titulo;

	private Boolean todasempresas;

	private Date vigenteate;

	private List<FecharmetaVO> fecharmetas;

	private List<HistoricoContatoVO> historicocontatos;

	private List<MailingagendamentoVO> mailingagendamentos;

	private List<MailingfiltroVO> mailingfiltros;

	private EmpresaVO empresaBean;

	private UsuarioVO usuario;

	public static MaladiretaVO toVO(MaladiretaEntity maladiretaEntity) {
		return MaladiretaVO.builder()
				.codigo(maladiretaEntity.getCodigo())
				.titulo(maladiretaEntity.getTitulo())
				.codaberturameta(maladiretaEntity.getCodaberturameta())
				.configs(maladiretaEntity.getConfigs())
				.contatoavulso(maladiretaEntity.getContatoavulso())
				.datacriacao(maladiretaEntity.getDatacriacao())
				.dataenvio(maladiretaEntity.getDataenvio())
				.diasposvenda(maladiretaEntity.getDiasposvenda())
				.enviohabilitado(maladiretaEntity.getEnviohabilitado())
				.evento(maladiretaEntity.getEvento())
				.excluida(maladiretaEntity.getExcluida())
				.faseenvio(maladiretaEntity.getFaseenvio())
				.importarlista(maladiretaEntity.getImportarlista())
				.intervalodias(maladiretaEntity.getIntervalodias())
				.mensagem(maladiretaEntity.getMensagem())
				.metaextraindividual(maladiretaEntity.getMetaextraindividual())
				.modelomensagem(maladiretaEntity.getModelomensagem())
				.opcoes(maladiretaEntity.getOpcoes())
				.quantidademaximaacessos(maladiretaEntity.getQuantidademaximaacessos())
				.quantidademinimaacessos(maladiretaEntity.getQuantidademinimaacessos())
				.questionario(maladiretaEntity.getQuestionario())
				.smsmarketing(maladiretaEntity.getSmsmarketing())
				.sql(maladiretaEntity.getSql())
				.tipoagendamento(maladiretaEntity.getTipoagendamento())
				.build();
	}
}
