/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */
package br.com.pacto.ms.contato.core.data.pojo.enums;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum TipoContatoCRM {

    CONTATO_TELEFONE("TE", "pct-phone", "tipocontatocrm.te.descricao"),
    CONTATO_EMAIL("EM", "pct-mail", "tipocontatocrm.em.descricao"),
    CONTATO_PESSOAL("PE", "pct-user", "tipocontatocrm.pe.descricao"),
    LIGACAO_SEM_CONTATO("LC", "pct-x", "tipocontatocrm.lc.descricao"),
    CONTATO_SMS("CS", "pct-message-square\n", "tipocontatocrm.cs.descricao"),
    CONTATO_WHATSAPP("WA", "pct-whatsapp", "tipocontatocrm.wa.descricao"),
    TODOS_CONTATOS("TD", "", "tipocontatocrm.td.descricao"),
    OBJECAO("OB", "pct-thumbs-down", "tipocontatocrm.ob.descricao"),
    OUTROS("OT", "pct-more-horizontal", "tipocontatocrm.ot.descricao"),
    CONTATO_APP("AP", "pct-smartphone", "tipocontatocrm.ap.descricao"),
    CONTATO_GYMBOT("BT", "pct-whatsapp", "tipocontatocrm.gb.descricao"),
    CONTATO_GYMBOT_PRO("GP", "pct-whatsapp", "tipocontatocrm.gp.descricao");

    private String sigla;
    private String icone;
    private String descricao;


    public static TipoContatoCRM getContatoPorSigla(String sigla) {
        return Arrays.stream(TipoContatoCRM.values())
                .filter(f -> f.getSigla().equalsIgnoreCase(sigla))
                .findFirst().orElseGet(null);
    }

    public static ObjectNode getContatoPorSiglaAsJson(String sigla) {
        TipoContatoCRM contato;
        if (sigla.isEmpty()) {
            contato = TipoContatoCRM.OUTROS;
        } else {
            contato = getContatoPorSigla(sigla);
        }
        ObjectMapper map = new ObjectMapper();
        ObjectNode obj = map.createObjectNode();
        obj.put("sigla", contato != null ? contato.getSigla() : "");
        obj.put("icone", contato != null ? contato.getIcone() : "");
        obj.put("descricao", contato != null ? contato.getDescricao() : "");
        return obj;
    }
}
