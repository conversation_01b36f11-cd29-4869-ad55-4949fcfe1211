package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.time.LocalDate;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "lead", schema = "public")
public class LeadEntity {

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "passivo", referencedColumnName = "codigo", foreignKey = @ForeignKey(name = "fk_lead_passivo"))
    private PassivoEntity passivo;

    @ManyToOne
    @JoinColumn(name = "cliente", referencedColumnName = "codigo", foreignKey = @ForeignKey(name = "fk_lead_cliente"))
    private ClienteEntity cliente;

    private String uuid = "";
    private String urlRD = "";
    private String email = "";

    private Integer empresa;

    private Integer indicado;
    private TipoLeadEnum tipo = null;
    private String dados;
    private LocalDate dataRegistro;
}
