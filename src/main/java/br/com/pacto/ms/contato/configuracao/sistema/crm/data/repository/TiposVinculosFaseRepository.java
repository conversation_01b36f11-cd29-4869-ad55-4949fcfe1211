package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.TiposVinculosFaseEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface TiposVinculosFaseRepository extends PagingAndSortingRepository<TiposVinculosFaseEntity, Integer> {

    @Query("SELECT t FROM TiposVinculosFaseEntity t ORDER BY t.fase")
    List<TiposVinculosFaseEntity> findOrderByFase();

    @Query("SELECT t FROM TiposVinculosFaseEntity t WHERE t.fase = :fase ORDER BY t.fase")
    List<TiposVinculosFaseEntity> findByFase(@Param("fase") FasesCRMEnum fase);

    @Query("SELECT t FROM TiposVinculosFaseEntity t WHERE t.fase = :fase " +
            "and t.tipoColaborador = :tipoColaborador " +
            "ORDER BY t.fase")
    Optional<List<TiposVinculosFaseEntity>> findByFaseAndTipoColaborador(@Param("fase") FasesCRMEnum fase, @Param("tipoColaborador") TipoColaboradorEnum tipoColaborador);

    @Query("SELECT t FROM TiposVinculosFaseEntity t WHERE t.fase IN :fasesCRMEnums ORDER BY t.fase")
    List<TiposVinculosFaseEntity> findByFasesCRMEnums(List<FasesCRMEnum> fasesCRMEnums);

    @Modifying
    @Transactional
    @Query("DELETE FROM TiposVinculosFaseEntity t WHERE t.fase = :fase")
    void deleteByFase(@Param("fase") FasesCRMEnum fase);
}
