package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.log.data.repository.UsuarioRepository;
import br.com.pactosolucoes.commons.util.annotation.ValueTransformContract;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@NoArgsConstructor
public class UsuarioValueTransform implements ValueTransformContract {

    static private UsuarioRepository usuarioRepository;

    @Autowired
    public void setUsuarioRepository(UsuarioRepository usuarioRepository) {
        this.usuarioRepository = usuarioRepository;
    }

    @Override
    public String transform(Object value) {
        if(value == null){
            return null;
        }

        return usuarioRepository.findById((Integer) value).get().getNome();
    }
}
