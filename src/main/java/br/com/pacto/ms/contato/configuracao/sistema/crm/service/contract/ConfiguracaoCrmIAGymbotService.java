package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.comuns.data.pojo.output.DepartamentoVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmIADTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoGymbotVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.GymbotTokenVO;


import java.util.List;

public interface ConfiguracaoCrmIAGymbotService<T, DTO> {

    GymbotTokenVO atualizaToken(Integer codigoEmpresa, String gymbotToken);

    GymbotTokenVO consultarTokenGymbot(Integer codigoEmpresa);

    List<DepartamentoVO> obterDepartamentos(Integer codigoEmpresa, String gymbotToken);

    ConfiguracaoCrmIADTO salvarConfiguracaoGymbot(ConfiguracaoGymbotVO configuracaoGymbotVO);

    void removerConfiguracaoGymbot(Integer codigoEmpresa);
}
