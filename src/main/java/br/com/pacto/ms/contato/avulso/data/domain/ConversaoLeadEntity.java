package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.Date;

@Data
@NoArgsConstructor
@Entity
@Table(name = "conversaolead")
public class ConversaoLeadEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private String identificador;
    private Date dataCriacao;
    private Date dataLancamento;
    private boolean tratada;
    private String props;
    @ManyToOne
    @JoinColumn(name = "lead", referencedColumnName = "codigo")
    private LeadEntity lead;

    @ManyToOne
    @JoinColumn(name = "responsavel", referencedColumnName = "codigo")
    private UsuarioEntity responsavel;

}
