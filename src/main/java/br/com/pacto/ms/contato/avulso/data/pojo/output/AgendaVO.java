package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgendaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Integer alunohorarioturma;

	private Integer codigoprofessor;

	private Integer conviteaulaexperimental;

	private Timestamp dataagendamento;

	private Timestamp datacomparecimento;

	private Timestamp datalancamento;

	private Boolean gympass;

	private String hora;

	private String minuto;

	private Integer modalidade;

	private Integer reposicao;

	private String tipoagendamento;

	private String tipoprofessor;

	private List<HistoricoContatoVO> historicocontatos;

}
