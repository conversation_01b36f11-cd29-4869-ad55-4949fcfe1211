package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.QuestionarioPerguntaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface QuestionarioPerguntaRepository extends PagingAndSortingRepository<QuestionarioPerguntaEntity, Integer> {

    @Query("select qp from QuestionarioPerguntaEntity qp where qp.questionario = :questionario")
    List<QuestionarioPerguntaEntity> buscarPerguntas(Integer questionario);

}
