package br.com.pacto.ms.contato.base.enums;

import java.util.Arrays;
import java.util.Optional;

public enum SituacaoEnum {

    MATRICULA("AT", "Matrícula"),
    REMATRICULA("RE", "Rematrícula"),
    RENOVACAO("RN", "Renovação"),
    TRANSFERENCIA("TF", "Transferência"),
    ATIVO("AT", "Ativo"),
    ATESTADO("AE", "Atestado"),
    FERIAS("CR", "Férias"),
    CANCELADO("CA", "Cancelado"),
    FERIAS_ALT("FE", "Férias"),
    TRANCADO("TR", "Trancado"),
    CONGELADO("CO", "Congelado"),
    INATIVO("IN", "Inativo");

    private String codigo;
    private String descricao;

    SituacaoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static SituacaoEnum findByCodigo(String codigo) {
        if(codigo == null || codigo.trim().equals("")){
            return null;
        }

        Optional<SituacaoEnum> first = Arrays.stream(values())
                .filter(situacaoContratoEnum -> situacaoContratoEnum.getCodigo().equals(codigo))
                .findFirst();

        return first.orElse(null);

    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
