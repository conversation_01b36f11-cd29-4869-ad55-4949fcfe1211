package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.IndicadoEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.input.IndicadoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.IndicadoVO;
import br.com.pacto.ms.contato.avulso.data.repository.IndicadoRepository;
import br.com.pacto.ms.contato.avulso.service.contract.IndicadoService;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class IndicadoServiceImpl<T> implements IndicadoService<T> {

    private IndicadoRepository repository;
    private ModelMapper mapper;

    @Override
    @LogExecution
    @ObjectMapper(IndicadoVO.class)
    public T salvarObjecao(Integer codigoIndicado, @NonNull Integer objecao) {
        IndicadoEntity entity = repository.findById(codigoIndicado).orElseThrow(DataNotMatchException::new);
        entity.setObjecao(objecao);
        return (T) repository.save(entity);
    }

    @Override
    @LogExecution
    @ObjectMapper(IndicadoVO.class)
    public T salvar(IndicadoDTO dto) {
        return (T) repository.save(mapper.map(dto, IndicadoEntity.class));
    }
}
