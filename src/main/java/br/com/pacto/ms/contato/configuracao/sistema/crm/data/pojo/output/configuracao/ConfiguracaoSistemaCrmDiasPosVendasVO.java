package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.TipoColaboradorVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.modelmapper.ModelMapper;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfiguracaoSistemaCrmDiasPosVendasVO {
    @Schema(description = "Código da configuração", example = "1")
    private Integer codigo;
    @Schema(description = "Configuração sistemacrm", example = "1")
    private Integer configuracaosistemacrm;
    @Schema(description = "Número de dias", example = "1")
    private Integer nrdia;
    @Schema(description = "Descrição da configuração", example = "VALIDAR LANÇAMENTO DO PROGRAMA DE TREINO")
    private String descricao;
    @Schema(description = "Indica o status sa configuração", example = "true")
    private Boolean ativo;
    @Schema(description = "Responsável pelo contato", example = "Responsáveis pelas fases")
    private TipoColaboradorVO tipoColaborador;
}
