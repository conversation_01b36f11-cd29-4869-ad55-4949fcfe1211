package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoPDFIADTO {

    @Schema(description = "ID do Configuração PDF IA", example = "1")
    private Integer codigo;

    @Schema(description = "Link para Dowload do PDF vinculado", example = "www.")
    private String linkPdf;

    @Schema(description = "Condigo da Empresa ", example = "1")
    private Integer codigoEmpresa;

    @Schema(description = "Data da última atualização", example = "")
    private Date dataUltimaAtualizacao;

}
