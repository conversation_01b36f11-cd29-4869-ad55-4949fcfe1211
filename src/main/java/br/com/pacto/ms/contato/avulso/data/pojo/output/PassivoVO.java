package br.com.pacto.ms.contato.avulso.data.pojo.output;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PassivoVO implements Serializable {

    private Integer codigo;

    private String nome;

    private String telefoneresidencial;

    private String telefonecelular;

    private String telefonetrabalho;

    private Integer responsavelcadastro;

    private Timestamp dia;

    private String observacao;

    private Integer colaboradorresponsavel;

    private String email;

    private Integer evento;

    private Integer cliente;

    private Integer empresa;

    private String nomeconsulta;

    private Integer contrato;

    private Integer objecao;

    private Boolean lead;

    private Short origemsistema;

    private Boolean metaextra;
}
