package br.com.pacto.ms.contato.ia.web.controller.v1;

import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.ia.data.pojo.output.AlunoVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import br.com.pacto.ms.contato.ia.service.contract.ContextoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@Tag(name = "Inteligência artificial",
        description = "Consultas de contextos aplicados a inteligência artificial do modulo CRM")
@RestController
@RequestMapping(value = "/v1/ia")
@SecurityRequirement(name = "Authorization bearer")
public class IAContextController extends BaseController {

    @Autowired
    private ContextoService iaContextoService;
    @Autowired
    private RequestService requestService;

    @Operation(summary = "Consultar contexto da empresa",
            description = "Consulta informações sobre a empresa para contextualizar o agente de ia do crm")
    @LogExecution
    @GetMapping(value = "/contextos/empresa")
    public ResponseEntity<ContextoEmpresaVO> consultarContextosPorEmpresa(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ContextoEmpresaVO>) super.finish(this.iaContextoService.consultarContextoEmpresa(empresa));
    }

    @Operation(summary = "Atualizar contexto da empresa na api de IA Pacto conversas")
    @LogExecution
    @PutMapping(value = "/contextos/empresa")
    public ResponseEntity<ResponsePactoConversasVO<ContextoEmpresaVO>> atualizarContextoEmpresa(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "rede", description = "Indica se a empresa faz parte de uma rede", example = "false") @RequestParam(required = false, defaultValue = "false") Boolean rede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<ContextoEmpresaVO>>) super.finish(this.iaContextoService.atualizarContextoEmpresa(empresa,rede));
    }

    @Operation(summary = "Consultar contexto de turmas de uma empresa",
            description = "Consulta informações sobre as turmas para contextualizar o agente de ia do crm")
    @LogExecution
    @GetMapping(value = "/contextos/turmas")
    public ResponseEntity<List<ContextoTurmaVO>> consultarContextosTurmasPorEmpresa(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<List<ContextoTurmaVO>>) super.finish(this.iaContextoService.consultarContextoTurmasPorEmpresa(empresa));
    }

    @Operation(summary = "Atualizar contexto de turmas na api de IA Pacto conversas")
    @LogExecution
    @PutMapping(value = "/contextos/turmas")
    public ResponseEntity<ResponsePactoConversasVO<List<ContextoTurmaVO>>> atualizarContextoTurmas(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "enviarContextoRede", description = "Indica se deve atualizar o contexto de toda rede da academia.", example = "false") @RequestParam(required = false, defaultValue = "true") Boolean enviarContextoRede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<List<ContextoTurmaVO>>>) super.finish(this.iaContextoService.atualizarContextoTurmas(empresa));
    }

    @Operation(summary = "Consultar contexto de planos da empresa",
            description = "Consulta informações os planos da empresa para contextualizar o agente de ia do crm")
    @LogExecution
    @GetMapping(value = "/contextos/planos")
    public ResponseEntity<ContextosPlanosVO> consultarContextoPlanos(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ContextosPlanosVO>) super.finish(this.iaContextoService.consultarContextosPlanos(empresa));
    }

    @Operation(summary = "Atualizar contexto de planos na api de IA Pacto conversas")
    @LogExecution
    @PutMapping(value = "/contextos/planos")
    public ResponseEntity<ResponsePactoConversasVO<ContextosPlanosVO>> atualizarContextoPlanos(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "enviarContextoRede", description = "Indica se deve atualizar o contexto de toda rede da academia.", example = "false") @RequestParam(required = false, defaultValue = "true") Boolean enviarContextoRede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<ContextosPlanosVO>>) super.finish(this.iaContextoService.atualizarContextoPlanos(empresa));
    }

    @Operation(summary = "Consultar contexto de plano",
            description = "Consulta informações sobre um plano especifico")
    @LogExecution
    @GetMapping(value = "/contextos/planos/{plano}")
    public ResponseEntity<ContextoPlanoVendaConsultorVO> consultarContextoPlano(
            @Parameter(name = "plano", description = "Código da plano", example = "72") @PathVariable Integer plano) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ContextoPlanoVendaConsultorVO>) super.finish(this.iaContextoService.consultarContextoPlano(plano));
    }

    @Operation(summary = "Consultar contexto de um aluno",
            description = "Consulta informações sobre o aluno, seus contratos, treinos, avaliação fisica, aulas, pagamentos e histórico de contato")
    @LogExecution
    @GetMapping(value = "/contextos/aluno")
    public ResponseEntity<ContextosAlunoVO> consultarContextosPorAlunoEmpresa(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "cliente", description = "Código de cliente", example = "150") Integer cliente,
            @Parameter(name = "contextoTreino", description = "Indica se deve ser consultado o contexto de treino", example = "false") @RequestParam(required = false, defaultValue = "true") Boolean contextoTreino) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ContextosAlunoVO>) super.finish(this.iaContextoService.consultarContextosAluno(empresa, cliente, contextoTreino));
    }

    @Operation(summary = "Atualizar contexto de um aluno",
            description = "Atualizar contexto  sobre o aluno, seus contratos, treinos, avaliação fisica, aulas, pagamentos e histórico de contato")
    @LogExecution
    @PostMapping(value = "/contextos/aluno")
    public ResponseEntity<ContextosAlunoVO> atualizarContextoAluno(
            @RequestBody AlunoVO alunoVO) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        try {
            return (ResponseEntity<ContextosAlunoVO>) super.finish(this.iaContextoService.atualizarContextoAluno(alunoVO));
        } catch (feign.FeignException.NotFound ex) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }


    @Operation(summary = "Consultar contexto de alunos por fase do crm na data atual da meta diária",
            description = "Consulta informações sobre o aluno, seus contratos, treinos, avaliação fisica, aulas, pagamentos e histórico de contato")
    @LogExecution
    @GetMapping(value = "/contextos/aluno/fase")
    public ResponseEntity<ContextosAlunoVO> consultarContextosAlunoPorFaseEmpresa(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "fase", description = "Fase do CRM", example = "VINTE_QUATRO_HORAS") FasesCRMEnum fase,
            @Parameter(name = "page", description = "Número da página", example = "0") int page,
            @Parameter(name = "size", description = "Quantidade de resultados por página", example = "10") int size) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        Pageable pageable = getPagingAndSortingData(page, size, null).get();
        return (ResponseEntity<ContextosAlunoVO>) super.finish(this.iaContextoService.consultarContextosAlunosPorFase(empresa, fase, pageable));
    }

    @Operation(summary = "Consultar contexto de fases na api de IA Pacto conversas")
    @LogExecution
    @GetMapping(value = "/contextos/fases")
    public ResponseEntity<ResponsePactoConversasVO<List<ContextoFaseVO>>> consultarContextoFases(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<List<ContextoFaseVO>>>) super.finish(this.iaContextoService.consultarContextoFases(empresa));
    }

    @Operation(summary = "Atualizar contexto de fases na api de IA Pacto conversas")
    @LogExecution
    @PutMapping(value = "/contextos/fases")
    public ResponseEntity<ResponsePactoConversasVO<List<ContextoFaseVO>>> atualizarContextoFases(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "enviarContextoRede", description = "Indica se deve atualizar o contexto de toda rede da academia.", example = "false") @RequestParam(required = false, defaultValue = "false") Boolean enviarContextoRede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<List<ContextoFaseVO>>>) super.finish(this.iaContextoService.atualizarContextoFases(empresa));
    }

    @Operation(summary = "Atualizar contexto de persionalidade na api de IA Pacto conversas")
    @LogExecution
    @PutMapping(value = "/contextos/personalidade")
    public ResponseEntity<ResponsePactoConversasVO<ResponseEnviarMensagemVO>> atualizarContextoPercionalidade(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "enviarContextoRede", description = "Indica se deve atualizar o contexto de toda rede da academia.", example = "false") @RequestParam(required = false, defaultValue = "false") Boolean enviarContextoRede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<ResponseEnviarMensagemVO>>) super.finish(this.iaContextoService.atualizarContextoPersonalidade(empresa));
    }


    @Operation(summary = "Consultar contexto do produto",
            description = "Consulta informações sobre o(s) produto(s) para contextualizar o agente de ia do crm")
    @LogExecution
    @GetMapping(value = "/contextos/produto")
    public ResponseEntity<List<ProdutoVO>> consultarContextosPorProdutos(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<List<ProdutoVO>>) super.finish(this.iaContextoService.consultarContextoProdutos(empresa));
    }

    @Operation(summary = "Atualizar contexto do(s) produtos na api de IA Pacto conversas")
    @LogExecution
    @PutMapping(value = "/contextos/produto")
    public ResponseEntity<ResponsePactoConversasVO<List<ProdutoVO>>> atualizarContextoProdutos(
            @Parameter(name = "empresa", description = "Código da empresa", example = "1") Integer empresa,
            @Parameter(name = "enviarContextoRede", description = "Indica se deve atualizar o contexto de toda rede da academia.", example = "false") @RequestParam(required = false, defaultValue = "true") Boolean enviarContextoRede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<ResponsePactoConversasVO<List<ProdutoVO>>>) super.finish(this.iaContextoService.atualizarContextoProdutos(empresa));
    }


    @Operation(summary = "Atualizar contexto de IA")
    @LogExecution
    @PutMapping("/contextos/atualizar")
    public ResponseEntity<?> atualizarContextos(
            @RequestParam @Parameter(name = "codigoEmpresa", description = "Codigo da empresa", example = "1") Integer codigoEmpresa,
            @Parameter(name = "rede", description = "Indica se a empresa faz parte de uma rede", example = "false") @RequestParam(required = false, defaultValue = "false") Boolean rede) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        iaContextoService.atualizarContextosConversasIA(codigoEmpresa, rede);
        return ResponseEntity.accepted().build();
    }
}
