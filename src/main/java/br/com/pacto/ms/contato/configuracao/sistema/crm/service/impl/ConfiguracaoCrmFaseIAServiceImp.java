package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaseIAVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmFaseIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmFaseIAService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.Converter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ConfiguracaoCrmFaseIAServiceImp<T, DTO> implements ConfiguracaoCrmFaseIAService<T, DTO> {

    @Autowired
    private ConfiguracaoCrmFaseIARepository repository;

    @Override
    public List<ConfiguracaoCrmFaseIAVO> listarTodos() {
        List<ConfiguracaoCrmFaseIAEntity> list = repository.findAllActivate();
        return convertToVO(list);
    }

    @Override
    public List<ConfiguracaoCrmFaseIAVO> listarAtivosPorEmpresa(Integer empresa) {
        List<ConfiguracaoCrmFaseIAEntity> list = repository.findAllActivateAndCompany(empresa);
        return convertToVO(list);
    }

    @Override
    public List<ConfiguracaoCrmFaseIAVO> listarPorFasesEMetaExtras(List<String> fases, Integer codigoEmpresa) {
        Map<Boolean, List<String>> fasesSeparadas = fases.stream()
                .collect(Collectors.partitioningBy(f -> FasesCRMEnum.getFaseByName(f) != null));

        List<FasesCRMEnum> fasesEnum = fasesSeparadas.get(true).stream()
                .map(FasesCRMEnum::getFaseByName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        List<ConfiguracaoCrmFaseIAVO> resultados = new ArrayList<>();
        if (!fasesEnum.isEmpty()) {
            resultados.addAll(convertToVO(repository.findAllActiveFilteredByFases(fasesEnum, codigoEmpresa)));
        }
        if (!fasesSeparadas.get(false).isEmpty()) {
            resultados.addAll(convertToVO(repository.findByMetaExtraName(fasesSeparadas.get(false), codigoEmpresa)));
        }
        return resultados;
    }

    private List<ConfiguracaoCrmFaseIAVO> convertToVO(List<ConfiguracaoCrmFaseIAEntity> entities) {
        return entities.stream()
                .map(f -> ConfiguracaoCrmFaseIAVO.builder()
                        .codigo(f.getCodigo())
                        .fase(f.getFase())
                        .descricao(f.getDescricao())
                        .habilitar(f.getHabilitar())
                        .codigoEmpresa(f.getCodigoEmpresa())
                        .codigoMetaExtra(f.getCodigoMetaExtra())
                        .nomeMetaExtra(f.getNomeMetaExtra())
                        .codigoEmpresa(f.getCodigoEmpresa())
                        .build())
                .collect(Collectors.toList());
    }
}