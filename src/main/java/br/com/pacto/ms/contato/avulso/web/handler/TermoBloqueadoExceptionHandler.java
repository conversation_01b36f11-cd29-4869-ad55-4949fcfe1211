package br.com.pacto.ms.contato.avulso.web.handler;

import br.com.pacto.ms.contato.avulso.service.exception.TermoBloqueadoException;
import br.com.pactosolucoes.commons.data.vo.ResponseImplLegacyVO;
import br.com.pactosolucoes.commons.data.vo.ResponseImplVO;
import br.com.pactosolucoes.commons.data.vo.ResponseVO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.TERMO_BLOQUIEIO_EXCEPTION;
import static br.com.pactosolucoes.commons.enums.message.APIStructureMessages.PROCESS_FINISHED_ERROR;

public @ControllerAdvice class TermoBloqueadoExceptionHandler {

    private @Autowired
    RequestService requestService;

    @ExceptionHandler(value = {TermoBloqueadoException.class})
    public ResponseEntity<?> handleArchetypeException(TermoBloqueadoException exception) {
        ResponseVO responseVO = null;

        if (requestService.getCurrentConfiguration().getReponseLegacy()) {
            responseVO = ResponseImplLegacyVO.init()
                    .addMessage(TERMO_BLOQUIEIO_EXCEPTION);
        } else {
            responseVO = ResponseImplVO.init()
                    .addMessage(TERMO_BLOQUIEIO_EXCEPTION)
                    .addMessage(PROCESS_FINISHED_ERROR);
        }

        return new ResponseEntity<ResponseVO>(responseVO, null, TERMO_BLOQUIEIO_EXCEPTION.getStatusCode());

    }
}
