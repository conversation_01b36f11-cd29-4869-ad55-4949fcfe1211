package br.com.pacto.ms.contato.base.service.contract;

import br.com.pacto.ms.contato.base.data.pojo.input.MalaDiretaDTO;
import br.com.pacto.ms.contato.base.data.pojo.input.MessageDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.IntegracaoCRMVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.MeioEnvioEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoAgendamentoEnum;

public interface ApiService<T>{
    String sendSms(MessageDTO msg, String key, String token);

    void sendJenkins(String chave, String url, String id_mailing, String urlJenkins, String chaveAntiga);

    void updateJenkinsInstantaneo(MalaDiretaDTO malaDireta, IntegracaoCRMVO confCrm, String key, MeioEnvioEnum m, TipoAgendamentoEnum t);

    String enviarNotificacaoApp(String key, String idCliente, String titulo, String textoCRM, String opcoes);
}
