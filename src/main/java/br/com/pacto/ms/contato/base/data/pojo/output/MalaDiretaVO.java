package br.com.pacto.ms.contato.base.data.pojo.output;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Date;
import java.sql.Timestamp;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MalaDiretaVO {
    private Timestamp dataenvio;
    private Timestamp datacriacao;
    private String mensagem;
    private String titulo;
    private int remetente;
    private Integer modelomensagem;
    private int codigo;
    private Short meiodeenvio;
    private Integer empresa;
    private Date vigenteate;
    private Integer evento;
    private String sql;
    private Integer tipoagendamento;
    private Boolean excluida;
    private Boolean contatoavulso;
    private String faseenvio;
    private String opcoes;
    private Integer tipopergunta;
    private Integer codaberturameta;
    private Integer diasposvenda;
    private Boolean metaextraindividual;
    private String tipoconsultormetaextraindividual;
    private Integer quantidademinimaacessos;
    private Integer quantidademaximaacessos;
    private Integer intervalodias;
    private Boolean todasempresas;
    private Integer questionario;
    private Integer tipocancelamento;
    private Boolean importarlista;
    private String configs;
    private Boolean enviohabilitado;
    private Boolean smsmarketing;
    private Boolean statusentregabilidade;
}

