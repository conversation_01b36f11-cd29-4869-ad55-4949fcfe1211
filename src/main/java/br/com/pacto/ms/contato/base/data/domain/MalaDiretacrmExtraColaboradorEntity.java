package br.com.pacto.ms.contato.base.data.domain;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;

@Entity
@Table(name = "maladiretacrmextracolaborador", schema = "public")
@Getter
@Setter
public class MalaDiretacrmExtraColaboradorEntity {

    @Id
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "maladireta")
    private Integer maladireta;

    @Column(name = "usuario")
    private Integer usuario;

}