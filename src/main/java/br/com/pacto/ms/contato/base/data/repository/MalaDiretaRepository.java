package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.MaladiretaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;

public interface MalaDiretaRepository extends PagingAndSortingRepository<MaladiretaEntity, Integer> {

    /**
     * MaladiretaEntity é a meta extra, que eu pego para virar uma "fase" que a IA vai usar para enviar mensagens
     *
     * @param username
     * @return
     */

    @Query("SELECT m FROM MaladiretaEntity m WHERE m.codigo IN " +
            "(SELECT mc.maladireta FROM MalaDiretacrmExtraColaboradorEntity mc WHERE mc.usuario IN " +
            "(SELECT u.codigo FROM UsuarioEntity u WHERE LOWER(u.username) = LOWER(:username)))")
    List<MaladiretaEntity> findByU<PERSON><PERSON>(String username);

}
