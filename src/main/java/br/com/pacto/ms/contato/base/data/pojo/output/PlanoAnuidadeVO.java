package br.com.pacto.ms.contato.base.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PlanoAnuidadeVO {
    @Schema(description = "Código da anuidade do plano", example = "1")
    private Integer codigo;

    @Schema(description = "Número da parcela", example = "1")
    private Integer numero;

    @Schema(description = "Valor da parcela", example = "99.99")
    private Double valor;

    @Schema(description = "Total de parcelas", example = "12")
    private Integer parcela;
}