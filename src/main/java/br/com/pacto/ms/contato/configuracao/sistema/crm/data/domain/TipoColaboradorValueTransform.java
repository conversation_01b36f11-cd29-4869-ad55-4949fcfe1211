package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import br.com.pactosolucoes.commons.util.annotation.ValueTransformContract;

public class TipoColaboradorValueTransform implements ValueTransformContract {
    @Override
    public String transform(Object value) {
        if(value == null){
            return null;
        }

        return TipoColaboradorEnum.getTipo((String) value).getDescricao();
    }
}
