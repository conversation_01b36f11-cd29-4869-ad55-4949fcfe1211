package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl.ConfiguracaoCrmFaseIAServiceImp;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@Tag(name = "Configurações CRM", description = "Gestão de configurações para gerir entidades de ConfiguraçãoCrmFaseIA")
@RequestMapping(value = "/v1/configuracao/fase-ia")
@RestController
public class ConfiguracaoCrmFaseIAController {

    private final ConfiguracaoCrmFaseIAServiceImp service;

    @Autowired
    public ConfiguracaoCrmFaseIAController(ConfiguracaoCrmFaseIAServiceImp service) {
        this.service = service;
    }

    @GetMapping("/consultar")
    public ResponseEntity<List<ConfiguracaoCrmFaseIAEntity>> listarAtivosPorEmpresa(@RequestParam(required = false) Integer empresa) {
        return ResponseEntity.ok((List<ConfiguracaoCrmFaseIAEntity>) service.listarAtivosPorEmpresa(empresa));
    }

}