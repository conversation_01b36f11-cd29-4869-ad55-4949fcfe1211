package br.com.pacto.ms.contato.log.data.repository;


import br.com.pacto.ms.contato.avulso.data.domain.UsuarioPerfilAcessoEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface UsuarioPerfilAcessoRepository extends PagingAndSortingRepository<UsuarioPerfilAcessoEntity, Integer> {

    @Query("SELECT up FROM UsuarioPerfilAcessoEntity up WHERE up.empresa = :empresaId and up.usuario = :codigoUsuario")
    Optional<UsuarioPerfilAcessoEntity> verificaSePossueAcessoAEmpresa(Integer empresaId, Integer codigoUsuario);

    @Query("SELECT up FROM UsuarioPerfilAcessoEntity up WHERE  up.usuario = :codigoUsuario")
    Optional<List<UsuarioPerfilAcessoEntity>> buscarPerfilAcessoOutraEmpresa(Integer codigoUsuario);
}
