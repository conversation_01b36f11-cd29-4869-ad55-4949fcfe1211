package br.com.pacto.ms.contato.base.enums;

import java.util.Arrays;

public enum GrupoSituacaoEnum {

    ATIVO("AT", "Ativo"),
    INATIVO("IN", "Inativo"),
    TRANCADO("TR", "Trancado"),
    VISITANTE("VI", "Visitante");

    private String codigo;
    private String descricao;

    GrupoSituacaoEnum(String codigo, String descricao) {
        this.codigo = codigo;
        this.descricao = descricao;
    }

    public static GrupoSituacaoEnum findByCodigo(String codigo) {
                return Arrays.stream(values())
                .filter(GrupoSituacaoEnum -> GrupoSituacaoEnum.getCodigo().equals(codigo))
                .findFirst().orElse(null);

    }

    public String getCodigo() {
        return codigo;
    }

    public void setCodigo(String codigo) {
        this.codigo = codigo;
    }

    public String getDescricao() {
        return descricao;
    }

    public void setDescricao(String descricao) {
        this.descricao = descricao;
    }
}
