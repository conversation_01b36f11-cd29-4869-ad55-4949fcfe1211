package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.pojo.output.ModalidadeVO;
import br.com.pacto.ms.contato.base.data.pojo.output.TelefoneVO;
import br.com.pacto.ms.contato.base.data.repository.ModalidadeRepository;
import br.com.pacto.ms.contato.base.service.contract.ModalidadeService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
public @Service class ModalidadeImpl<T> implements ModalidadeService {

    private ModalidadeRepository repository;

    @Override
    @LogExecution
    @ObjectMapper(ModalidadeVO.class)
    public T buscarModalidade(String nome) {
        return (T) repository.findAllByAtivoAndNomeContaining(true, nome);
    }

    @Override
    @LogExecution
    @ObjectMapper(ModalidadeVO.class)
    public T buscarTodasModalidade() {
        return (T) repository.findAllByAtivo(true);
    }


}
