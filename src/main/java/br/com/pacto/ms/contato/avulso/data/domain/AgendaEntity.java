package br.com.pacto.ms.contato.avulso.data.domain;

import java.sql.Timestamp;

import javax.persistence.*;

import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "agenda", schema = "public")
public class AgendaEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "dataagendamento", nullable = true)
    private Timestamp dataagendamento;
    @Basic
    @Column(name = "datalancamento", nullable = true)
    private Timestamp datalancamento;
    @Basic
    @Column(name = "hora", nullable = true, length = 5)
    private String hora;
    @Basic
    @Column(name = "minuto", nullable = true, length = 2)
    private String minuto;
    @Basic
    @Column(name = "tipoagendamento", nullable = true, length = 50)
    private String tipoagendamento;
    @Basic
    @Column(name = "modalidade", nullable = true)
    private Integer modalidade;
    @Basic
    @Column(name = "passivo", nullable = true)
    private Integer passivo;
    @Basic
    @Column(name = "colaboradorresponsavel", nullable = true)
    private Integer colaboradorresponsavel;
    @Basic
    @Column(name = "cliente")
    private Integer cliente;
    @Basic
    @Column(name = "responsavelcadastro", nullable = true)
    private Integer responsavelcadastro;
    @Basic
    @Column(name = "indicado", nullable = true)
    private Integer indicado;
    @Basic
    @Column(name = "datacomparecimento", nullable = true)
    private Timestamp datacomparecimento;
    @Basic
    @Column(name = "responsavelcomparecimento", nullable = true)
    private Integer responsavelcomparecimento;
    @Basic
    @Column(name = "empresa", nullable = true)
    private Integer empresa;
    @Basic
    @Column(name = "conviteaulaexperimental", nullable = true)
    private Integer conviteaulaexperimental;
    @Basic
    @Column(name = "reposicao", nullable = true)
    private Integer reposicao;
    @Basic
    @Column(name = "gympass", nullable = true)
    private Boolean gympass;
    @Basic
    @Column(name = "tipoprofessor", nullable = true, length = 2)
    private String tipoprofessor;
    @Basic
    @Column(name = "codigoprofessor", nullable = true)
    private Integer codigoprofessor;
    @Basic
    @Column(name = "alunohorarioturma", nullable = true)
    private Integer alunohorarioturma;

}
