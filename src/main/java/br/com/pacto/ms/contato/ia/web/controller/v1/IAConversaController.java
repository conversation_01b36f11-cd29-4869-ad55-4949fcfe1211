package br.com.pacto.ms.contato.ia.web.controller.v1;

import br.com.pacto.ms.contato.ia.data.pojo.input.HistoricoContatoAIDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.IndicadorDTO;
import br.com.pacto.ms.contato.ia.data.pojo.input.MetaVendaPlanoDTO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import br.com.pacto.ms.contato.ia.service.contract.ConversaService;
import br.com.pacto.ms.contato.ia.service.contract.IAConversaService;
import br.com.pactosolucoes.commons.data.PagingAndSortingData;
import br.com.pactosolucoes.commons.data.dto.DefaultConfigurationDTO;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Validated
@Tag(name = "Inteligência artificial",
        description = "Consultas de contextos aplicados a inteligência artificial do modulo CRM")
@RestController
@RequestMapping(value = "/v1/ia")
@SecurityRequirement(name = "Authorization bearer")
public class IAConversaController extends BaseController {

    @Autowired
    private ConversaService conversaService;
    @Autowired
    private IAConversaService iaConversaService;
    @Autowired
    private RequestService requestService;

    private static final String DEFAULT_ORDER_BY = "codigo";

    @Operation(summary = "Inciar conversa com aluno utilizando a IA Pacto conversas",
            description = "Consulta informações do aluno e inicia um conversa com base no na fase do crm em que esse aluno se encontra")
    @LogExecution
    @PostMapping(value = "/conversa")
    public ResponseEntity<ContextoEmpresaVO> iniciarConversa(
            @Parameter(name = "chave", description = "Chave da empresa", example = "810f691394b9cf79d33459a08861f3ed") @RequestParam(name = "chave") String chave,
            @Parameter(name = "cliente", description = "Código do cliente", example = "4") @RequestParam(name = "cliente") Integer cliente) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        requestService.getCurrentConfiguration().setCompanyKey(chave);
        return (ResponseEntity<ContextoEmpresaVO>) super.finish(this.conversaService.inciarConversa(cliente, null, null, null, null));
    }

    @Operation(summary = "Inciar conversa após uma venda",
            description = "Consulta informações do aluno, e inicia uma conversa no contexto de pós venda")
    @LogExecution
    @PostMapping(value = "/conversa/pos-venda")
    public ResponseEntity<ContextoEmpresaVO> iniciarConversaPosVenda(
            @Parameter(name = "chave", description = "Chave da empresa", example = "teste") @RequestParam(name = "chave") String chave,
            @Parameter(name = "linkAppTreino", description = "Link do app que será enviado para o aluno instalar o app", example = "https://treino.page.link/rt69oY6GXGFXq8TA6") @RequestParam(name = "linkAppTreino", required = false) String linkAppTreino,
            @Parameter(name = "cpf", description = "Cpf do cliente", example = "897.601.838-97") @RequestParam(name = "cpf") String cpf) {
        // TODO: tratar o default pagination, pagingandsorting e chave no pacto-commons
        DefaultConfigurationDTO currentConfiguration = new DefaultConfigurationDTO();
        currentConfiguration.setCompanyKey(chave);
        currentConfiguration.setReponseLegacy(false);
        currentConfiguration.setPageable(getPagingAndSortingData(0, 1, null).get());
        PagingAndSortingData pagingAndSortingData = PagingAndSortingData
                .builder()
                .page(0)
                .size(1)
                .orderBy(DEFAULT_ORDER_BY.split(","))
                .build();
        currentConfiguration.setPagingAndSortingData(pagingAndSortingData);
        this.requestService.updateCurrentConfiguration(currentConfiguration);
        if(this.requestService.getCurrentConfiguration().getCompanyKey() == null){
            this.requestService.getCurrentConfiguration().setCompanyKey(chave);
        }

        return (ResponseEntity<ContextoEmpresaVO>) super.finish(this.conversaService.inciarPosVendaConversaPorCpf(cpf, linkAppTreino));
    }

    @LogExecution
    @Operation(summary = "Registar histórico de contato com aluno.",
            description = "Registra um histórico de contato com aluno das conversas realizadas pela IA")
    @PostMapping(value = "/conversa/historico-contato")
    public ResponseEntity<HistoricoContatoAIVO> salvarHistoricoContato(@Valid @RequestBody HistoricoContatoAIDTO dto) {
        return (ResponseEntity<HistoricoContatoAIVO>) super.finish(this.conversaService.salvarConversa(dto));
    }

    @Operation(summary = "Inciar conversa por Fase do CRM",
            description = "Inicicar uma conversa com base na fase do CRM")
    @LogExecution
    @PostMapping(value = "/conversa/fase/crm/{chave}")
    public ResponseEntity<ResponsePactoConversasVO<IAConversaServiceVO>> iniciarConversaPorFaseCRM(
            @PathVariable @Parameter(name = "chave", description = "Chave da empresa", example = "teste") String chave,
            @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Lista de fases da conversa no CRM",
                    content = @Content(
                            mediaType = "application/json",
                            examples = @ExampleObject(value = "[\"AGENDAMENTO\", \"EX_ALUNOS\"]")
                    )
            )
            @RequestBody(required = false) List<String> fases,
            @Parameter(
                    name = "forcarEnvio",
                    description = "Se true a mensagem será enviada para os clientes mesmo se já foi enviado uma mensagem para ele no mesmo dia.", example = "false")
            @RequestParam(name = "forcarEnvio", defaultValue = "true", required = false) boolean forcarEnvio,
            @RequestParam(name = "empresa", required = false) Integer empresa) {
        return (ResponseEntity<ResponsePactoConversasVO<IAConversaServiceVO>>)
                super.finish(this.iaConversaService.inciarConversaPorFase(chave, fases, forcarEnvio, empresa));
    }

    @PostMapping(value = "/bi/indicadores/venda/plano")
    @Operation(summary = "Recebe indicadores de BI", description = "Registra indicadores de planos vendidos pelo Conversas.AI")
    public ResponseEntity<ResponsePactoConversasVO<ResponseStatusVO>> receberIndicadoresBI(
            @Valid @RequestBody IndicadorDTO<MetaVendaPlanoDTO> dto
    ) {
        try {
            return ResponseEntity.ok(this.conversaService.receberIndicadoresBI(dto));
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }
}
