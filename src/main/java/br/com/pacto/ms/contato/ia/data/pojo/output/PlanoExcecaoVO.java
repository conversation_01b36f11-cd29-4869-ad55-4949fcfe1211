package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.pojo.output.ModalidadeVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class PlanoExcecaoVO {
    @Schema(description = "Valor.")
    private Float valor;
    @Schema(description = "Duração.")
    private Integer duracao;
    @Schema(description = "Repetições na semana.")
    private Integer vezesSemana;
    @Schema(description = "Modalidade.")
    private ModalidadeVO modalidade;
}