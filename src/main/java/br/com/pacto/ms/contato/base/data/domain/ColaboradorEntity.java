package br.com.pacto.ms.contato.base.data.domain;

import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "colaborador", schema = "public")
public class ColaboradorEntity {
    @Basic
    @Column(name = "situacao")
    private String situacao;
    @Id
    @Column(name = "codigo")
    private int codigo;

    @OneToOne
    @JoinColumn(name = "pessoa", referencedColumnName = "codigo")
    private PessoaEntity pessoa;

}
