package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "cliente", schema = "public")
public class ClientEntity {
    private Integer codigomatricula;
    private String matricula;
    private String situacao;
    private int codigo;

    @Basic
    @Column(name = "codigomatricula")
    public Integer getCodigomatricula() {
        return codigomatricula;
    }

    @Basic
    @Column(name = "matricula")
    public String getMatricula() {
        return matricula;
    }

    @Id
    @Column(name = "codigo")
    public int getCodigo() {
        return codigo;
    }

    @Basic
    @Column(name = "pessoa", nullable = false)
    private Integer pessoa;

}
