package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class PactoConversasUrlResolverService {

    @Value("${conversas.ia.url:}")
    private String conversasIaUrl;

    @Autowired
    private RequestService requestService;

    public String getPactoConversasUrl() {
        if (StringUtils.hasText(conversasIaUrl) && !conversasIaUrl.contains("@conversas.ai.url@")) {
            log.info("Resolvendo URL do Pacto Conversas via configuração: {}", conversasIaUrl);
            return conversasIaUrl;
        }
        
        if (conversasIaUrl.contains("@conversas.ai.url@")) {
            log.warn("Placeholder @conversas.ai.url@ não foi resolvido durante o build. Usando discovery service como fallback.");
        }
        
        String discoveryUrl = requestService.getClienteDiscovery().getServiceUrls().getPactoConversasApiUrl();
        log.info("Resolvendo URL do Pacto Conversas via discovery service: {}", discoveryUrl);
        return discoveryUrl;
    }
}
