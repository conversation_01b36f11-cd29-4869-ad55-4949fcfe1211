package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.avulso.data.domain.EmpresaEntity;
import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogFieldDescription;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaoemailfechamentometa", schema = "public")
@EntityListeners(LogListener.class)
public class ConfiguracaoEmailFechamentoMetaEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Column
    @LogFieldDescription("E-mail para notificação de fechamento de meta")
    private String email;
    @ManyToOne
    @JoinColumn(name = "empresa", referencedColumnName = "codigo")
    @LogFieldDescription("Empresa relacionada ao email do fechamento de meta")
    private EmpresaEntity empresa;
}
