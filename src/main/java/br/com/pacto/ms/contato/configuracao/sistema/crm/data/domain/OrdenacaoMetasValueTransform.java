package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pactosolucoes.commons.util.annotation.ValueTransformContract;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class OrdenacaoMetasValueTransform implements ValueTransformContract {
    @Override
    public String transform(Object value) {
        if(value == null){
            return null;
        }

        String[] siglas = value.toString().split(",");
        List<String> descricoes = Arrays.stream(siglas).map(sigla -> FasesCRMEnum.getFasePorSigla(sigla).getDescricao()).collect(Collectors.toList());
        return  descricoes.stream().collect(Collectors.joining(", "));
    }
}
