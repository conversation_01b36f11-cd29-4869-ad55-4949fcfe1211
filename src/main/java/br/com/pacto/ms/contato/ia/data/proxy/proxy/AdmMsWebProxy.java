package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import br.com.pacto.ms.contato.config.proxy.AdmMsWebProxyConfig;
import br.com.pacto.ms.contato.ia.data.pojo.output.EmpresaResumoResponseVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "AdmMsWeb", configuration = AdmMsWebProxyConfig.class)
public interface AdmMsWebProxy {

    @GetMapping(path = "/v1/empresa/resumo")
    EmpresaResumoResponseVO consultarResumo(
            @RequestHeader("Authorization") String token,
            @RequestParam(name = "nome", required = false) String nome,
            @RequestParam(name = "page") int page,
            @RequestParam(name = "size") int size,
            @RequestParam(name = "orderBy") String orderBy
    );

}

