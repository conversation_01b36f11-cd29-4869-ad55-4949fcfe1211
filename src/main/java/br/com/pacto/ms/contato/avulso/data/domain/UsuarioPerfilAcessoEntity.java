package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "usuarioperfilacesso", schema = "public")
public class UsuarioPerfilAcessoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "empresa",  nullable = false)
    private Integer empresa;

    @Column(name = "usuario", nullable = false)
    private Integer usuario;

    @Column(name = "perfilacesso", nullable = false)
    private Integer perfilAcesso;

    @Column(name = "unificado", nullable = false, columnDefinition = "boolean default false")
    private Boolean unificado;
}