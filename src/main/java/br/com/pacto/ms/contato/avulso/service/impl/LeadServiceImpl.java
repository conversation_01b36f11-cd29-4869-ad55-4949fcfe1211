package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.*;
import br.com.pacto.ms.contato.avulso.data.pojo.input.LeadDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.LeadVO;
import br.com.pacto.ms.contato.avulso.data.repository.*;
import br.com.pacto.ms.contato.avulso.service.contract.LeadService;
import br.com.pacto.ms.contato.avulso.service.exception.CadastroLeadException;
import br.com.pacto.ms.contato.base.data.domain.FecharMetaDetalhadoEntity;
import br.com.pacto.ms.contato.base.data.domain.FecharMetaEntity;
import br.com.pacto.ms.contato.base.data.repository.FecharMetaDetalhadoRepository;
import br.com.pacto.ms.contato.base.data.repository.FecharMetaRepository;
import br.com.pacto.ms.contato.base.service.contract.TelefoneService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.AgendamentoLeadVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.proxy.ZillyonWebCrmProxy;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.log.service.contract.UsuarioService;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.apachecommons.CommonsLog;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.sql.Timestamp;
import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@CommonsLog
@Service
@RequiredArgsConstructor
public class LeadServiceImpl implements LeadService {

    private static final String horarioLimite = "23:59";

    private final ConversaoLeadRepository conversaoLeadRepository;
    private final EmpresaRepository empresaRepository;
    private final ClienteRepository clienteRepository;
    private final UsuarioService usuarioService;
    private final LeadRepository leadRepository;
    private final PassivoRepository passivoRepository;
    private final FecharMetaRepository fecharMetaRepository;
    private final FecharMetaDetalhadoRepository fecharMetaDetalhadoRepository;
    private final HistoricoContatoRepository historicoContatoRepository;
    private final TelefoneService telefoneService;
    private final ZillyonWebCrmProxy zillyonWebCrmProxy;
    private final RequestService requestService;

    private static final String TIPO_AGENDAMENTO = "VI";
    private static final String MSG_AGENDAMENTO = "Seu agendamento de {0} esta confirmado para {1} as {2}.";

    @Override
    @ExceptionHandler(CadastroLeadException.class)
    public LeadVO cadastrarLead(LeadDTO leadDTO) {
        UsuarioEntity usuarioResponsavel = usuarioService.consultarPorUsername(leadDTO.getUsernameResposavel());
        if (Objects.isNull(usuarioResponsavel)) {
            throw new CadastroLeadException("Usuario responsavel não encontrado");
        }

        Optional<EmpresaEntity> empresa = empresaRepository.findById(leadDTO.getCodigoEmpresa());
        if (!empresa.isPresent()) {
            throw new CadastroLeadException("Empresa não encontrada");
        }
        try {
            LeadEntity lead = criarLead(leadDTO, empresa.get(), usuarioResponsavel);
            ConversaoLeadEntity novaLead = new ConversaoLeadEntity();
            novaLead.setProps(toProps(leadDTO));
            novaLead.setLead(lead);
            novaLead.setResponsavel(usuarioResponsavel);
            novaLead.setDataCriacao(new Date());
            novaLead.setDataLancamento(new Date());
            novaLead.setIdentificador(Objects.isNull(leadDTO.getIdentificador()) ? "Conversas IA" : leadDTO.getIdentificador());
            conversaoLeadRepository.save(novaLead);

            Date agora = Date.from(ZonedDateTime.now(ZoneId.of("Brazil/East")).toInstant());
            LocalTime horaLimite = LocalTime.parse(horarioLimite);
            ZonedDateTime horarioLimiteZoned = LocalDate.now().atTime(horaLimite).atZone(ZoneId.of("Brazil/East"));
            Date horarioLimite = Date.from(horarioLimiteZoned.toInstant());

            if (horarioLimite.after(agora)) {
                this.adicionarMetaDoDia(novaLead, agora, empresa.get().getCodigo(), usuarioResponsavel.getCodigo());
            }
            agendarLeadNaFase(lead, leadDTO);
            return LeadVO.toVo(lead);
        } catch (Exception e) {
            log.error("Erro ao cadastrar lead: ", e);
            throw new CadastroLeadException(e.getMessage());
        }
    }

    private void agendarLeadNaFase(LeadEntity lead, LeadDTO leadDTO) throws CadastroLeadException {
        try {
            String dataAtual = DateTimeFormatter.ofPattern("dd/MM/yyyy").format(LocalDate.now());
            String horaAtual = DateTimeFormatter.ofPattern("HH:mm").format(LocalTime.now());

            String tipoAgendamento = Objects.nonNull(leadDTO.getTipoAgendamento()) ? "VI".equals(leadDTO.getTipoAgendamento()) ? "Visita" : "Ligação": "Visita";
            String dataAgendamento = leadDTO.getDataAgendamento() != null ? leadDTO.getDataAgendamento() : dataAtual;
            String horarioAgendamento = leadDTO.getHorarioAgendamento() != null ? leadDTO.getHorarioAgendamento() : horaAtual;
            String tipo = leadDTO.getTipoAgendamento() != null ? leadDTO.getTipoAgendamento() : TIPO_AGENDAMENTO;

            String mensagem = MessageFormat.format(MSG_AGENDAMENTO, tipoAgendamento, dataAgendamento, horarioAgendamento);

            AgendamentoLeadVO agendamentoLeadVO = AgendamentoLeadVO.builder()
                    .codigoLead(lead.getCodigo())
                    .codigoCliente(null)
                    .tipoAgendamento(tipo)
                    .faseAtual(FasesCRMEnum.LEADS_HOJE.getName())
                    .empresa(String.valueOf(leadDTO.getCodigoEmpresa()))
                    .observacao(mensagem)
                    .dataAgendamento(dataAgendamento)
                    .horario(horarioAgendamento)
                    .build();

            String chave = requestService.getCurrentConfiguration().getCompanyKey();
            zillyonWebCrmProxy.agendarLead(chave, agendamentoLeadVO);
        } catch (Exception e) {
            log.error("Erro ao agendar lead na fase: ", e);
            throw new CadastroLeadException(e.getMessage());
        }
    }

    private String toProps(LeadDTO leadDTO) {
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(leadDTO);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @ExceptionHandler(Exception.class)
    private LeadEntity criarLead(LeadDTO leadDTO, EmpresaEntity empresa, UsuarioEntity responsavel) {
        LeadEntity lead = new LeadEntity();
        lead.setEmpresa(empresa.getCodigo());
        lead.setTipo(TipoLeadEnum.GENERICO);
        lead.setDataRegistro(LocalDate.now());
        Optional<ClienteEntity> cliente = obterCliente(leadDTO);
        if (cliente.isPresent()) {
            LeadEntity leadDb = leadRepository.consultarPorCliente(cliente.get().getCodigo());
            lead = (Objects.isNull(leadDb)) ? lead : leadDb;
            telefoneService.persistirTelefonesClienteLead(cliente.get().getPessoa().getCodigo(), leadDTO.getTelefone(), "", "");
            PassivoEntity passivo = criarPassivo(leadDTO, empresa, responsavel, cliente.get());
            lead.setCliente(cliente.get());
            lead.setPassivo(passivo);
        } else {
            Optional<PassivoEntity> optionalPassivo =
                    (leadDTO.getEmail() != null && !leadDTO.getEmail().isEmpty())
                            ? passivoRepository.consultarPorEmail(leadDTO.getEmail())
                            : Optional.empty();

            if (optionalPassivo.isPresent()) {
                PassivoEntity passivoEntity = optionalPassivo.get();
                HistoricoContatoEntity historicoContatoEntity = getHistoricoContato(responsavel, passivoEntity);
                historicoContatoRepository.save(historicoContatoEntity);
                lead.setPassivo(passivoEntity);
                passivoEntity.setTelefonecelular(passivoEntity.getTelefonecelular());
                passivoRepository.save(passivoEntity);
            } else {
                PassivoEntity passivoEntity = criarPassivo(leadDTO, empresa, responsavel, null);
                lead.setPassivo(passivoEntity);
            }
        }
        return leadRepository.save(lead);
    }

    @NotNull
    private static HistoricoContatoEntity getHistoricoContato(UsuarioEntity responsavel, PassivoEntity passivo) {
        HistoricoContatoEntity historicoContatoEntity = new HistoricoContatoEntity();
        historicoContatoEntity.setPassivo(passivo.getCodigo());
        historicoContatoEntity.setDia(new Timestamp(new Date().getTime()));
        historicoContatoEntity.setObservacao(passivo.getObservacao());
        historicoContatoEntity.setResponsavelcadastro(responsavel.getCodigo());
        historicoContatoEntity.setContatoavulso(false);
        historicoContatoEntity.setResultado("Simples Registro");
        historicoContatoEntity.setTipocontato("EM");
        return historicoContatoEntity;
    }

    private Optional<ClienteEntity> obterCliente(LeadDTO leadDTO) {
        if (!Objects.isNull(leadDTO.getEmail())) {
            Optional<List<ClienteEntity>> cliente = clienteRepository.consultarPorEmail(leadDTO.getEmail(), leadDTO.getCodigoEmpresa());
            if (cliente.isPresent() && !cliente.get().isEmpty()) {
                return cliente.get().stream().findFirst();
            }
        }

        if (!Objects.isNull(leadDTO.getCpf())) {
            Optional<List<ClienteEntity>> clientePorCpf = clienteRepository.consultarPorCpf(leadDTO.getCpf(), leadDTO.getCodigoEmpresa());
            if (clientePorCpf.isPresent() && !clientePorCpf.get().isEmpty()) {
                return clientePorCpf.get().stream().findFirst();
            }
        }
        return Optional.empty();
    }

    private String NormalizePhoneNumber(String telefone) {
        if (telefone == null || telefone.isEmpty()) {
            return telefone;
        }

        if (telefone.startsWith("+55")) {
            return telefone.substring(3);
        }
        return telefone;
    }

    private PassivoEntity criarPassivo(LeadDTO dto, EmpresaEntity empresa, UsuarioEntity responsavel, ClienteEntity clienteEntity) {
        PassivoEntity passivo = new PassivoEntity();
        passivo.setEmpresa(empresa.getCodigo());
        passivo.setColaboradorresponsavel(responsavel.getCodigo());
        passivo.setOrigemsistema((short) 7);
        passivo.setDia(new Timestamp(new Date().getTime()));
        passivo.setObservacao(dto.getMensagem());
        passivo.setResponsavelcadastro(responsavel.getCodigo());
        passivo.setNome(dto.getNome());
        passivo.setEmail(dto.getEmail());
        passivo.setLead(true);
        String telefoneSemDDI = NormalizePhoneNumber(dto.getTelefone());
        passivo.setTelefonecelular(telefoneSemDDI);
        if (Objects.nonNull(clienteEntity)) {
            passivo.setCliente(clienteEntity.getCodigo());
        }
        passivo = passivoRepository.save(passivo);
        return passivo;
    }

    public void adicionarMetaDoDia(ConversaoLeadEntity conversaoLead, Date data, Integer empresa, Integer colaboradorResponsavel) {
        FecharMetaEntity metaDia = fecharMetaRepository.consultarMetaPorDiaPorColaboradorResponsavel(
                empresa,
                colaboradorResponsavel,
                FasesCRMEnum.LEADS_HOJE.getSigla()
        );
        if (Objects.nonNull(metaDia)) {
            FecharMetaDetalhadoEntity novo = getMetaDetalhadoEntity(conversaoLead, metaDia);
            fecharMetaDetalhadoRepository.save(novo);
            metaDia.setMeta(metaDia.getMeta() + 1);
            metaDia.calcularPorcentagem();
            fecharMetaRepository.updateFecharMeta(metaDia.getMeta(), metaDia.getPorcentagem(), metaDia.getCodigo());
        }
    }

    @NotNull
    private static FecharMetaDetalhadoEntity getMetaDetalhadoEntity(ConversaoLeadEntity conversaoLead, FecharMetaEntity metaDia) {
        FecharMetaDetalhadoEntity novo = new FecharMetaDetalhadoEntity();
        if (Objects.nonNull(conversaoLead.getLead().getCliente())) {
            novo.setCliente(conversaoLead.getLead().getCliente());
        } else if (Objects.nonNull(conversaoLead.getLead().getPassivo())) {
            novo.setPassivo(conversaoLead.getLead().getPassivo());
        }
        novo.setCodigoOrigem(conversaoLead.getCodigo());
        novo.setFecharMeta(metaDia);
        novo.setOrigem("CONVERSAOLEAD");
        return novo;
    }

}
