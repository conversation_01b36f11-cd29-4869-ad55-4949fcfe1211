package br.com.pacto.ms.contato.config.proxy;

import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

public class AdmMsWebProxyConfig implements RequestInterceptor {

    private static final Logger log = LoggerFactory.getLogger(AdmMsWebProxyConfig.class);

    @Override
    public void apply(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            RequestService requestService = (RequestService) attributes.getRequest().getAttribute("scopedTarget.requestService");
            if (requestService != null) {
                try {
                    String dynamicUrl = requestService.getClienteDiscovery().getServiceUrls().getAdmMsUrl();
                    requestTemplate.target(dynamicUrl);
                    requestTemplate.query("key", requestService.getCurrentConfiguration().getCompanyKey());
                    requestTemplate.query("chave", requestService.getCurrentConfiguration().getCompanyKey());
                    requestTemplate.header("Authorization", "Bearer " + requestService.getCurrentConfiguration().getToken());
                    log.debug("URL ADM MS dinâmica definida a partir do discovery-ms para: {}", dynamicUrl);
                } catch (Exception e) {
                    log.error("Erro ao resolver a URL dinâmica", e);
                    throw new IllegalStateException("Não foi possível resolver a URL dinâmica para o cliente Feign AdmMsProxy", e);
                }
            } else {
                log.warn("RequestService é nulo, a URL dinâmica não pode ser resolvida.");
            }
        }
    }
}
