package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pactosolucoes.commons.util.AnnotationUtils;
import br.com.pactosolucoes.commons.util.annotation.LogTagTransformContract;

public class TermoSpamTagTransform implements LogTagTransformContract<TermoSpamEntity> {
    @Override
    public String transform(TermoSpamEntity object) {
        return AnnotationUtils.getTableName(object.getClass());
    }
}
