package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ContextoTurmaVO {
    @Schema(description = "Código da turma.")
    private Integer codigo;

    @Schema(description = "Idade máxima.")
    private Integer idademaxima;

    @Schema(description = "Idade mínima.")
    private Integer idademinima;

    @Schema(description = "Data final de vigência.")
    private Timestamp datafinalvigencia;

    @Schema(description = "Data inicial de vigência.")
    private Timestamp datainicialvigencia;

    @Schema(description = "Modalidade.")
    private String modalidade;

    @Schema(description = "Descrição.")
    private String descricao;

    @Schema(description = "Minutos de antecedência para marcar aula.")
    private Short minutosantecedenciamarcaraula;

    @Schema(description = "Minutos de antecedência para desmarcar aula.")
    private Short minutosantecedenciadesmarcaraula;

    @Schema(description = "Aula coletiva.")
    private Boolean aulacoletiva;

    @Schema(description = "Ocupação.")
    private Integer ocupacao;

    @Schema(description = "Mensagem.")
    private String mensagem;

    @Schema(description = "Dias.")
    private String dias;

    @Schema(description = "Horários.")
    private String horarios;

    @Schema(description = "Permitir aula experimental.")
    private Boolean permitiraulaexperimental;

    @Schema(description = "Permitir desmarcar reposições.")
    private Boolean permitirdesmarcarreposicoes;
}