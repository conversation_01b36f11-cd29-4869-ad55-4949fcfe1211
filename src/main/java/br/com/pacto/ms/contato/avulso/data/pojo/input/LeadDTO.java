package br.com.pacto.ms.contato.avulso.data.pojo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeadDTO {

    @Schema(description = "Código identificador único da empresa", example = "1")
    private Integer codigoEmpresa;

    @Schema(description = "Nome de usuário do responsável", example = "canetaazul")
    private String usernameResposavel;

    @Schema(description = "Nome completo do cliente", example = "Fulano de Tal")
    private String nome;

    @Schema(description = "Endereço de e-mail do cliente", example = "<EMAIL>")
    private String email;

    @Schema(description = "Número de telefone de contato do cliente", example = "(62)99999-9999")
    private String telefone;

    @Schema(description = "CPF do cliente (Cadastro de Pessoa Física)", example = "691.348.680-46")
    private String cpf;

    @Schema(description = "Sexo do cliente (ex.: Masculino, Feminino)", example = "M")
    private String sexo;

    @Schema(description = "Código de Endereçamento Postal (CEP) do cliente", example = "7400000")
    private String cep;

    @Schema(description = "Mensagem ou observação detalhada associada ao registro", example = "nda")
    private String mensagem;

    @Schema(description = "Indentificador conversão")
    private String identificador;

    @Schema(description = "horario agendamento")
    private String horarioAgendamento;

    @Schema(description = "data agendamento")
    private String dataAgendamento;

    @Schema(description = "tipo agendamento")
    private String tipoAgendamento;
}
