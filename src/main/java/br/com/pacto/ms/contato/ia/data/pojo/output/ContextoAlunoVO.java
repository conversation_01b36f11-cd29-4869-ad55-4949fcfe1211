package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.pojo.output.SituacaoClienteVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ContextoAlunoVO {

    @Schema(description = "Matrícula do aluno, pode ser nula, com no máximo 10 caracteres.")
    private String matricula;

    @Schema(required = true, description = "Situação do aluno, não nula, com exatamente 2 caracteres.")
    private SituacaoClienteVO situacao;

    @Schema(description = "Entidade Pessoa associada ao aluno.")
    private ContextoPessoaVO pessoa;

    @Schema(required = true, description = "Código identificador do aluno, não nulo.")
    private Integer codigo;

    @Schema(description = "Nome social do aluno, pode ser nulo, com no máximo 150 caracteres.")
    private String nomesocial;

    @Schema(description = "Indica se o aluno possui freepass, pode ser nulo.")
    private Integer freepass;

    @Schema(description = "Lista de parcelas do aluno")
    private List<ContextoParcelaVO> parcelas;

    @Schema(description = "Nome de enumerador identificador unico da principal fase do crm que o cliente se encontra")
    private String fase_crm;

    @Schema(description = "Lista de fases do crm que o cliente se encontra")
    private List<String> fases_crm;

    @Schema(description = "Link para para instalação e login do aplicativo treino")
    private String linkAppTreino;
}
