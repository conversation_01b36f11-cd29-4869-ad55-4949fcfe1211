package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.AberturametaEntity;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public @Repository interface AberturametaRepository extends PagingAndSortingRepository<AberturametaEntity, Integer> {

    Optional<List<AberturametaEntity>> findByColaboradorresponsavelAndEmpresaAndDia(Integer colaboradorresponsavel,
                                                                                    Integer empresa, Date dia);
}
