package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ColaboradorVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Timestamp acessoautorizadoate;

	private Boolean bloquearacessosemcheckin;

	private String codacesso;

	private String codacessoalternativo;

	private String coragendaprofissional;

	private String cref;

	private Integer departamento;

	private Integer diavencimento;

	private String emailMovidesk;

	private Boolean ematendimentopersonal;

	private byte[] fotopersonal;

	private Boolean funcionario;

	private String idexterno;

	private String observacao;

	private Integer pessoa;

	private float porccomissao;

	private Integer produtodefault;

	private Integer saldocreditopersonal;

	private Boolean sincronizadoMovidesk;

	private String situacao;

	private Integer tempoentreacessos;

	private String tokengoogle;

	private Integer uacodigo;

	private Integer usocreditospersonal;

	private EmpresaVO empresaBean;

	private List<EmpresaVO> empresas1;

	private List<EmpresaVO> empresas2;

	private List<UsuarioVO> usuarios;

	private List<VinculoVO> vinculos;

}