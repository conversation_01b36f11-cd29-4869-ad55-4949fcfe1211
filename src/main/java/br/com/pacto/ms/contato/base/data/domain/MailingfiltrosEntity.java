package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "mailingfiltros", schema = "public")
public class MailingfiltrosEntity {
    private int codigo;
    private Integer categoria;
    private String situacao;
    private Integer vinculocolaborador;
    private Integer modalidade;
    private Integer duracao;
    private Integer evento;
    private String codigoscategoria;
    private String codigosmodalidades;
    private String listasituacoes;
    private String codigosconsultores;
    private String codigosprofessores;
    private String codigosplanos;
    private String codigocontratoduracao;
    private Timestamp datacadastromin;
    private Timestamp datacadastromax;
    private Integer idademin;
    private Integer idademax;
    private Boolean feminino;
    private Boolean masculino;

    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo")
    public int getCodigo() {
        return codigo;
    }


    @Basic
    @Column(name = "categoria")
    public Integer getCategoria() {
        return categoria;
    }


    @Basic
    @Column(name = "situacao")
    public String getSituacao() {
        return situacao;
    }


    @Basic
    @Column(name = "vinculocolaborador")
    public Integer getVinculocolaborador() {
        return vinculocolaborador;
    }


    @Basic
    @Column(name = "modalidade")
    public Integer getModalidade() {
        return modalidade;
    }


    @Basic
    @Column(name = "duracao")
    public Integer getDuracao() {
        return duracao;
    }


    @Basic
    @Column(name = "evento")
    public Integer getEvento() {
        return evento;
    }


    @Basic
    @Column(name = "codigoscategoria")
    public String getCodigoscategoria() {
        return codigoscategoria;
    }


    @Basic
    @Column(name = "codigosmodalidades")
    public String getCodigosmodalidades() {
        return codigosmodalidades;
    }


    @Basic
    @Column(name = "listasituacoes")
    public String getListasituacoes() {
        return listasituacoes;
    }


    @Basic
    @Column(name = "codigosconsultores")
    public String getCodigosconsultores() {
        return codigosconsultores;
    }


    @Basic
    @Column(name = "codigosprofessores")
    public String getCodigosprofessores() {
        return codigosprofessores;
    }


    @Basic
    @Column(name = "codigosplanos")
    public String getCodigosplanos() {
        return codigosplanos;
    }


    @Basic
    @Column(name = "codigocontratoduracao")
    public String getCodigocontratoduracao() {
        return codigocontratoduracao;
    }

    @Basic
    @Column(name = "datacadastromin")
    public Timestamp getDatacadastromin() {
        return datacadastromin;
    }


    @Basic
    @Column(name = "datacadastromax")
    public Timestamp getDatacadastromax() {
        return datacadastromax;
    }


    @Basic
    @Column(name = "idademin")
    public Integer getIdademin() {
        return idademin;
    }


    @Basic
    @Column(name = "idademax")
    public Integer getIdademax() {
        return idademax;
    }


    @Basic
    @Column(name = "feminino")
    public Boolean getFeminino() {
        return feminino;
    }


    @Basic
    @Column(name = "masculino")
    public Boolean getMasculino() {
        return masculino;
    }

}
