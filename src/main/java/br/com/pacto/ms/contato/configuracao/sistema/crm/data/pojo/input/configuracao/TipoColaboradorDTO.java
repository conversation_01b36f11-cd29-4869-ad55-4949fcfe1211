package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TipoColaboradorDTO {
    @Schema(description = "Sigla do tipo de colaborador", example = "PR")
    private String sigla;
    @Schema(description = "Descrição do tipo de colaborador", example = "Professor")
    private String descricao;
}
