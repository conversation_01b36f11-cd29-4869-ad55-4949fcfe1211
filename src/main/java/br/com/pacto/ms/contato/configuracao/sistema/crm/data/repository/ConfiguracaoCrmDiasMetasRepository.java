package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmDiasMetasEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoEmailFechamentoMetaEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface ConfiguracaoCrmDiasMetasRepository extends PagingAndSortingRepository<ConfiguracaoCrmDiasMetasEntity, Integer> {

    Optional<List<ConfiguracaoCrmDiasMetasEntity>> findAllByFaseOrderByNrdias(FasesCRMEnum fase);

    @Query("select c from ConfiguracaoCrmDiasMetasEntity c where :fase is null or c.fase = :fase order by c.nrdias asc")
    Optional<List<ConfiguracaoCrmDiasMetasEntity>> consultarPorFaseTipoFase(FasesCRMEnum fase);
}

