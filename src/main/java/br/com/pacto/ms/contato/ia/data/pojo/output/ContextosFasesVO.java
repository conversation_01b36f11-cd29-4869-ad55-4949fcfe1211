package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.TipoFaseVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContextosFasesVO {

    @Schema(description = "Nome da fase")
    private String name;

    @Schema(description = "Descrição detalhada da fase")
    private String descricao;

    @Schema(description = "Indica se deve utilizar o primeiro contato", example = "true")
    private boolean utilizarPrimeiroContato;

    @Schema(description = "URL da imagem representativa da fase")
    private String imagem;

    @Schema(description = "Sigla da fase")
    private String sigla;

    @Schema(description = "Código numérico da fase", example = "100")
    private int codigo;

    @Schema(description = "Identificador único da fase")
    private String identificador;

    @Schema(description = "Ordem para exibição no totalizador")
    private int ordemTotalizador;

    @Schema(description = "Instrução fornecida para IA nessa fase")
    private String instrucaoIa;

    @Schema(description = "Tipo da fase, representado pelo objeto TipoFaseVO")
    private TipoFaseVO tipoFase;
}
