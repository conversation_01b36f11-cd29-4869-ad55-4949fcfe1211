package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;

import br.com.pacto.ms.comuns.Util;
import br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaixasDeHorarioDeAcessoVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmFaixasDeHorarioDeAcessoRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmFaixasDeHorarioDeAcessoService;
import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.exception.GenericException;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.PERIODO_EXISTENTE;

@Service
@AllArgsConstructor
public class ConfiguracaoCrmFaixasDeHorarioDeAcessoServiceImpl<T, DTO> extends Object implements ConfiguracaoCrmFaixasDeHorarioDeAcessoService<T, DTO> {

    private ModelMapper mapper;
    private ConfiguracaoCrmFaixasDeHorarioDeAcessoRepository repository;

    @Override
    @ObjectMapper(ConfiguracaoCrmFaixasDeHorarioDeAcessoVO.class)
    public T consultar() {
        return (T) repository.findAll();
    }

    @Override
    @ObjectMapper(ConfiguracaoCrmFaixasDeHorarioDeAcessoVO.class)
    public T incluir(ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO configuracaoCrmFaixasDeHorarioDeAcessoDTO) throws Exception {

        this.validarAdicaoFaixa(configuracaoCrmFaixasDeHorarioDeAcessoDTO);

        return (T) this.repository.save(mapper.map(configuracaoCrmFaixasDeHorarioDeAcessoDTO, ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity.class));
    }

    @Override
    public void excluir(Integer codigo) {
        ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity configuracaoCrmFaixasDeHorarioDeAcessoEntity = this.repository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        this.repository.delete(configuracaoCrmFaixasDeHorarioDeAcessoEntity);
    }

    @Override
    @ObjectMapper(ConfiguracaoCrmFaixasDeHorarioDeAcessoVO.class)
    public T alterar(Integer codigo, ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO dto) throws ParseException {
        ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity configuracaoCrmFaixasDeHorarioDeAcessoEntity = this.repository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        validarAdicaoFaixa(dto, configuracaoCrmFaixasDeHorarioDeAcessoEntity.getCodigo());
        this.mapper.map(dto, configuracaoCrmFaixasDeHorarioDeAcessoEntity);
        this.repository.save(configuracaoCrmFaixasDeHorarioDeAcessoEntity);
        return (T) configuracaoCrmFaixasDeHorarioDeAcessoEntity;
    }

    private void validarAdicaoFaixa(ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO faixaDTO) throws GenericException, ParseException {
        validarAdicaoFaixa(faixaDTO, null);
    }

    private void validarAdicaoFaixa(ConfiguracaoCrmFaixasDeHorarioDeAcessoDTO faixaDTO, Integer codigo) throws GenericException, ParseException {

        Util.validarHoraMinutos(faixaDTO.getHorarioinicio(), ExceptionMessage.HORARIO_INICIAL_INVALIDO);
        Util.validarHoraMinutos(faixaDTO.getHorariofim(), ExceptionMessage.HORARIO_FINAL_INVALIDO);

        List<ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity> faixasHorariosExistentes = this.repository.findAllNotContainsCodigo(codigo);


        for (ConfiguracaoCrmFaixasDeHorarioDeAcessoEntity faixaHorarioExiste : faixasHorariosExistentes) {
            if (faixaHorarioExiste.getDescricao().equals(faixaDTO.getDescricao())) {
                throw new GenericException(new ExceptionMessageVO().message(PERIODO_EXISTENTE));
            }

            Date dataInicioExistente = converterHorarioEmData(faixaHorarioExiste.getHorarioinicio());
            Date dataFimExistente = converterHorarioEmData(faixaHorarioExiste.getHorariofim());

            Date dataInicio = converterHorarioEmData(faixaDTO.getHorarioinicio());
            Date dataFim = converterHorarioEmData(faixaDTO.getHorariofim());

            if(betweenDates(dataInicio, dataInicioExistente, dataFimExistente)){
                throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.FAIXAHORARIO_CONCOMITANTES));
            }

            if(betweenDates(dataFim, dataInicioExistente, dataFimExistente)){
                throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.FAIXAHORARIO_CONCOMITANTES));
            }
        }
    }

    private boolean betweenDates(Date date, Date startDate, Date endDate) {
        return (date.after(startDate) || date.equals(startDate)) && (date.before(endDate) || date.equals(endDate));
    }

    private Date converterHorarioEmData(String horario) throws ParseException {
        final DateFormat dataf = DateFormat.getTimeInstance(
                DateFormat.SHORT, new Locale("pt", "BR"));
        return dataf.parse(horario);
    }
}
