package br.com.pacto.ms.contato.base.data.domain;

import br.com.pacto.ms.contato.avulso.data.domain.ClienteEntity;
import br.com.pacto.ms.contato.avulso.data.domain.PassivoEntity;
import br.com.pacto.ms.contato.base.data.domain.ClientEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "fecharmetadetalhado", schema = "public")
public class FecharMetaDetalhadoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo", nullable = false)
    private Integer codigo;

    @ManyToOne
    @JoinColumn(name = "fecharmeta", foreignKey = @ForeignKey(name = "fk_fecharmetadetalhado_fecharmeta"))
    private FecharMetaEntity fecharMeta;

    @Column(name = "obtevesucesso")
    private Boolean obteveSucesso;

    @ManyToOne
    @JoinColumn(name = "cliente", foreignKey = @ForeignKey(name = "fk_fecharmetadetalhado_cliente"))
    private ClienteEntity cliente;

    @ManyToOne
    @JoinColumn(name = "passivo", foreignKey = @ForeignKey(name = "fk_fecharmetadetalhado_passivo"))
    private PassivoEntity passivo;

    @Column(name = "motivoparaentraremcontatocomclienteposvenda", length = 50)
    private String motivoParaEntrarEmContatoComClientePosVenda;

    @ManyToOne
    @JoinColumn(name = "contrato", foreignKey = @ForeignKey(name = "fk_fecharmetadetalhado_contrato"))
    private ContratoEntity contrato;

    @ManyToOne
    @JoinColumn(name = "acessocliente", foreignKey = @ForeignKey(name = "fk_fecharmetadetalhado_acessocliente"))
    private AcessoClienteEntity acessoCliente;

    @Column(name = "codigoorigem", nullable = false)
    private Integer codigoOrigem;

    @Column(name = "origem", nullable = false, length = 50)
    private String origem;

    @Column(name = "pesorisco")
    private Integer pesoRisco;

    @Column(name = "observacao", length = 200)
    private String observacao;

    @Column(name = "sessoesfinais")
    private Integer sessoesFinais;

    @Column(name = "vendaavulsa")
    private Integer vendaAvulsa;

    @Column(name = "diassemagendamento")
    private Integer diasSemAgendamento;

    @Column(name = "repescagem", columnDefinition = "boolean default false")
    private Boolean repescagem;

    @Column(name = "tevecontato", columnDefinition = "boolean default false")
    private Boolean teveContato;

    @Column(name = "descconfiguracaodiasmetas", length = 50)
    private String descConfiguracaoDiasMetas;
}
