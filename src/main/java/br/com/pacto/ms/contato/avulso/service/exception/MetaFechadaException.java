package br.com.pacto.ms.contato.avulso.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;

public class MetaFechadaException extends CustomException {
	private static final long serialVersionUID = -9192943455755903832L;

	public MetaFechadaException() {
	}

	public MetaFechadaException(ExceptionMessageVO... messagesParamVO) {
		super(messagesParamVO);
	}

	public MetaFechadaException(String message) {
		super(message);
	}

	public MetaFechadaException(String message, ExceptionMessageVO... messagesParamVO) {
		super(message, messagesParamVO);
	}

	public MetaFechadaException(Throwable cause) {
		super(cause);
	}

	public MetaFechadaException(Throwable cause, ExceptionMessageVO... messagesParamVO) {
		super(cause, messagesParamVO);
	}

	public MetaFechadaException(String message, Throwable cause) {
		super(message, cause);
	}

	public MetaFechadaException(String message, Throwable cause, ExceptionMessageVO... messagesParamVO) {
		super(message, cause, messagesParamVO);
	}

}
