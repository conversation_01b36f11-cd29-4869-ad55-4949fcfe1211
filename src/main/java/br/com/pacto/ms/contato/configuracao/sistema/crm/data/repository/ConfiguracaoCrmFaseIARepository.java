package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface ConfiguracaoCrmFaseIARepository extends PagingAndSortingRepository<ConfiguracaoCrmFaseIAEntity, Integer> {

    List<ConfiguracaoCrmFaseIAEntity> findAll();

    @Query("SELECT cf FROM ConfiguracaoCrmFaseIAEntity cf WHERE cf.habilitar is true ")
    List<ConfiguracaoCrmFaseIAEntity> findAllActivate();

    @Query("SELECT cf FROM ConfiguracaoCrmFaseIAEntity cf WHERE cf.habilitar is true and cf.codigoEmpresa = :codigoEmpresa")
    List<ConfiguracaoCrmFaseIAEntity> findAllActivateAndCompany(Integer codigoEmpresa);

    @Query("SELECT cf FROM ConfiguracaoCrmFaseIAEntity cf WHERE cf.codigoEmpresa = :codigoEmpresa")
    List<ConfiguracaoCrmFaseIAEntity> findAllAndCompany(Integer codigoEmpresa);

    @Query("SELECT cf FROM ConfiguracaoCrmFaseIAEntity cf" +
            " WHERE cf.habilitar = true " +
            "AND ((:fases) is null or cf.fase in (:fases)) " +
            "AND cf.codigoEmpresa = :codigoEmpresa")
    List<ConfiguracaoCrmFaseIAEntity> findAllActiveFilteredByFases(@Param("fases") List<FasesCRMEnum> fases, Integer codigoEmpresa);

    @Query("SELECT cf FROM ConfiguracaoCrmFaseIAEntity cf" +
            " WHERE cf.habilitar = true " +
            "AND cf.nomeMetaExtra in (:fases) " +
            "AND cf.codigoEmpresa = :codigoEmpresa")
    List<ConfiguracaoCrmFaseIAEntity> findByMetaExtraName(@Param("fases") List<String> fases, Integer codigoEmpresa);

    List<ConfiguracaoCrmFaseIAEntity> findByFaseAndCodigoEmpresa(FasesCRMEnum fase, Integer codigoEmpresa);

    List<ConfiguracaoCrmFaseIAEntity> findByCodigoEmpresa( Integer codigoEmpresa);
}
