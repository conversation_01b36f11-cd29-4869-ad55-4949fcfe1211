package br.com.pacto.ms.contato.ia.data.proxy.proxy;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.lang.Nullable;

public interface ApiProxy {

    Object mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body);

    Object mandarPost(HttpHeaders headers, String urlCompleta, @Nullable String token, @Nullable Object body);

    <T> T mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body, Class<T> responseType);

    <T> T mandarPost(String urlCompleta, @Nullable String token, @Nullable Object body, ParameterizedTypeReference<T> responseType);

    <T> T mandarGet(String urlCompleta, @Nullable String token, Class<T> responseType);

    <T> T mandarPut(String urlCompleta, @Nullable String token, @Nullable Object body, Class<T> responseType);

}
