package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EmpresaVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;
	private String tokenSms;
	private String tokenSmsShortCode;

}
