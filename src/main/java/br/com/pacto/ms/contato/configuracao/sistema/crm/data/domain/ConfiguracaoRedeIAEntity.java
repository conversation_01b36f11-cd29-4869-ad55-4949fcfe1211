package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaoredeia", schema = "public")

public class ConfiguracaoRedeIAEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "chavebancomatriz")
    private String chaveBancoMatriz;

    @Column(name = "codigounidadematriz")
    private Integer codigoUnidadeMatriz;

    @Column(name = "tipoconfigrede")
    private String tipoConfigRede;
}