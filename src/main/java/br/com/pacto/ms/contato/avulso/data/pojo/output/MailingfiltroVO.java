package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MailingfiltroVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Integer categoria;

	private String codigocontratoduracao;

	private String codigoscategoria;

	private String codigosconsultores;

	private String codigosmodalidades;

	private String codigosplanos;

	private String codigosprofessores;

	private Timestamp datacadastromax;

	private Timestamp datacadastromin;

	private Integer duracao;

	private Integer evento;

	private Boolean feminino;

	private Integer idademax;

	private Integer idademin;

	private String listasituacoes;

	private Boolean masculino;

	private Integer modalidade;

	private String situacao;

	private Integer vinculocolaborador;

	private MaladiretaVO maladiretaBean;

}