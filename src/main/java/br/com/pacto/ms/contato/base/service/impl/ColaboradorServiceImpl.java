package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.domain.ColaboradorResumo;
import br.com.pacto.ms.contato.base.data.repository.ColaboradorRepository;
import br.com.pacto.ms.contato.base.service.contract.ColaboradorService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
@Service
public class ColaboradorServiceImpl <T> implements ColaboradorService {

    private ColaboradorRepository repository;


    @Override
    @LogExecution
    @ObjectMapper(ColaboradorResumo.class)
    public T buscarColaboradorPorNome(String nome) {
        return (T) repository.findyByname(nome);
    }
}
