package br.com.pacto.ms.contato.base.data.domain;

import br.com.pacto.ms.contato.avulso.data.domain.ClienteEntity;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.util.Date;

@Entity
@Table(name = "questionariocliente", schema = "public")
@Data
public class QuestionarioClienteEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(nullable = false)
    private Date data;

    @Column(nullable = false)
    private Integer cliente;

    @Column(nullable = false)
    private Integer questionario;

    @Column(columnDefinition = "text")
    private String observacao;

    @Column(name = "tipobv", columnDefinition = "smallint")
    private Short tipobv;

    @Column(name = "ultimaatualizacao")
    private Date ultimaAtualizacao;

    @Column(name = "origemsistema", columnDefinition = "smallint default 1")
    private Short origemsistema;

}
