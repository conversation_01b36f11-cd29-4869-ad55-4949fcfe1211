package br.com.pacto.ms.contato.base.data.domain;

import lombok.Data;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "planorecorrencia", schema = "public")
public class PlanoRecorrenciaEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "taxaadesao", nullable = false)
    private Double taxaadesao;

    @Column(name = "valoranuidade", nullable = false)
    private Double valoranuidade;

    @Column(name = "valormensal", nullable = false)
    private Double valormensal;

    @Column(name = "diaanuidade", nullable = false)
    private Integer diaanuidade;

    @Column(name = "mesanuidade", nullable = false)
    private Integer mesanuidade;

    @Column(name = "duracaoplano", nullable = false)
    private Integer duracaoplano;

    @Column(name = "qtddiasvenccancelauto", nullable = false)
    private Integer qtddiasvenccancelauto;

    @Column(name = "renovavelautomaticamente")
    private Boolean renovavelautomaticamente;

    @OneToOne
    @JoinColumn(name = "plano", nullable = false, referencedColumnName = "codigo")
    @ToString.Exclude
    private PlanoEntity plano;

    @Column(name = "naorenovarparcelavencida", nullable = false)
    private Boolean naorenovarparcelavencida = false;

    @Column(name = "naocobraranuidadeproporcional", nullable = false)
    private Boolean naocobraranuidadeproporcional = false;

    @Column(name = "anuidadenaparcela", nullable = false)
    private Boolean anuidadenaparcela = false;

    @Column(name = "parcelaanuidade")
    private Integer parcelaanuidade;

    @Column(name = "cancelamentoproporcional", nullable = false)
    private Boolean cancelamentoproporcional = false;

    @Column(name = "qtddiascobrarproximaparcela")
    private Integer qtddiascobrarproximaparcela;

    @Column(name = "qtddiascobraranuidadetotal")
    private Integer qtddiascobraranuidadetotal;

    @Column(name = "gerarparcelasvalordiferente")
    private Boolean gerarparcelasvalordiferente;

    @Column(name = "parcelaranuidade", nullable = false)
    private Boolean parcelaranuidade = false;

    @Column(name = "cancelamentoproporcionalsomenterenovacao", nullable = false)
    private Boolean cancelamentoproporcionalsomenterenovacao = false;

    @Column(name = "gerarparcelasvalordiferenterenovacao")
    private Boolean gerarparcelasvalordiferenterenovacao;
}