package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.SituacaoclientesinteticodwEntity;
import br.com.pacto.ms.contato.avulso.data.domain.UsuarioMovelEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

public interface UsuarioMovelRepository
            extends PagingAndSortingRepository<UsuarioMovelEntity, Integer> {

    @Query("SELECT u FROM UsuarioMovelEntity u WHERE u.cliente = :codigoCliente")
    Optional<UsuarioMovelEntity> consultarPorCliente(Integer codigoCliente);
}
