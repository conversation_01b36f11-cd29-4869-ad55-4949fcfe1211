package br.com.pacto.ms.contato.avulso.data.proxy;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.net.URI;

@FeignClient(url = "${proxy.microservice.api}", name = "notificaoApp")
public interface NotificacaoAppProxy {

    @PostMapping(path = "/notificacoes/{key}/gerarNotificacao")
    String gerarNotificacao(URI baseUrl,
                            @PathVariable("key") final String key,
                            @RequestParam("idCliente") final String idCliente,
                            @RequestParam("titulo") final String titulo,
                            @RequestParam("textoCRM") final String textoCRM,
                            @RequestParam("opcoes") final String opcoes);
}

