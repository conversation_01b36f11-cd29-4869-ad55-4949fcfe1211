package br.com.pacto.ms.contato.log.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import br.com.pacto.ms.contato.avulso.data.domain.UsuarioEntity;
import br.com.pacto.ms.contato.log.data.repository.UsuarioRepository;
import br.com.pacto.ms.contato.log.service.contract.UsuarioService;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Optional;

@AllArgsConstructor
public @Service  class UsuarioServiceImpl<T> implements UsuarioService {

    private UsuarioRepository usuarioRepository;

    @Override
    public PessoaEntity consultarPessoa(Integer codigo) {
        UsuarioEntity usuario = this.usuarioRepository.findById(codigo)
                .orElseThrow(() -> new DataNotFoundException("Nâo foi possível encontrar o usuário com o código " + codigo));

        if(usuario.getColaborador() != null && usuario.getColaborador().getPessoa() != null){
            return usuario.getColaborador().getPessoa();
        }

        if(usuario.getCliente() != null && usuario.getCliente().getPessoa() != null){
            return  usuario.getCliente().getPessoa();
        }

        throw new DataNotFoundException("Nâo foi possível encontrar a pessoa do usuário com o código " + codigo);
    }

    @Override
    public UsuarioEntity consultarPorUsername(String nome) {
        return this.usuarioRepository.findByUsername(nome)
                .orElseThrow(() -> new DataNotFoundException("Nâo foi possível encontrar o usuário com username:  " + nome));
    }

}
