package br.com.pacto.ms.contato.avulso.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.input.ReceptivoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ReceptivoFiltro;
import br.com.pacto.ms.contato.avulso.data.pojo.output.PassivoVO;
import org.springframework.data.domain.Page;

public interface ReceptivoService {
    Page<PassivoVO> buscar(ReceptivoFiltro receptivoFiltro);
    PassivoVO cadastrarReceptivo(ReceptivoDTO receptivoDTO);
    PassivoVO obter(Integer id);
}
