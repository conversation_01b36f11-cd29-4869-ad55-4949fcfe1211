package br.com.pacto.ms.contato.core.data.pojo.enums;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class FasesCRMEnumConverter implements AttributeConverter<FasesCRMEnum, Integer> {

    @Override
    public Integer convertToDatabaseColumn(FasesCRMEnum fasesCRMEnum) {
        if(fasesCRMEnum == null){
            return null;
        }
        return fasesCRMEnum.getCodigo();
    }

    @Override
    public FasesCRMEnum convertToEntityAttribute(Integer codigo) {
        if (codigo == null) {
            return null;
        }
        return FasesCRMEnum.getFase(codigo);
    }
}

