package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.avulso.data.pojo.output.MaladiretaVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.*;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmIAService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.ia.data.pojo.input.InserirPdfRequestVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.InserirPdfResponseVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.InstanciaVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.QrCodeVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePDFDocumentoVO;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static org.springframework.http.HttpStatus.NO_CONTENT;

@SuppressWarnings("unchecked")
@Validated
@Tag(name = "Configurações da IA", description = "Gestão de configurações do módulo CRM")
@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/configuracao/ia")
public class ConfiguracaoCrmIAController extends BaseController {

    private final RequestService requestService;
    private final ConfiguracaoCrmIAService configuracaoCrmIAService;

    private void disableLegacyResponse() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
    }

    @Operation(summary = "Consultar Configuração IA")
    @GetMapping("/consultar")
    public ResponseEntity<ConfiguracaoCrmIADTO> consultarConfiguracao(
            @Parameter(name = "idEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "idEmpresa", required = false) Integer idEmpresa
    ) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCrmIADTO>) super.finish(configuracaoCrmIAService.consultarConfiguracao(idEmpresa));
    }


    @Operation(summary = "Consultar fases IA")
    @GetMapping("/consultar/fases")
    public ResponseEntity<List<ConfiguracaoCrmFaseIADTO>> consultarFases(
            @Parameter(name = "idEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "idEmpresa", required = false) Integer idEmpresa
    ) {
        disableLegacyResponse();
        return (ResponseEntity<List<ConfiguracaoCrmFaseIADTO>>) super.finish(configuracaoCrmIAService.consultarFases(idEmpresa));
    }

    @Operation(summary = "Incluir configuração IA")
    @PostMapping("/incluir")
    public ResponseEntity<ConfiguracaoCrmIADTO> incluirConfiguracao(@RequestBody @Valid ConfiguracaoCrmIADTO configuracaoCrmIADTO) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCrmIADTO>) super.finish(configuracaoCrmIAService.incluirConfiguracao(configuracaoCrmIADTO));
    }

    @Operation(summary = "Excluir documento por empresa")
    @DeleteMapping("/delete/pdf")
    public ResponseEntity<InserirPdfResponseVO> excluirDocumentoPorEmpresa(@RequestParam(name = "empresa") Integer empresa) {
        disableLegacyResponse();
        return (ResponseEntity<InserirPdfResponseVO>) super.finish(configuracaoCrmIAService.deletarPDF(empresa));
    }

    @Operation(summary = "Incluir PDF")
    @PostMapping("/incluir/pdf")
    public ResponseEntity<InserirPdfResponseVO> incluirConfiguracaoNoConversas(@RequestBody @Valid InserirPdfRequestVO inserirPdfRequestVO) {
        disableLegacyResponse();
        return (ResponseEntity<InserirPdfResponseVO>) super.finish(configuracaoCrmIAService.incluirPDFConversas(inserirPdfRequestVO));
    }

    @Operation(summary = "Consultar PDF")
    @GetMapping("/consultar/pdf")
    public ResponseEntity<ResponsePDFDocumentoVO> consultarConfiguracaoNoConversas(
            @RequestParam(name = "empresa") Integer empresa
    ) {
        disableLegacyResponse();
        return (ResponseEntity<ResponsePDFDocumentoVO>) super.finish(configuracaoCrmIAService.consultarPDFConversas(empresa));
    }

    @Operation(summary = "Consultar fase específica de IA")
    @GetMapping("/consultar/fase")
    public ResponseEntity<ConfiguracaoCrmFaseIADTO> consultarFase(
            @Parameter(name = "fase", description = "Identificador da fase do CRM", example = "EX_ALUNOS")
            @RequestParam(required = false) FasesCRMEnum fase,
            @Parameter(name = "idEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "idEmpresa", required = false) Integer idEmpresa) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCrmFaseIADTO>) super.finish(configuracaoCrmIAService.consultar(fase, idEmpresa));
    }

    @Operation(summary = "Incluir múltiplas fases de configuração IA")
    @PostMapping("/incluir/fases/{codigoEmpresa}")
    public ResponseEntity<ConfiguracaoCrmFaseIADTO> incluirFases(
            @RequestBody @Valid List<ConfiguracaoCrmFaseIADTO> configuracoesCrmFaseIADTO,
            @PathVariable @Parameter(name = "codigoEmpresa", description = "Código da configuração de meta", example = "1") Integer codigoEmpresa) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCrmFaseIADTO>) super.finish(configuracaoCrmIAService.incluirConfiguracaoDeFases(configuracoesCrmFaseIADTO, codigoEmpresa));
    }

    @Deprecated
    @Operation(summary = "Alterar fase de configuração IA")
    @PutMapping("/{codigo}")
    public ResponseEntity<ConfiguracaoCrmFaseIADTO> alterarFase(
            @PathVariable @Parameter(name = "codigo", description = "Código da configuração de meta", example = "1") Integer codigo,
            @RequestBody @Valid ConfiguracaoCrmFaseIADTO configuracaoCrmFaseIADTO) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoCrmFaseIADTO>) super.finish(configuracaoCrmIAService.alterar(codigo, configuracaoCrmFaseIADTO));
    }

    @Deprecated
    @Operation(summary = "Excluir configuração de fase IA")
    @DeleteMapping("/{codigo}")
    @ResponseStatus(NO_CONTENT)
    public ResponseEntity<Void> excluirFase(
            @PathVariable @Parameter(name = "codigo", description = "Código do registro", example = "1") Integer codigo) {
        configuracaoCrmIAService.excluir(codigo);
        return (ResponseEntity<Void>) super.finish();
    }

    @Deprecated
    @Operation(summary = "Consultar configuração de pós-vendas")
    @GetMapping("/consultar/posvendas")
    public ResponseEntity<List<ConfiguracaoCrmIAPosVendaDTO>> consultarConfigPosVenda() {
        disableLegacyResponse();
        return (ResponseEntity<List<ConfiguracaoCrmIAPosVendaDTO>>) super.finish(configuracaoCrmIAService.consultarConfigPosVenda());
    }

    @Deprecated
    @Operation(summary = "Incluir configuração de pós-vendas")
    @PostMapping("/posvenda/incluir")
    public ResponseEntity<List<ConfiguracaoCrmIAPosVendaDTO>> incluirPosVenda(@RequestBody @Valid List<ConfiguracaoCrmIAPosVendaDTO> configCrmIAPosVendaDTOS) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<List<ConfiguracaoCrmIAPosVendaDTO>>) super.finish(configuracaoCrmIAService.incluirPosVenda(configCrmIAPosVendaDTOS));
    }

    @Operation(summary = "Obter QR Code do WhatsApp")
    @GetMapping("/qrcode/whatsapp")
    public ResponseEntity<QrCodeVO> obterQrCodeWhatsApp(
            @RequestParam(name = "idInstancia") String idInstancia,
            @RequestParam(name = "token") String token
    ) {
        disableLegacyResponse();
        QrCodeVO codeVO = (QrCodeVO) configuracaoCrmIAService.obterQrCode(idInstancia, token);
        return (ResponseEntity<QrCodeVO>) super.finish(codeVO);
    }

    @Operation(summary = "Criar Instancia para conversas IA")
    @PostMapping("/criar-instancia")
    public ResponseEntity<InstanciaVO> criarInstancia(
            @RequestBody ConfiguracaoInstanciaDTO configuracaoInstanciaDTO
    ) {
        disableLegacyResponse();
        return (ResponseEntity<InstanciaVO>) super.finish(configuracaoCrmIAService.criarInstancia(configuracaoInstanciaDTO));
    }

    @Operation(summary = "Desconectar instancia do Whatsapp")
    @PostMapping("/desconectar-instancia")
    public ResponseEntity<?> desconectarInstancia(
            @Parameter(name = "codigoEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "codigoEmpresa", required = false) Integer codigoEmpresa
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoCrmIAService.desconectarInstancia(codigoEmpresa));
    }

    @Operation(summary = "Verifica se instancia está ativa e conectada")
    @GetMapping("/verificar-instancia")
    public ResponseEntity<?> verificarInstancia(
            @RequestParam(name = "idInstancia") String idInstancia,
            @RequestParam(name = "token") String token
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoCrmIAService.verificarStatusInstancia(idInstancia, token));
    }

    @Operation(summary = "Cancelar instancia conectada")
    @GetMapping("/cancelar-instancia")
    public ResponseEntity<?> cancelarInstancia(
            @RequestParam(name = "instance_id") String idInstancia,
            @RequestParam(name = "token") String token,
            @RequestParam(value = "codigoEmpresa") Integer codigoEmpresa
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoCrmIAService.cancelarInstancia(idInstancia, token, codigoEmpresa));
    }

    @Operation(summary = "Obter chave empresa")
    @GetMapping("/instancia-empresa")
    public ResponseEntity<?> cancelarInstancia(
            @RequestParam(name = "chaveEmpresa") String chaveEmpresa
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoCrmIAService.obterInstanciaEmpresa(chaveEmpresa));
    }

    @Operation(summary = "Consultar fases extras do CRM geradas pela IA")
    @GetMapping("consultar/fases-extra-ia")
    public ResponseEntity<List<MaladiretaVO>> consultarFaseExtrasIA(
            @Parameter(name = "codigoEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "codigoEmpresa", required = false) Integer codigoEmpresa
    ) {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
        return (ResponseEntity<List<MaladiretaVO>>) super.finish(configuracaoCrmIAService.consultarFaseExtrasIA(codigoEmpresa));
    }

    @Operation(summary = "Verificador dados da  instancia ")
    @GetMapping("/status-whatsapp-conectado")
    public ResponseEntity<?> obterInformacoesDispositivoConectado(
            @RequestParam(name = "instance_id") String idInstancia,
            @RequestParam(name = "token") String token
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoCrmIAService.obterInformacoesDispositivoConectado(idInstancia, token));
    }

    @Operation(summary = "Incluir configuração de rede IA")
    @PostMapping("/rede")
    public ResponseEntity<ConfiguracaoRedeIADTO> incluirConfiguracao(@RequestBody ConfiguracaoRedeIADTO configuracaoCrmIADTO) {
        disableLegacyResponse();
        return (ResponseEntity<ConfiguracaoRedeIADTO>) super.finish(configuracaoCrmIAService.incluirConfiguracaoRede(configuracaoCrmIADTO));
    }

    @Operation(summary = "Consultar unidades da empresa por banco")
    @GetMapping("/rede/unidade")
    @Deprecated
    public ResponseEntity<List<ResumoEmpresaVO>> buscarUnidadesDaEmpresaPorBanco(@RequestParam(name = "chaveBanco") String chaveBanco) {
        disableLegacyResponse();
        return (ResponseEntity<List<ResumoEmpresaVO>>) super.finish(configuracaoCrmIAService.buscarUnidadesDaEmpresaPorBanco(chaveBanco));
    }

}
