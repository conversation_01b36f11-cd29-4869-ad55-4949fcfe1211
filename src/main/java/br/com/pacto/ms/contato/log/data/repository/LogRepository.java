package br.com.pacto.ms.contato.log.data.repository;

import br.com.pacto.ms.contato.log.data.domain.LogEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface LogRepository extends PagingAndSortingRepository<LogEntity, Integer> {

    @Query("select l.dataAlteracao, 'YYYY-MM-DD HH24:MI', l.operacao, l.responsavelAlteracao " +
            "from LogEntity l " +
            "where :nomesentidades is null or lower(l.nomeEntidade) in (:nomesentidades) " +
            "and (:pesquisa is null or (" +
            "       lower(l.nomeCampo) like concat('%', lower(:pesquisa), '%') or " +
            "       lower(l.nomeCampoDescricao) like concat('%', lower(:pesquisa), '%') or " +
            "       lower(l.responsavelAlteracao) like concat('%', lower(:pesquisa) ,'%') " +
            "   )" +
            ") ")
    Optional<Page<LogEntity>> consultarResumoAgrupadoPorMinuto(String pesquisa, List<String> nomesentidades, Pageable pageable);

    @Query("select l from LogEntity l " +
            "where :nomesentidades is null or lower(l.nomeEntidade) in (:nomesentidades) " +
            "and (:operacoes is null or cast(translate(lower(l.operacao), 'áéíóúàèìòùâêîôûãõäëïöüç', 'aeiouaeiouaeiouaoaeiouc') as string) in (:operacoes)) " +
            "and (cast(:dataInicio as date ) is null or l.dataAlteracao >=  :dataInicio ) " +
            "and (cast(:dataFim as date) is null or l.dataAlteracao <= :dataFim) " +
            "and (:pesquisa is null or (" +
            "       cast(translate(lower(l.nomeCampo), 'áéíóúàèìòùâêîôûãõäëïöüç', 'aeiouaeiouaeiouaoaeiouc') as string) like concat('%',lower(:pesquisa), '%') or " +
            "       cast(translate(lower(l.nomeCampoDescricao), 'áéíóúàèìòùâêîôûãõäëïöüç', 'aeiouaeiouaeiouaoaeiouc') as string) like concat('%',lower(:pesquisa), '%') or " +
            "       cast(translate(lower(l.responsavelAlteracao), 'áéíóúàèìòùâêîôûãõäëïöüç', 'aeiouaeiouaeiouaoaeiouc') as string) like concat('%', lower(:pesquisa) ,'%') " +
            "   ) " +
            ") ")
    Page<List<LogEntity>> consultar(String pesquisa, List<String> nomesentidades, List<String> operacoes, Date dataInicio, Date dataFim, Pageable pageable);
}
