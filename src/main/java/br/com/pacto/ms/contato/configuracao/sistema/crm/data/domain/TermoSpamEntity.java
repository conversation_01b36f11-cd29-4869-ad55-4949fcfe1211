package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogTagTransform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "termospam", schema = "public")
@LogTagTransform(TermoSpamTagTransform.class)
public class TermoSpamEntity {

    @Id
    @Column(name = "termo")
    public String termo;

}
