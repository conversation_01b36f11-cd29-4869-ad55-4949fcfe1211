package br.com.pacto.ms.contato.avulso.data.pojo.input;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.sql.Date;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HistoricoContatoDTO implements Serializable {

    @JsonIgnore
    private Integer codigoHistorico;

    private Timestamp dia;

    private Integer cliente;

    private Integer passivo;

    private Integer indicado;

    private Integer maladireta;

    @NotEmpty
    private String observacao;

    @Size(max = 2)
    private String tipooperacao;

    private Integer responsavelcadastro;

    private Integer objecao;

    private Integer agenda;

    @Size(max = 50)
    private String fase;

    @Size(max = 255)
    private String resultado;

    @Size(max = 2)
    private String tipocontato;

    @Size(max = 2)
    private String grausatisfacao;

    private Boolean contatoavulso;

    @Size(max = 120)
    private String resposta;

    @Size(max = 360)
    private String opcoes;

    private Integer codigonotificacao;

    private Integer conviteaulaexperimental;

    private Date dataproximoenvio;

    private Boolean wagienvi;

    private Integer codigoObjecao;

    private Integer codigoAgenda;

    @Size(max = 2)
    private String tiporesultado;

    private Integer empresa;

    private String telefoneCelular;

    private String email;

}
