package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pactosolucoes.commons.util.annotation.LogTagTransformContract;

public class ConfiguracaoCrmIATagTransform  implements LogTagTransformContract<ConfiguracaoCrmFaseIAEntity> {

    @Override
    public String transform(ConfiguracaoCrmFaseIAEntity config) {
        if(config == null || config.getFase() == null){
            return null;
        }
        return config.getFase().getName();
    }
}
