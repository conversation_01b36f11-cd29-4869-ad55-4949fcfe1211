package br.com.pacto.ms.contato.avulso.data.repository.factory;

import br.com.pacto.ms.contato.core.data.pojo.enums.TipoHistoricoObjecaoEnum;
import br.com.pacto.ms.contato.avulso.data.pojo.input.HistoricoObjecaoDTO;
import br.com.pacto.ms.contato.avulso.service.contract.ClienteService;
import br.com.pacto.ms.contato.avulso.service.contract.HistoricoObjecaoService;
import br.com.pacto.ms.contato.avulso.service.contract.IndicadoService;
import br.com.pacto.ms.contato.avulso.service.contract.PassivoService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

@Component
@AllArgsConstructor
public class HistoricoObjecaoDefinitivaFactory {

    private ClienteService<?> clienteService;
    private IndicadoService<?> indicadoService;
    private PassivoService<?> passivoService;

    private Map<TipoHistoricoObjecaoEnum, HistoricoObjecaoService> registered = new HashMap<>();

    @PostConstruct
    private void init() {
        registered.put(TipoHistoricoObjecaoEnum.CLIENTE, clienteService);
        registered.put(TipoHistoricoObjecaoEnum.PASSIVO, passivoService);
        registered.put(TipoHistoricoObjecaoEnum.INDICADO, indicadoService);
    }

    public HistoricoObjecaoService<?> get(HistoricoObjecaoDTO dto) {
        return registered.get(dto.getTipo());
    }

}
