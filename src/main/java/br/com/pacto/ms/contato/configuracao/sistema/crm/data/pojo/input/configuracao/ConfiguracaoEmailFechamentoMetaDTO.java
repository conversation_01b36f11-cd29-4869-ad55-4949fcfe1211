package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ConfiguracaoEmailFechamentoMetaDTO {

    @Schema(description = "Email de fechamento de meta", example = "<EMAIL>")
    private String email;
    @Schema(description = "Código a empresa", example = "1")
    private Integer empresa;
}
