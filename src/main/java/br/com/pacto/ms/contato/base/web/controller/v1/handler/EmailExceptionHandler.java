package br.com.pacto.ms.contato.base.web.controller.v1.handler;

import br.com.pacto.ms.contato.base.service.exception.EmailException;
import br.com.pactosolucoes.commons.data.vo.ResponseImplLegacyVO;
import br.com.pactosolucoes.commons.data.vo.ResponseImplVO;
import br.com.pactosolucoes.commons.data.vo.ResponseVO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.EMAIL_EXCEPTION;
import static br.com.pactosolucoes.commons.enums.message.APIStructureMessages.PROCESS_FINISHED_ERROR;

public @ControllerAdvice
class EmailExceptionHandler {

    private @Autowired
    RequestService requestService;

    @ExceptionHandler(value = {EmailException.class})
    public ResponseEntity<?> handleArchetypeException(EmailException exception) {
        ResponseVO responseVO = null;

        if (requestService.getCurrentConfiguration().getReponseLegacy()) {
            responseVO = ResponseImplLegacyVO.init()
                    .addMessage(EMAIL_EXCEPTION);
        } else {
            responseVO = ResponseImplVO.init()
                    .addMessage(EMAIL_EXCEPTION)
                    .addMessage(PROCESS_FINISHED_ERROR);
        }

        return new ResponseEntity<ResponseVO>(responseVO, null, EMAIL_EXCEPTION.getStatusCode());

    }
}
