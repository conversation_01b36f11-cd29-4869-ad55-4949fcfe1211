package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos;

import javax.validation.Valid;

import org.springframework.validation.annotation.Validated;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Validated
public class ConfiguracaoCRMDadosBasicosDTO {

  @Valid
  private GeralDTO geral;
  @Valid
  private ClientesEmDiaForaDaAcademiaDTO cliente;
  @Valid
  private ContratosDTO contrato;
  @Valid
  private AgendamentosDTO agendamento;
  @Valid
  private IndicacoesDTO indicacao;
  @Valid
  private TreinaWebDTO treinoWeb;


//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    @Id
//    @Column(name = "codigo", nullable = false)
//    private Integer codigo;
//    @Basic
//    @Column(name = "remetentepadrao", nullable = true, length = 50)
//    private String remetentepadrao;
//    @Basic
//    @Column(name = "emailpadrao", nullable = true, length = 50)
//    private String emailpadrao;
//    @Basic
//    @Column(name = "mailserver", nullable = true, length = 50)
//    private String mailserver;
//    @Basic
//    @Column(name = "login", nullable = true, length = 50)
//    private String login;
//    @Basic
//    @Column(name = "senha", nullable = true, length = 80)
//    private String senha;
//    @Basic
//
//    @Basic

//    @Basic

//    @Basic
//
//    @Basic
//
//    @Basic

//    @Basic
//    @Column(name = "conexaosegura", nullable = true)
//    private Boolean conexaosegura;
//    @Basic
//    @Column(name = "dividirfase", nullable = true)
//    private Boolean dividirfase;
//    @Basic

//    @Basic
//    @Column(name = "nrdiasposterioresagendamento", nullable = true)

//    @Basic
//    @Column(name = "nrdiaslimiteagendamentofuturo", nullable = true)
//
//    @Basic
//    @Column(name = "bloqueartermospam", nullable = true)
//    private Boolean bloqueartermospam;
//    @Basic
//    @Column(name = "urljenkins", nullable = true, length = -1)
//    private String urljenkins;
//    @Basic
//    @Column(name = "urlmailing", nullable = true, length = -1)
//    private String urlmailing;
//    @Basic
//    @Column(name = "iniciartls", nullable = true)
//    private Boolean iniciartls;
//    @Basic
//    @Column(name = "qtdindicacoesmes", nullable = true)
//    ;
//    @Basic
//    @Column(name = "qtdconversoesvendasmes", nullable = true)
//    private Integer qtdconversoesvendasmes;
//    @Basic
//    @Column(name = "nrdiasposagendamentoconversaoexaluno", nullable = true)
//    private Integer nrdiasposagendamentoconversaoexaluno;
//    @Basic
//    @Column(name = "qtdconversoesexalunosmes", nullable = true)
//    private Integer qtdconversoesexalunosmes;
//    @Basic
//    @Column(name = "incluircontratosrenovados", nullable = true)
//    private Boolean incluircontratosrenovados;
//    @Basic
//    @Column(name = "considerarprofessortreinoweb", nullable = true)
//
//    @Basic
//
//    @Basic
//    @Column(name = "nrdiascontarresultado", nullable = true)
//
//    @Basic
//    @Column(name = "portaserver", nullable = true, length = 10)
//    private String portaserver;
//    @Basic
//    @Column(name = "obrigatorioseguirordemmetas", nullable = true)
//    private Boolean obrigatorioseguirordemmetas;
//    @Basic
//    @Column(name = "ordenacaometas", nullable = true, length = 120)
//    private String ordenacaometas;
//    @Basic
//    @Column(name = "enviaremailindividualmente", nullable = true)
//    private Boolean enviaremailindividualmente;
//    @Basic
//    @Column(name = "remetentepadraomailing", nullable = true)
//    private Integer remetentepadraomailing;
//    @Basic
//
//    @Basic
//
//    @Basic
//    @Basic

//    @Basic
//
//    @Basic
//    @Column(name = "usasmtps", nullable = true)
//    private Boolean usasmtps;
//    @Basic
//    @Column(name = "mailingftpserver", nullable = true, length = -1)
//    private String mailingftpserver;
//    @Basic
//    @Column(name = "mailingftpuser", nullable = true, length = -1)
//    private String mailingftpuser;
//    @Basic
//    @Column(name = "mailingftppass", nullable = true, length = -1)
//    private String mailingftppass;
//    @Basic
//    @Column(name = "mailingftpport", nullable = true)
//    private Integer mailingftpport;
//    @Basic
//    @Column(name = "mailingftptype", nullable = true, length = 5)
//    private String mailingftptype;
//    @Basic
//    @Column(name = "mailingftpfolder", nullable = true, length = -1)
//    private String mailingftpfolder;
//    @Basic
//    @Column(name = "tokenbitly", nullable = true, length = 255)
//    private String tokenbitly;
//    @Basic
//
//    @Basic
//    @Column(name = "integracaosendyativa", nullable = true)
//    private Boolean integracaosendyativa;
//    @Basic
//    @Column(name = "integracaopacto", nullable = true)
//    private Boolean integracaopacto;
//    @Basic
//    @Column(name = "limitediarioemails", nullable = true)
//    private Integer limitediarioemails;
//    @Basic
//    @Column(name = "limitemensalpacto", nullable = true)
//    private Integer limitemensalpacto;
//    @Basic
//    @Column(name = "usarremetentepadraogeral", nullable = true)
//    private Boolean usarremetentepadraogeral;

  }
