package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.base.data.domain.ProdutoEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogFieldValueTransform;
import br.com.pactosolucoes.commons.util.annotation.LogTagTransform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaodiasmetas", schema = "public")
@EntityListeners(LogListener.class)
@LogTagTransform(ConfiguracaoCrmDiasMetasTagTransform.class)
public class ConfiguracaoCrmDiasMetasEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Column(name = "nrdias")
    private Integer nrdias;
    @Column
    private String descricao;
    @Column
    private FasesCRMEnum fase;

    @OneToOne
    @JoinColumn(name = "produto", referencedColumnName = "codigo")
    private ProdutoEntity produto;

}
