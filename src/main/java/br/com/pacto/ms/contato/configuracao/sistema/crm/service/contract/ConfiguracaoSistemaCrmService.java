package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaosistemacrmEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoSistemaCrmDTO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;

import java.util.List;

public interface ConfiguracaoSistemaCrmService<T, DTO> {

    ConfiguracaosistemacrmEntity consultarConfiguracaoAtual();

    T buscarDadosBasicos();

    T salvarDadosBasicos(DTO dtoObject);

    T buscarEmail();

    T salvarEmail(DTO dtoObject);

    T buscarPosVenda();

    T salvarPosVenda(DTO dtoObject);

    T buscarDadosIntegracao();

    T consultar();

    T alterar(ConfiguracaoSistemaCrmDTO dto);

    List<FasesCRMEnum> consultarOrdenacaoMetas();
}
