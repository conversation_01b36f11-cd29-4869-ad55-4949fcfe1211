package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.avulso.data.domain.UsuarioEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoSistemaCrmDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoSistemaCrmVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.log.data.repository.UsuarioRepository;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaosistemacrmEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.ConfiguracaoCRMDadosBasicosVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.IntegracaoCRMVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaosistemacrmRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ConfiguracaosistemcrmServiceImpl<T, DTO> extends Object
        implements ConfiguracaoSistemaCrmService<T, DTO> {

    private static final Integer ID_CONFIGURACAO_CRM = 1;

    private ModelMapper mapper;
    private ConfiguracaosistemacrmRepository repository;
    private UsuarioRepository usuarioRepository;

    private ConfiguracaosistemacrmEntity salvar(DTO dtoObject) {
        ConfiguracaosistemacrmEntity oldValues = consultarConfiguracaoAtual();
        ConfiguracaosistemacrmEntity newValues = mapper.map(dtoObject, ConfiguracaosistemacrmEntity.class);
        mapper.map(newValues, oldValues);
        return repository.save(oldValues);
    }

    public ConfiguracaosistemacrmEntity consultarConfiguracaoAtual() {
        return repository.findFirstByOrderByCodigoAsc();
    }

    @Override
    @LogExecution
    @ObjectMapper(ConfiguracaoCRMDadosBasicosVO.class)
    @Transactional(rollbackFor = RuntimeException.class)
    public T buscarDadosBasicos() {
        return (T) consultarConfiguracaoAtual();
    }

    @Override
    @LogExecution
    @ObjectMapper(ConfiguracaoCRMDadosBasicosVO.class)
    public T salvarDadosBasicos(DTO dtoObject) {
        return (T) salvar(dtoObject);
    }

    @Override
    @LogExecution
    @ObjectMapper(ConfiguracaoCRMDadosBasicosVO.class)
    public T buscarEmail() {
        return (T) consultarConfiguracaoAtual();
    }

    @Override
    @LogExecution
    @ObjectMapper(ConfiguracaoCRMDadosBasicosVO.class)
    public T salvarEmail(DTO dtoObject) {
        return (T) salvar(dtoObject);
    }

    @Override
    @LogExecution
    @ObjectMapper(ConfiguracaoCRMDadosBasicosVO.class)
    public T buscarPosVenda() {
        return (T) consultarConfiguracaoAtual();
    }

    @Override
    @LogExecution
    @ObjectMapper(ConfiguracaoCRMDadosBasicosVO.class)
    public T salvarPosVenda(DTO dtoObject) {
        return (T) salvar(dtoObject);
    }

    @Override
    @LogExecution
    @ObjectMapper(IntegracaoCRMVO.class)
    public T buscarDadosIntegracao() {
        return (T) consultarConfiguracaoAtual();
    }

    @Override
    @ObjectMapper(ConfiguracaoSistemaCrmVO.class)
    public T consultar() {
        ConfiguracaosistemacrmEntity configuracaoAtual = consultarConfiguracaoAtual();

        Optional<UsuarioEntity> remetentePadrao = usuarioRepository.findByNome(configuracaoAtual.getRemetentepadrao());

        remetentePadrao.ifPresent(configuracaoAtual::setRemetentePadraoVO);
        return (T) configuracaoAtual;
    }

    @Override
    @ObjectMapper(ConfiguracaoSistemaCrmVO.class)
    public T alterar(ConfiguracaoSistemaCrmDTO dto) {
        ConfiguracaosistemacrmEntity configuracaosistemacrmEntity = this.consultarConfiguracaoAtual();
        mapper.map(dto, configuracaosistemacrmEntity);
        this.repository.save(configuracaosistemacrmEntity);

        return (T) configuracaosistemacrmEntity;
    }


    @Override
    public List<FasesCRMEnum> consultarOrdenacaoMetas() {
        String ordenacaometas = this.consultarConfiguracaoAtual().getOrdenacaometas();
        List<FasesCRMEnum> fasesCRMEnumList = new ArrayList<>();
        if(ordenacaometas != null){
            String[] siglasFaseCrm = ordenacaometas.split(",");
            fasesCRMEnumList.addAll(Arrays.stream(siglasFaseCrm)
                    .map(sigla -> FasesCRMEnum.getFasePorSigla(sigla))
                    .collect(Collectors.toList()));
        }

        return fasesCRMEnumList;
    }


}
