package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ConfiguracaoGymbotDTO {
    @Schema(description = "", example = "true")
    private Boolean habilitarGymbot;
    @Schema(description = "Token Gymbot", example = "3D93CE54E2B640BCE11CC61BF7AC92D7")
    private String tokenGymbot;
    @Schema(description = "Descricao Instrucao IA", example = "Transfira para este departamento quando o usuário quiser saber sobre pagamentos.")
    private String descricaoDepartamento;
    @Schema(description = "id Departamento", example = "3D93CE54E2B640BCE11CC61BF7AC92D7")
    private String idDepartamento;

}
