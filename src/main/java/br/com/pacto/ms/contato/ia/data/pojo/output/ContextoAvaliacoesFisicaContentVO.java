package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class ContextoAvaliacoesFisicaContentVO {
    private int totalAvaliacoes;
    private int periodoDias;
    private int diasProximaAvaliacao;
    private int dataProxima;
    private double percentualMassaGorda;
    private double percentualMassaMagra;
    private double massaGordaInicial;
    private double massaGordaAtual;
    private double evolucaoGeral;
    private double massaMagraInicial;
    private double massaMagraAtual;
    private double nivelGorduraCorporal;
    private double nivelGorduraCorporalInicial;
    private double nivelGorduraCorporalFaltando;
    private List<GrupoVO> grupos;
    private List<PesoVO> pesos;
}
