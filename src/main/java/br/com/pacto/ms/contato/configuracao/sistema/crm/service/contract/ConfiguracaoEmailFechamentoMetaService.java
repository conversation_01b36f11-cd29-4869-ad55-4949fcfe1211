package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoEmailFechamentoMetaDTO;

import javax.validation.Valid;
import java.util.List;

public interface ConfiguracaoEmailFechamentoMetaService<T, DTO> {

    T consultar();

    T alterarTodos(@Valid List<ConfiguracaoEmailFechamentoMetaDTO> configuracaoEmailFechamentoMetaDTOS);

    T incluir(ConfiguracaoEmailFechamentoMetaDTO configuracaoEmailFechamentoMetaDTO);

    void excluir(Integer codigo);

    T alterar(Integer codigo, ConfiguracaoEmailFechamentoMetaDTO configuracaoEmailFechamentoMetaDTO);
}
