package br.com.pacto.ms.contato.avulso.data.pojo.input;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgendaLigacaoDTO {

    @Schema(description = "cliente", example = "3", required = true)
    private Integer cliente;

    @Schema(description = "Hora de agendamento", example = "15", required = true)
    private String hora;

    @Schema(description = "Minuto de agendamento", example = "30", required = true)
    private String minuto;

    @Schema(description = "Dia em que a ligacao sera feita", example = "2024-09-12T12:25:59.261Z", required = true)
    private Timestamp dia;

    @Schema(description = "Empresa", example = "1", required = true)
    private Integer empresa;

    @Schema(description = "Observacao", example = "Teste de agendamento feito pelo swagger", required = true)
    private String observacao;

    @Size(max = 50)
    @Schema(description = "Fase", example = "VA", required = true)
    private String fase;
}
