package br.com.pacto.ms.contato.ia.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ContextoFaseVO {

    @Schema(description = "Nome da fase")
    private String name;

    @Schema(description = "Código numérico da fase", example = "0")
    private Integer codigo;

    @Schema(description = "Descrição detalhada da fase")
    private String descricao;

    @Schema(description = "Descrição detalhada da fase")
    private String mensagensextras;

    @Schema(description = "Instrução fornecida para IA nessa fase")
    private String instrucao_ia;
}
