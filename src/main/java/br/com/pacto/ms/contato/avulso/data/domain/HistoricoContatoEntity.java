package br.com.pacto.ms.contato.avulso.data.domain;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.List;

import javax.persistence.*;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ChatMessageDTO;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import com.fasterxml.jackson.databind.node.ObjectNode;

import lombok.Data;
import lombok.NoArgsConstructor;

@DynamicUpdate
@DynamicInsert
@Data
@NoArgsConstructor
@Entity
@Table(name = "historicocontato", schema = "public")
public class HistoricoContatoEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "dia", nullable = true)
    private Timestamp dia;
    @Basic
    @Column(name = "cliente", nullable = true)
    private Integer cliente;
    @Basic
    @Column(name = "passivo", nullable = true)
    private Integer passivo;
    @Basic
    @Column(name = "indicado", nullable = true)
    private Integer indicado;
    @Basic
    @Column(name = "maladireta", nullable = true)
    private Integer maladireta;
    @Basic
    @Column(name = "observacao", nullable = true, length = -1)
    private String observacao;
    @Basic
    @Column(name = "tipooperacao", nullable = true, length = 2)
    private String tipooperacao;
    @Basic
    @Column(name = "responsavelcadastro", nullable = true)
    private Integer responsavelcadastro;
    @Basic
    @Column(name = "objecao", nullable = true)
    private Integer objecao;
    @Basic
    @Column(name = "agenda", nullable = true)
    private Integer agenda;
    @Basic
    @Column(name = "fase", nullable = true, length = 50)
    private String fase;
    @Basic
    @Column(name = "resultado", nullable = true, length = 255)
    private String resultado;
    @Basic
    @Column(name = "tipocontato", nullable = true, length = 2)
    private String tipocontato;
    @Basic
    @Column(name = "grausatisfacao", nullable = true, length = 2)
    private String grausatisfacao;
    @Basic
    @Column(name = "contatoavulso", nullable = true)
    private Boolean contatoavulso;
    @Basic
    @Column(name = "resposta", nullable = true, length = 120)
    private String resposta;
    @Basic
    @Column(name = "opcoes", nullable = true, length = 360)
    private String opcoes;
    @Basic
    @Column(name = "codigonotificacao", nullable = true)
    private Integer codigonotificacao;
    @Basic
    @Column(name = "conviteaulaexperimental", nullable = true)
    private Integer conviteaulaexperimental;
    @Basic
    @Column(name = "dataproximoenvio", nullable = true)
    private Date dataproximoenvio;
    @Basic
    @Column(name = "wagienvi", nullable = true)
    private Boolean wagienvi;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "cliente", referencedColumnName = "codigo", insertable = false, updatable = false)
    private ClienteEntity clienteByCliente;
    
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "objecao", referencedColumnName = "codigo", insertable = false, updatable = false)
    private ObjecaoEntity objecaoByObjecao;

    @Transient
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "agenda", referencedColumnName = "codigo", insertable = false, updatable = false)
    private AgendaEntity agendaByAgenda;

    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "responsavelcadastro", referencedColumnName = "codigo", insertable = false, updatable = false)
    private UsuarioEntity responsavelcadastroByUsuario;

    @Transient
    private ObjectNode tipoContatoCRM;

    @Transient
    private String diaPorExtenso;

    @Transient
    private String faseDescricao;

    @Transient
    private String observacaoApresentar;

    @Transient
    private List<ChatMessageDTO> observacaoApresentarWA;

    @Transient
    private String respostaApresentar;
}
