package br.com.pacto.ms.contato.configuracao.sistema.commons.data.domain;

import javax.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "configuracaosistema", schema = "public", catalog = "bdzillyonawakenbox")
public class ConfiguracaosistemaEntity {
    @Basic
    @Column(name = "nrdiasavencer", nullable = true)
    private Integer nrdiasavencer;
    @Basic
    @Column(name = "qtdfaltapeso1", nullable = true)
    private Integer qtdfaltapeso1;
    @Basic
    @Column(name = "qtdfaltainiciopeso2", nullable = true)
    private Integer qtdfaltainiciopeso2;
    @Basic
    @Column(name = "qtdfaltaterminopeso2", nullable = true)
    private Integer qtdfaltaterminopeso2;
    @Basic
    @Column(name = "qtdfaltapeso3", nullable = true)
    private Integer qtdfaltapeso3;
    @Basic
    @Column(name = "carenciarenovacao", nullable = true)
    private Integer carenciarenovacao;
    @Basic
    @Column(name = "mascaramatricula", nullable = true, length = -1)
    private String mascaramatricula;
    @Basic
    @Column(name = "multa", nullable = true, precision = 0)
    private Float multa;
    @Basic
    @Column(name = "juroparcela", nullable = true, precision = 0)
    private Float juroparcela;
    @Basic
    @Column(name = "questionariorematricula", nullable = false)
    private Integer questionariorematricula;
    @Basic
    @Column(name = "questionarioretorno", nullable = false)
    private Integer questionarioretorno;
    @Basic
    @Column(name = "questionarioprimeiravisita", nullable = false)
    private Integer questionarioprimeiravisita;
    @Basic
    @Column(name = "nrdiasvigentequestionariovisita", nullable = true)
    private Integer nrdiasvigentequestionariovisita;
    @Basic
    @Column(name = "nrdiasvigentequestionarioretorno", nullable = true)
    private Integer nrdiasvigentequestionarioretorno;
    @Basic
    @Column(name = "nrdiasvigentequestionariorematricula", nullable = true)
    private Integer nrdiasvigentequestionariorematricula;
    @Basic
    @Column(name = "toleranciapagamento", nullable = true)
    private Integer toleranciapagamento;
    @Basic
    @Column(name = "emailcontapagdigital", nullable = true, length = 60)
    private String emailcontapagdigital;
    @Basic
    @Column(name = "tokencontapagdigital", nullable = true, length = 60)
    private String tokencontapagdigital;
    @Basic
    @Column(name = "tokencontasms", nullable = true, length = 60)
    private String tokencontasms;
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Basic
    @Column(name = "carencia", nullable = false)
    private Integer carencia;
    @Basic
    @Column(name = "nrdiasprorata", nullable = true)
    private Integer nrdiasprorata;
    @Basic
    @Column(name = "cpfvalidar", nullable = true)
    private Boolean cpfvalidar;
    @Basic
    @Column(name = "dataexpiracao", nullable = true)
    private Date dataexpiracao;
    @Basic
    @Column(name = "rodarsqlsbancoinicial", nullable = true)
    private Boolean rodarsqlsbancoinicial;
    @Basic
    @Column(name = "urlgoogleagenda", nullable = true, length = 2048)
    private String urlgoogleagenda;
    @Basic
    @Column(name = "diasparabloqueio", nullable = true)
    private Integer diasparabloqueio;
    @Basic
    @Column(name = "vencimentocolaborador", nullable = true)
    private Short vencimentocolaborador;
    @Basic
    @Column(name = "usaecf", nullable = true)
    private Boolean usaecf;
    @Basic
    @Column(name = "aliquotaservico", nullable = true)
    private Integer aliquotaservico;
    @Basic
    @Column(name = "aliquotaproduto", nullable = true)
    private Integer aliquotaproduto;
    @Basic
    @Column(name = "alteracaodatabasecontrato", nullable = true)
    private Boolean alteracaodatabasecontrato;
    @Basic
    @Column(name = "toleranciadiascontratovencido", nullable = true)
    private Integer toleranciadiascontratovencido;
    @Basic
    @Column(name = "urlrecorrencia", nullable = true, length = -1)
    private String urlrecorrencia;
    @Basic
    @Column(name = "qtddiasexpirarsenha", nullable = true)
    private Integer qtddiasexpirarsenha;
    @Basic
    @Column(name = "qtddiasestornoautomaticocontrato", nullable = true)
    private Integer qtddiasestornoautomaticocontrato;
    @Basic
    @Column(name = "acessochamada", nullable = true)
    private Boolean acessochamada;
    @Basic
    @Column(name = "localacessochamada", nullable = true)
    private Integer localacessochamada;
    @Basic
    @Column(name = "coletorchamada", nullable = true)
    private Integer coletorchamada;
    @Basic
    @Column(name = "dataultimarepescagem", nullable = true)
    private Timestamp dataultimarepescagem;
    @Basic
    @Column(name = "emailsfechamentoacessos", nullable = true, length = 1000)
    private String emailsfechamentoacessos;
    @Basic
    @Column(name = "bloquearacessoseparcelaaberta", nullable = true)
    private Boolean bloquearacessoseparcelaaberta;
    @Basic
    @Column(name = "enviarsmsautomatico", nullable = true)
    private Boolean enviarsmsautomatico;
    @Basic
    @Column(name = "nomedatanascvalidar", nullable = true)
    private Boolean nomedatanascvalidar;
    @Basic
    @Column(name = "enviarremessasremotamente", nullable = true)
    private Boolean enviarremessasremotamente;
    @Basic
    @Column(name = "validarcontatometa", nullable = true)
    private Boolean validarcontatometa;
    @Basic
    @Column(name = "forcarcodigoalternativoacesso", nullable = true)
    private Boolean forcarcodigoalternativoacesso;
    @Basic
    @Column(name = "ecfporpagamento", nullable = true)
    private Boolean ecfporpagamento;
    @Basic
    @Column(name = "itemvendaavulsaautomatico", nullable = true)
    private Boolean itemvendaavulsaautomatico;
    @Basic
    @Column(name = "questionarioprimeiracompra", nullable = true)
    private Integer questionarioprimeiracompra;
    @Basic
    @Column(name = "nrdiasvigentequestionarioprimeiracompra", nullable = true)
    private Integer nrdiasvigentequestionarioprimeiracompra;
    @Basic
    @Column(name = "questionarioretornocompra", nullable = true)
    private Integer questionarioretornocompra;
    @Basic
    @Column(name = "nrdiasvigentequestionarioretornocompra", nullable = true)
    private Integer nrdiasvigentequestionarioretornocompra;
    @Basic
    @Column(name = "numerocielo", nullable = true, length = 20)
    private String numerocielo;
    @Basic
    @Column(name = "validarcpfduplicado", nullable = true)
    private Boolean validarcpfduplicado;
    @Basic
    @Column(name = "marcarpresencapeloacesso", nullable = true)
    private Boolean marcarpresencapeloacesso;
    @Basic
    @Column(name = "usarnomeresponsavelnota", nullable = true)
    private Boolean usarnomeresponsavelnota;
    @Basic
    @Column(name = "qtddiaprimeiraparcelavencidaestornarcontrato", nullable = true)
    private Integer qtddiaprimeiraparcelavencidaestornarcontrato;
    @Basic
    @Column(name = "utilizarsistemaparaclube", nullable = true)
    private Boolean utilizarsistemaparaclube;
    @Basic
    @Column(name = "imprimirrecibopagtomatricial", nullable = true)
    private Boolean imprimirrecibopagtomatricial;
    @Basic
    @Column(name = "defaultenderecocorrespondecia", nullable = true)
    private Boolean defaultenderecocorrespondecia;
    @Basic
    @Column(name = "ecfapenasplano", nullable = true)
    private Boolean ecfapenasplano;
    @Basic
    @Column(name = "habilitargestaoarmarios", nullable = true)
    private Boolean habilitargestaoarmarios;
    @Basic
    @Column(name = "diaprorataarmario", nullable = true)
    private Integer diaprorataarmario;
    @Basic
    @Column(name = "nomenclaturavendacredito", nullable = false, length = 2)
    private String nomenclaturavendacredito;
    @Basic
    @Column(name = "sequencialitem", nullable = true)
    private Integer sequencialitem;
    @Basic
    @Column(name = "sesc", nullable = true)
    private Boolean sesc;
    @Basic
    @Column(name = "controleacessomultiplasempresasporplano", nullable = true)
    private Boolean controleacessomultiplasempresasporplano;
    @Basic
    @Column(name = "priorizarvendarapida", nullable = true)
    private Boolean priorizarvendarapida;
    @Basic
    @Column(name = "barrardevedorvendarapida", nullable = true)
    private Boolean barrardevedorvendarapida;
    @Basic
    @Column(name = "usaaprovafacil", nullable = true)
    private Boolean usaaprovafacil;
    @Basic
    @Column(name = "cancelarcontratonaunidadeorigemaotransferiraluno", nullable = true)
    private Boolean cancelarcontratonaunidadeorigemaotransferiraluno;
    @Basic
    @Column(name = "sequencialarquivo", nullable = true)
    private Integer sequencialarquivo;
    @Basic
    @Column(name = "usarsistemainternacional", nullable = true)
    private Boolean usarsistemainternacional;
    @Basic
    @Column(name = "seqnotafiscalfamilia", nullable = true)
    private Integer seqnotafiscalfamilia;
    @Basic
    @Column(name = "utilizartipoplano", nullable = true)
    private Boolean utilizartipoplano;
    @Basic
    @Column(name = "justfit", nullable = true)
    private Boolean justfit;
    @Basic
    @Column(name = "usardigitalcomoassinatura", nullable = true)
    private Boolean usardigitalcomoassinatura;
    @Basic
    @Column(name = "validarcpfresponsaveis", nullable = true)
    private Boolean validarcpfresponsaveis;
    @Basic
    @Column(name = "nomearquivoremessapadraotivit", nullable = true)
    private Boolean nomearquivoremessapadraotivit;
    @Basic
    @Column(name = "definirdatainicioplanosrecorrencia", nullable = true)
    private Boolean definirdatainicioplanosrecorrencia;
    @Basic
    @Column(name = "transferirautorizacaocobranca", nullable = true)
    private Boolean transferirautorizacaocobranca;
    @Basic
    @Column(name = "habilitarcanalcliente", nullable = true)
    private Boolean habilitarcanalcliente;
    @Basic
    @Column(name = "versaocanalcliente", nullable = true)
    private Integer versaocanalcliente;
    @Basic
    @Column(name = "lancamentocontratosiguais", nullable = true)
    private Boolean lancamentocontratosiguais;
    @Basic
    @Column(name = "exibirmodalusuariosinativos", nullable = true)
    private Boolean exibirmodalusuariosinativos;
    @Basic
    @Column(name = "seqprocessoimportacao", nullable = true)
    private Integer seqprocessoimportacao;
    @Basic
    @Column(name = "usarverificadorremessasrejeitadas", nullable = true)
    private Boolean usarverificadorremessasrejeitadas;
    @Basic
    @Column(name = "agruparremessasgetnet", nullable = true)
    private Boolean agruparremessasgetnet;
    @Basic
    @Column(name = "datasincronizacaooamd", nullable = true)
    private Timestamp datasincronizacaooamd;
    @Basic
    @Column(name = "datainiciodesconsideraracessorisco", nullable = true)
    private Timestamp datainiciodesconsideraracessorisco;
    @Basic
    @Column(name = "datafimdesconsideraracessorisco", nullable = true)
    private Timestamp datafimdesconsideraracessorisco;
    @Basic
    @Column(name = "apresentarmarketplace", nullable = true)
    private Boolean apresentarmarketplace;
    @Basic
    @Column(name = "agruparremessascartaoedi", nullable = true)
    private Boolean agruparremessascartaoedi;
    @Basic
    @Column(name = "chavepublicasesc", nullable = true, length = 40)
    private String chavepublicasesc;
    @Basic
    @Column(name = "chaveprivadasesc", nullable = true, length = 40)
    private String chaveprivadasesc;
    @Basic
    @Column(name = "ativarverificarcartao", nullable = true)
    private Boolean ativarverificarcartao;
    @Basic
    @Column(name = "permitirreplicarplanoredeempresa", nullable = true)
    private Boolean permitirreplicarplanoredeempresa;
    @Basic
    @Column(name = "propagaraassinaturadigital", nullable = true)
    private Boolean propagaraassinaturadigital;
    @Basic
    @Column(name = "forcarutilizacaoplanoantigo", nullable = true)
    private Boolean forcarutilizacaoplanoantigo;
    @Basic
    @Column(name = "mascaratelefone", nullable = true, length = 255)
    private String mascaratelefone;
    @Basic
    @Column(name = "utilizarformatommddyyydtnascimento", nullable = true)
    private Boolean utilizarformatommddyyydtnascimento;
    @Basic
    @Column(name = "utilizarservicosesisc", nullable = true)
    private Boolean utilizarservicosesisc;

    public Integer getNrdiasavencer() {
        return nrdiasavencer;
    }

    public void setNrdiasavencer(Integer nrdiasavencer) {
        this.nrdiasavencer = nrdiasavencer;
    }

    public Integer getQtdfaltapeso1() {
        return qtdfaltapeso1;
    }

    public void setQtdfaltapeso1(Integer qtdfaltapeso1) {
        this.qtdfaltapeso1 = qtdfaltapeso1;
    }

    public Integer getQtdfaltainiciopeso2() {
        return qtdfaltainiciopeso2;
    }

    public void setQtdfaltainiciopeso2(Integer qtdfaltainiciopeso2) {
        this.qtdfaltainiciopeso2 = qtdfaltainiciopeso2;
    }

    public Integer getQtdfaltaterminopeso2() {
        return qtdfaltaterminopeso2;
    }

    public void setQtdfaltaterminopeso2(Integer qtdfaltaterminopeso2) {
        this.qtdfaltaterminopeso2 = qtdfaltaterminopeso2;
    }

    public Integer getQtdfaltapeso3() {
        return qtdfaltapeso3;
    }

    public void setQtdfaltapeso3(Integer qtdfaltapeso3) {
        this.qtdfaltapeso3 = qtdfaltapeso3;
    }

    public Integer getCarenciarenovacao() {
        return carenciarenovacao;
    }

    public void setCarenciarenovacao(Integer carenciarenovacao) {
        this.carenciarenovacao = carenciarenovacao;
    }

    public String getMascaramatricula() {
        return mascaramatricula;
    }

    public void setMascaramatricula(String mascaramatricula) {
        this.mascaramatricula = mascaramatricula;
    }

    public Float getMulta() {
        return multa;
    }

    public void setMulta(Float multa) {
        this.multa = multa;
    }

    public Float getJuroparcela() {
        return juroparcela;
    }

    public void setJuroparcela(Float juroparcela) {
        this.juroparcela = juroparcela;
    }

    public Integer getQuestionariorematricula() {
        return questionariorematricula;
    }

    public void setQuestionariorematricula(Integer questionariorematricula) {
        this.questionariorematricula = questionariorematricula;
    }

    public Integer getQuestionarioretorno() {
        return questionarioretorno;
    }

    public void setQuestionarioretorno(Integer questionarioretorno) {
        this.questionarioretorno = questionarioretorno;
    }

    public Integer getQuestionarioprimeiravisita() {
        return questionarioprimeiravisita;
    }

    public void setQuestionarioprimeiravisita(Integer questionarioprimeiravisita) {
        this.questionarioprimeiravisita = questionarioprimeiravisita;
    }

    public Integer getNrdiasvigentequestionariovisita() {
        return nrdiasvigentequestionariovisita;
    }

    public void setNrdiasvigentequestionariovisita(Integer nrdiasvigentequestionariovisita) {
        this.nrdiasvigentequestionariovisita = nrdiasvigentequestionariovisita;
    }

    public Integer getNrdiasvigentequestionarioretorno() {
        return nrdiasvigentequestionarioretorno;
    }

    public void setNrdiasvigentequestionarioretorno(Integer nrdiasvigentequestionarioretorno) {
        this.nrdiasvigentequestionarioretorno = nrdiasvigentequestionarioretorno;
    }

    public Integer getNrdiasvigentequestionariorematricula() {
        return nrdiasvigentequestionariorematricula;
    }

    public void setNrdiasvigentequestionariorematricula(Integer nrdiasvigentequestionariorematricula) {
        this.nrdiasvigentequestionariorematricula = nrdiasvigentequestionariorematricula;
    }

    public Integer getToleranciapagamento() {
        return toleranciapagamento;
    }

    public void setToleranciapagamento(Integer toleranciapagamento) {
        this.toleranciapagamento = toleranciapagamento;
    }

    public String getEmailcontapagdigital() {
        return emailcontapagdigital;
    }

    public void setEmailcontapagdigital(String emailcontapagdigital) {
        this.emailcontapagdigital = emailcontapagdigital;
    }

    public String getTokencontapagdigital() {
        return tokencontapagdigital;
    }

    public void setTokencontapagdigital(String tokencontapagdigital) {
        this.tokencontapagdigital = tokencontapagdigital;
    }

    public String getTokencontasms() {
        return tokencontasms;
    }

    public void setTokencontasms(String tokencontasms) {
        this.tokencontasms = tokencontasms;
    }

    public Integer getCodigo() {
        return codigo;
    }

    public void setCodigo(Integer codigo) {
        this.codigo = codigo;
    }

    public Integer getCarencia() {
        return carencia;
    }

    public void setCarencia(Integer carencia) {
        this.carencia = carencia;
    }

    public Integer getNrdiasprorata() {
        return nrdiasprorata;
    }

    public void setNrdiasprorata(Integer nrdiasprorata) {
        this.nrdiasprorata = nrdiasprorata;
    }

    public Boolean getCpfvalidar() {
        return cpfvalidar;
    }

    public void setCpfvalidar(Boolean cpfvalidar) {
        this.cpfvalidar = cpfvalidar;
    }

    public Date getDataexpiracao() {
        return dataexpiracao;
    }

    public void setDataexpiracao(Date dataexpiracao) {
        this.dataexpiracao = dataexpiracao;
    }

    public Boolean getRodarsqlsbancoinicial() {
        return rodarsqlsbancoinicial;
    }

    public void setRodarsqlsbancoinicial(Boolean rodarsqlsbancoinicial) {
        this.rodarsqlsbancoinicial = rodarsqlsbancoinicial;
    }

    public String getUrlgoogleagenda() {
        return urlgoogleagenda;
    }

    public void setUrlgoogleagenda(String urlgoogleagenda) {
        this.urlgoogleagenda = urlgoogleagenda;
    }

    public Integer getDiasparabloqueio() {
        return diasparabloqueio;
    }

    public void setDiasparabloqueio(Integer diasparabloqueio) {
        this.diasparabloqueio = diasparabloqueio;
    }

    public Short getVencimentocolaborador() {
        return vencimentocolaborador;
    }

    public void setVencimentocolaborador(Short vencimentocolaborador) {
        this.vencimentocolaborador = vencimentocolaborador;
    }

    public Boolean getUsaecf() {
        return usaecf;
    }

    public void setUsaecf(Boolean usaecf) {
        this.usaecf = usaecf;
    }

    public Integer getAliquotaservico() {
        return aliquotaservico;
    }

    public void setAliquotaservico(Integer aliquotaservico) {
        this.aliquotaservico = aliquotaservico;
    }

    public Integer getAliquotaproduto() {
        return aliquotaproduto;
    }

    public void setAliquotaproduto(Integer aliquotaproduto) {
        this.aliquotaproduto = aliquotaproduto;
    }

    public Boolean getAlteracaodatabasecontrato() {
        return alteracaodatabasecontrato;
    }

    public void setAlteracaodatabasecontrato(Boolean alteracaodatabasecontrato) {
        this.alteracaodatabasecontrato = alteracaodatabasecontrato;
    }

    public Integer getToleranciadiascontratovencido() {
        return toleranciadiascontratovencido;
    }

    public void setToleranciadiascontratovencido(Integer toleranciadiascontratovencido) {
        this.toleranciadiascontratovencido = toleranciadiascontratovencido;
    }

    public String getUrlrecorrencia() {
        return urlrecorrencia;
    }

    public void setUrlrecorrencia(String urlrecorrencia) {
        this.urlrecorrencia = urlrecorrencia;
    }

    public Integer getQtddiasexpirarsenha() {
        return qtddiasexpirarsenha;
    }

    public void setQtddiasexpirarsenha(Integer qtddiasexpirarsenha) {
        this.qtddiasexpirarsenha = qtddiasexpirarsenha;
    }

    public Integer getQtddiasestornoautomaticocontrato() {
        return qtddiasestornoautomaticocontrato;
    }

    public void setQtddiasestornoautomaticocontrato(Integer qtddiasestornoautomaticocontrato) {
        this.qtddiasestornoautomaticocontrato = qtddiasestornoautomaticocontrato;
    }

    public Boolean getAcessochamada() {
        return acessochamada;
    }

    public void setAcessochamada(Boolean acessochamada) {
        this.acessochamada = acessochamada;
    }

    public Integer getLocalacessochamada() {
        return localacessochamada;
    }

    public void setLocalacessochamada(Integer localacessochamada) {
        this.localacessochamada = localacessochamada;
    }

    public Integer getColetorchamada() {
        return coletorchamada;
    }

    public void setColetorchamada(Integer coletorchamada) {
        this.coletorchamada = coletorchamada;
    }

    public Timestamp getDataultimarepescagem() {
        return dataultimarepescagem;
    }

    public void setDataultimarepescagem(Timestamp dataultimarepescagem) {
        this.dataultimarepescagem = dataultimarepescagem;
    }

    public String getEmailsfechamentoacessos() {
        return emailsfechamentoacessos;
    }

    public void setEmailsfechamentoacessos(String emailsfechamentoacessos) {
        this.emailsfechamentoacessos = emailsfechamentoacessos;
    }

    public Boolean getBloquearacessoseparcelaaberta() {
        return bloquearacessoseparcelaaberta;
    }

    public void setBloquearacessoseparcelaaberta(Boolean bloquearacessoseparcelaaberta) {
        this.bloquearacessoseparcelaaberta = bloquearacessoseparcelaaberta;
    }

    public Boolean getEnviarsmsautomatico() {
        return enviarsmsautomatico;
    }

    public void setEnviarsmsautomatico(Boolean enviarsmsautomatico) {
        this.enviarsmsautomatico = enviarsmsautomatico;
    }

    public Boolean getNomedatanascvalidar() {
        return nomedatanascvalidar;
    }

    public void setNomedatanascvalidar(Boolean nomedatanascvalidar) {
        this.nomedatanascvalidar = nomedatanascvalidar;
    }

    public Boolean getEnviarremessasremotamente() {
        return enviarremessasremotamente;
    }

    public void setEnviarremessasremotamente(Boolean enviarremessasremotamente) {
        this.enviarremessasremotamente = enviarremessasremotamente;
    }

    public Boolean getValidarcontatometa() {
        return validarcontatometa;
    }

    public void setValidarcontatometa(Boolean validarcontatometa) {
        this.validarcontatometa = validarcontatometa;
    }

    public Boolean getForcarcodigoalternativoacesso() {
        return forcarcodigoalternativoacesso;
    }

    public void setForcarcodigoalternativoacesso(Boolean forcarcodigoalternativoacesso) {
        this.forcarcodigoalternativoacesso = forcarcodigoalternativoacesso;
    }

    public Boolean getEcfporpagamento() {
        return ecfporpagamento;
    }

    public void setEcfporpagamento(Boolean ecfporpagamento) {
        this.ecfporpagamento = ecfporpagamento;
    }

    public Boolean getItemvendaavulsaautomatico() {
        return itemvendaavulsaautomatico;
    }

    public void setItemvendaavulsaautomatico(Boolean itemvendaavulsaautomatico) {
        this.itemvendaavulsaautomatico = itemvendaavulsaautomatico;
    }

    public Integer getQuestionarioprimeiracompra() {
        return questionarioprimeiracompra;
    }

    public void setQuestionarioprimeiracompra(Integer questionarioprimeiracompra) {
        this.questionarioprimeiracompra = questionarioprimeiracompra;
    }

    public Integer getNrdiasvigentequestionarioprimeiracompra() {
        return nrdiasvigentequestionarioprimeiracompra;
    }

    public void setNrdiasvigentequestionarioprimeiracompra(Integer nrdiasvigentequestionarioprimeiracompra) {
        this.nrdiasvigentequestionarioprimeiracompra = nrdiasvigentequestionarioprimeiracompra;
    }

    public Integer getQuestionarioretornocompra() {
        return questionarioretornocompra;
    }

    public void setQuestionarioretornocompra(Integer questionarioretornocompra) {
        this.questionarioretornocompra = questionarioretornocompra;
    }

    public Integer getNrdiasvigentequestionarioretornocompra() {
        return nrdiasvigentequestionarioretornocompra;
    }

    public void setNrdiasvigentequestionarioretornocompra(Integer nrdiasvigentequestionarioretornocompra) {
        this.nrdiasvigentequestionarioretornocompra = nrdiasvigentequestionarioretornocompra;
    }

    public String getNumerocielo() {
        return numerocielo;
    }

    public void setNumerocielo(String numerocielo) {
        this.numerocielo = numerocielo;
    }

    public Boolean getValidarcpfduplicado() {
        return validarcpfduplicado;
    }

    public void setValidarcpfduplicado(Boolean validarcpfduplicado) {
        this.validarcpfduplicado = validarcpfduplicado;
    }

    public Boolean getMarcarpresencapeloacesso() {
        return marcarpresencapeloacesso;
    }

    public void setMarcarpresencapeloacesso(Boolean marcarpresencapeloacesso) {
        this.marcarpresencapeloacesso = marcarpresencapeloacesso;
    }

    public Boolean getUsarnomeresponsavelnota() {
        return usarnomeresponsavelnota;
    }

    public void setUsarnomeresponsavelnota(Boolean usarnomeresponsavelnota) {
        this.usarnomeresponsavelnota = usarnomeresponsavelnota;
    }

    public Integer getQtddiaprimeiraparcelavencidaestornarcontrato() {
        return qtddiaprimeiraparcelavencidaestornarcontrato;
    }

    public void setQtddiaprimeiraparcelavencidaestornarcontrato(Integer qtddiaprimeiraparcelavencidaestornarcontrato) {
        this.qtddiaprimeiraparcelavencidaestornarcontrato = qtddiaprimeiraparcelavencidaestornarcontrato;
    }

    public Boolean getUtilizarsistemaparaclube() {
        return utilizarsistemaparaclube;
    }

    public void setUtilizarsistemaparaclube(Boolean utilizarsistemaparaclube) {
        this.utilizarsistemaparaclube = utilizarsistemaparaclube;
    }

    public Boolean getImprimirrecibopagtomatricial() {
        return imprimirrecibopagtomatricial;
    }

    public void setImprimirrecibopagtomatricial(Boolean imprimirrecibopagtomatricial) {
        this.imprimirrecibopagtomatricial = imprimirrecibopagtomatricial;
    }

    public Boolean getDefaultenderecocorrespondecia() {
        return defaultenderecocorrespondecia;
    }

    public void setDefaultenderecocorrespondecia(Boolean defaultenderecocorrespondecia) {
        this.defaultenderecocorrespondecia = defaultenderecocorrespondecia;
    }

    public Boolean getEcfapenasplano() {
        return ecfapenasplano;
    }

    public void setEcfapenasplano(Boolean ecfapenasplano) {
        this.ecfapenasplano = ecfapenasplano;
    }

    public Boolean getHabilitargestaoarmarios() {
        return habilitargestaoarmarios;
    }

    public void setHabilitargestaoarmarios(Boolean habilitargestaoarmarios) {
        this.habilitargestaoarmarios = habilitargestaoarmarios;
    }

    public Integer getDiaprorataarmario() {
        return diaprorataarmario;
    }

    public void setDiaprorataarmario(Integer diaprorataarmario) {
        this.diaprorataarmario = diaprorataarmario;
    }

    public String getNomenclaturavendacredito() {
        return nomenclaturavendacredito;
    }

    public void setNomenclaturavendacredito(String nomenclaturavendacredito) {
        this.nomenclaturavendacredito = nomenclaturavendacredito;
    }

    public Integer getSequencialitem() {
        return sequencialitem;
    }

    public void setSequencialitem(Integer sequencialitem) {
        this.sequencialitem = sequencialitem;
    }

    public Boolean getSesc() {
        return sesc;
    }

    public void setSesc(Boolean sesc) {
        this.sesc = sesc;
    }

    public Boolean getControleacessomultiplasempresasporplano() {
        return controleacessomultiplasempresasporplano;
    }

    public void setControleacessomultiplasempresasporplano(Boolean controleacessomultiplasempresasporplano) {
        this.controleacessomultiplasempresasporplano = controleacessomultiplasempresasporplano;
    }

    public Boolean getPriorizarvendarapida() {
        return priorizarvendarapida;
    }

    public void setPriorizarvendarapida(Boolean priorizarvendarapida) {
        this.priorizarvendarapida = priorizarvendarapida;
    }

    public Boolean getBarrardevedorvendarapida() {
        return barrardevedorvendarapida;
    }

    public void setBarrardevedorvendarapida(Boolean barrardevedorvendarapida) {
        this.barrardevedorvendarapida = barrardevedorvendarapida;
    }

    public Boolean getUsaaprovafacil() {
        return usaaprovafacil;
    }

    public void setUsaaprovafacil(Boolean usaaprovafacil) {
        this.usaaprovafacil = usaaprovafacil;
    }

    public Boolean getCancelarcontratonaunidadeorigemaotransferiraluno() {
        return cancelarcontratonaunidadeorigemaotransferiraluno;
    }

    public void setCancelarcontratonaunidadeorigemaotransferiraluno(Boolean cancelarcontratonaunidadeorigemaotransferiraluno) {
        this.cancelarcontratonaunidadeorigemaotransferiraluno = cancelarcontratonaunidadeorigemaotransferiraluno;
    }

    public Integer getSequencialarquivo() {
        return sequencialarquivo;
    }

    public void setSequencialarquivo(Integer sequencialarquivo) {
        this.sequencialarquivo = sequencialarquivo;
    }

    public Boolean getUsarsistemainternacional() {
        return usarsistemainternacional;
    }

    public void setUsarsistemainternacional(Boolean usarsistemainternacional) {
        this.usarsistemainternacional = usarsistemainternacional;
    }

    public Integer getSeqnotafiscalfamilia() {
        return seqnotafiscalfamilia;
    }

    public void setSeqnotafiscalfamilia(Integer seqnotafiscalfamilia) {
        this.seqnotafiscalfamilia = seqnotafiscalfamilia;
    }

    public Boolean getUtilizartipoplano() {
        return utilizartipoplano;
    }

    public void setUtilizartipoplano(Boolean utilizartipoplano) {
        this.utilizartipoplano = utilizartipoplano;
    }

    public Boolean getJustfit() {
        return justfit;
    }

    public void setJustfit(Boolean justfit) {
        this.justfit = justfit;
    }

    public Boolean getUsardigitalcomoassinatura() {
        return usardigitalcomoassinatura;
    }

    public void setUsardigitalcomoassinatura(Boolean usardigitalcomoassinatura) {
        this.usardigitalcomoassinatura = usardigitalcomoassinatura;
    }

    public Boolean getValidarcpfresponsaveis() {
        return validarcpfresponsaveis;
    }

    public void setValidarcpfresponsaveis(Boolean validarcpfresponsaveis) {
        this.validarcpfresponsaveis = validarcpfresponsaveis;
    }

    public Boolean getNomearquivoremessapadraotivit() {
        return nomearquivoremessapadraotivit;
    }

    public void setNomearquivoremessapadraotivit(Boolean nomearquivoremessapadraotivit) {
        this.nomearquivoremessapadraotivit = nomearquivoremessapadraotivit;
    }

    public Boolean getDefinirdatainicioplanosrecorrencia() {
        return definirdatainicioplanosrecorrencia;
    }

    public void setDefinirdatainicioplanosrecorrencia(Boolean definirdatainicioplanosrecorrencia) {
        this.definirdatainicioplanosrecorrencia = definirdatainicioplanosrecorrencia;
    }

    public Boolean getTransferirautorizacaocobranca() {
        return transferirautorizacaocobranca;
    }

    public void setTransferirautorizacaocobranca(Boolean transferirautorizacaocobranca) {
        this.transferirautorizacaocobranca = transferirautorizacaocobranca;
    }

    public Boolean getHabilitarcanalcliente() {
        return habilitarcanalcliente;
    }

    public void setHabilitarcanalcliente(Boolean habilitarcanalcliente) {
        this.habilitarcanalcliente = habilitarcanalcliente;
    }

    public Integer getVersaocanalcliente() {
        return versaocanalcliente;
    }

    public void setVersaocanalcliente(Integer versaocanalcliente) {
        this.versaocanalcliente = versaocanalcliente;
    }

    public Boolean getLancamentocontratosiguais() {
        return lancamentocontratosiguais;
    }

    public void setLancamentocontratosiguais(Boolean lancamentocontratosiguais) {
        this.lancamentocontratosiguais = lancamentocontratosiguais;
    }

    public Boolean getExibirmodalusuariosinativos() {
        return exibirmodalusuariosinativos;
    }

    public void setExibirmodalusuariosinativos(Boolean exibirmodalusuariosinativos) {
        this.exibirmodalusuariosinativos = exibirmodalusuariosinativos;
    }

    public Integer getSeqprocessoimportacao() {
        return seqprocessoimportacao;
    }

    public void setSeqprocessoimportacao(Integer seqprocessoimportacao) {
        this.seqprocessoimportacao = seqprocessoimportacao;
    }

    public Boolean getUsarverificadorremessasrejeitadas() {
        return usarverificadorremessasrejeitadas;
    }

    public void setUsarverificadorremessasrejeitadas(Boolean usarverificadorremessasrejeitadas) {
        this.usarverificadorremessasrejeitadas = usarverificadorremessasrejeitadas;
    }

    public Boolean getAgruparremessasgetnet() {
        return agruparremessasgetnet;
    }

    public void setAgruparremessasgetnet(Boolean agruparremessasgetnet) {
        this.agruparremessasgetnet = agruparremessasgetnet;
    }

    public Timestamp getDatasincronizacaooamd() {
        return datasincronizacaooamd;
    }

    public void setDatasincronizacaooamd(Timestamp datasincronizacaooamd) {
        this.datasincronizacaooamd = datasincronizacaooamd;
    }

    public Timestamp getDatainiciodesconsideraracessorisco() {
        return datainiciodesconsideraracessorisco;
    }

    public void setDatainiciodesconsideraracessorisco(Timestamp datainiciodesconsideraracessorisco) {
        this.datainiciodesconsideraracessorisco = datainiciodesconsideraracessorisco;
    }

    public Timestamp getDatafimdesconsideraracessorisco() {
        return datafimdesconsideraracessorisco;
    }

    public void setDatafimdesconsideraracessorisco(Timestamp datafimdesconsideraracessorisco) {
        this.datafimdesconsideraracessorisco = datafimdesconsideraracessorisco;
    }

    public Boolean getApresentarmarketplace() {
        return apresentarmarketplace;
    }

    public void setApresentarmarketplace(Boolean apresentarmarketplace) {
        this.apresentarmarketplace = apresentarmarketplace;
    }

    public Boolean getAgruparremessascartaoedi() {
        return agruparremessascartaoedi;
    }

    public void setAgruparremessascartaoedi(Boolean agruparremessascartaoedi) {
        this.agruparremessascartaoedi = agruparremessascartaoedi;
    }

    public String getChavepublicasesc() {
        return chavepublicasesc;
    }

    public void setChavepublicasesc(String chavepublicasesc) {
        this.chavepublicasesc = chavepublicasesc;
    }

    public String getChaveprivadasesc() {
        return chaveprivadasesc;
    }

    public void setChaveprivadasesc(String chaveprivadasesc) {
        this.chaveprivadasesc = chaveprivadasesc;
    }

    public Boolean getAtivarverificarcartao() {
        return ativarverificarcartao;
    }

    public void setAtivarverificarcartao(Boolean ativarverificarcartao) {
        this.ativarverificarcartao = ativarverificarcartao;
    }

    public Boolean getPermitirreplicarplanoredeempresa() {
        return permitirreplicarplanoredeempresa;
    }

    public void setPermitirreplicarplanoredeempresa(Boolean permitirreplicarplanoredeempresa) {
        this.permitirreplicarplanoredeempresa = permitirreplicarplanoredeempresa;
    }

    public Boolean getPropagaraassinaturadigital() {
        return propagaraassinaturadigital;
    }

    public void setPropagaraassinaturadigital(Boolean propagaraassinaturadigital) {
        this.propagaraassinaturadigital = propagaraassinaturadigital;
    }

    public Boolean getForcarutilizacaoplanoantigo() {
        return forcarutilizacaoplanoantigo;
    }

    public void setForcarutilizacaoplanoantigo(Boolean forcarutilizacaoplanoantigo) {
        this.forcarutilizacaoplanoantigo = forcarutilizacaoplanoantigo;
    }

    public String getMascaratelefone() {
        return mascaratelefone;
    }

    public void setMascaratelefone(String mascaratelefone) {
        this.mascaratelefone = mascaratelefone;
    }

    public Boolean getUtilizarformatommddyyydtnascimento() {
        return utilizarformatommddyyydtnascimento;
    }

    public void setUtilizarformatommddyyydtnascimento(Boolean utilizarformatommddyyydtnascimento) {
        this.utilizarformatommddyyydtnascimento = utilizarformatommddyyydtnascimento;
    }

    public Boolean getUtilizarservicosesisc() {
        return utilizarservicosesisc;
    }

    public void setUtilizarservicosesisc(Boolean utilizarservicosesisc) {
        this.utilizarservicosesisc = utilizarservicosesisc;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConfiguracaosistemaEntity that = (ConfiguracaosistemaEntity) o;
        return Objects.equals(nrdiasavencer, that.nrdiasavencer) && Objects.equals(qtdfaltapeso1, that.qtdfaltapeso1) && Objects.equals(qtdfaltainiciopeso2, that.qtdfaltainiciopeso2) && Objects.equals(qtdfaltaterminopeso2, that.qtdfaltaterminopeso2) && Objects.equals(qtdfaltapeso3, that.qtdfaltapeso3) && Objects.equals(carenciarenovacao, that.carenciarenovacao) && Objects.equals(mascaramatricula, that.mascaramatricula) && Objects.equals(multa, that.multa) && Objects.equals(juroparcela, that.juroparcela) && Objects.equals(questionariorematricula, that.questionariorematricula) && Objects.equals(questionarioretorno, that.questionarioretorno) && Objects.equals(questionarioprimeiravisita, that.questionarioprimeiravisita) && Objects.equals(nrdiasvigentequestionariovisita, that.nrdiasvigentequestionariovisita) && Objects.equals(nrdiasvigentequestionarioretorno, that.nrdiasvigentequestionarioretorno) && Objects.equals(nrdiasvigentequestionariorematricula, that.nrdiasvigentequestionariorematricula) && Objects.equals(toleranciapagamento, that.toleranciapagamento) && Objects.equals(emailcontapagdigital, that.emailcontapagdigital) && Objects.equals(tokencontapagdigital, that.tokencontapagdigital) && Objects.equals(tokencontasms, that.tokencontasms) && Objects.equals(codigo, that.codigo) && Objects.equals(carencia, that.carencia) && Objects.equals(nrdiasprorata, that.nrdiasprorata) && Objects.equals(cpfvalidar, that.cpfvalidar) && Objects.equals(dataexpiracao, that.dataexpiracao) && Objects.equals(rodarsqlsbancoinicial, that.rodarsqlsbancoinicial) && Objects.equals(urlgoogleagenda, that.urlgoogleagenda) && Objects.equals(diasparabloqueio, that.diasparabloqueio) && Objects.equals(vencimentocolaborador, that.vencimentocolaborador) && Objects.equals(usaecf, that.usaecf) && Objects.equals(aliquotaservico, that.aliquotaservico) && Objects.equals(aliquotaproduto, that.aliquotaproduto) && Objects.equals(alteracaodatabasecontrato, that.alteracaodatabasecontrato) && Objects.equals(toleranciadiascontratovencido, that.toleranciadiascontratovencido) && Objects.equals(urlrecorrencia, that.urlrecorrencia) && Objects.equals(qtddiasexpirarsenha, that.qtddiasexpirarsenha) && Objects.equals(qtddiasestornoautomaticocontrato, that.qtddiasestornoautomaticocontrato) && Objects.equals(acessochamada, that.acessochamada) && Objects.equals(localacessochamada, that.localacessochamada) && Objects.equals(coletorchamada, that.coletorchamada) && Objects.equals(dataultimarepescagem, that.dataultimarepescagem) && Objects.equals(emailsfechamentoacessos, that.emailsfechamentoacessos) && Objects.equals(bloquearacessoseparcelaaberta, that.bloquearacessoseparcelaaberta) && Objects.equals(enviarsmsautomatico, that.enviarsmsautomatico) && Objects.equals(nomedatanascvalidar, that.nomedatanascvalidar) && Objects.equals(enviarremessasremotamente, that.enviarremessasremotamente) && Objects.equals(validarcontatometa, that.validarcontatometa) && Objects.equals(forcarcodigoalternativoacesso, that.forcarcodigoalternativoacesso) && Objects.equals(ecfporpagamento, that.ecfporpagamento) && Objects.equals(itemvendaavulsaautomatico, that.itemvendaavulsaautomatico) && Objects.equals(questionarioprimeiracompra, that.questionarioprimeiracompra) && Objects.equals(nrdiasvigentequestionarioprimeiracompra, that.nrdiasvigentequestionarioprimeiracompra) && Objects.equals(questionarioretornocompra, that.questionarioretornocompra) && Objects.equals(nrdiasvigentequestionarioretornocompra, that.nrdiasvigentequestionarioretornocompra) && Objects.equals(numerocielo, that.numerocielo) && Objects.equals(validarcpfduplicado, that.validarcpfduplicado) && Objects.equals(marcarpresencapeloacesso, that.marcarpresencapeloacesso) && Objects.equals(usarnomeresponsavelnota, that.usarnomeresponsavelnota) && Objects.equals(qtddiaprimeiraparcelavencidaestornarcontrato, that.qtddiaprimeiraparcelavencidaestornarcontrato) && Objects.equals(utilizarsistemaparaclube, that.utilizarsistemaparaclube) && Objects.equals(imprimirrecibopagtomatricial, that.imprimirrecibopagtomatricial) && Objects.equals(defaultenderecocorrespondecia, that.defaultenderecocorrespondecia) && Objects.equals(ecfapenasplano, that.ecfapenasplano) && Objects.equals(habilitargestaoarmarios, that.habilitargestaoarmarios) && Objects.equals(diaprorataarmario, that.diaprorataarmario) && Objects.equals(nomenclaturavendacredito, that.nomenclaturavendacredito) && Objects.equals(sequencialitem, that.sequencialitem) && Objects.equals(sesc, that.sesc) && Objects.equals(controleacessomultiplasempresasporplano, that.controleacessomultiplasempresasporplano) && Objects.equals(priorizarvendarapida, that.priorizarvendarapida) && Objects.equals(barrardevedorvendarapida, that.barrardevedorvendarapida) && Objects.equals(usaaprovafacil, that.usaaprovafacil) && Objects.equals(cancelarcontratonaunidadeorigemaotransferiraluno, that.cancelarcontratonaunidadeorigemaotransferiraluno) && Objects.equals(sequencialarquivo, that.sequencialarquivo) && Objects.equals(usarsistemainternacional, that.usarsistemainternacional) && Objects.equals(seqnotafiscalfamilia, that.seqnotafiscalfamilia) && Objects.equals(utilizartipoplano, that.utilizartipoplano) && Objects.equals(justfit, that.justfit) && Objects.equals(usardigitalcomoassinatura, that.usardigitalcomoassinatura) && Objects.equals(validarcpfresponsaveis, that.validarcpfresponsaveis) && Objects.equals(nomearquivoremessapadraotivit, that.nomearquivoremessapadraotivit) && Objects.equals(definirdatainicioplanosrecorrencia, that.definirdatainicioplanosrecorrencia) && Objects.equals(transferirautorizacaocobranca, that.transferirautorizacaocobranca) && Objects.equals(habilitarcanalcliente, that.habilitarcanalcliente) && Objects.equals(versaocanalcliente, that.versaocanalcliente) && Objects.equals(lancamentocontratosiguais, that.lancamentocontratosiguais) && Objects.equals(exibirmodalusuariosinativos, that.exibirmodalusuariosinativos) && Objects.equals(seqprocessoimportacao, that.seqprocessoimportacao) && Objects.equals(usarverificadorremessasrejeitadas, that.usarverificadorremessasrejeitadas) && Objects.equals(agruparremessasgetnet, that.agruparremessasgetnet) && Objects.equals(datasincronizacaooamd, that.datasincronizacaooamd) && Objects.equals(datainiciodesconsideraracessorisco, that.datainiciodesconsideraracessorisco) && Objects.equals(datafimdesconsideraracessorisco, that.datafimdesconsideraracessorisco) && Objects.equals(apresentarmarketplace, that.apresentarmarketplace) && Objects.equals(agruparremessascartaoedi, that.agruparremessascartaoedi) && Objects.equals(chavepublicasesc, that.chavepublicasesc) && Objects.equals(chaveprivadasesc, that.chaveprivadasesc) && Objects.equals(ativarverificarcartao, that.ativarverificarcartao) && Objects.equals(permitirreplicarplanoredeempresa, that.permitirreplicarplanoredeempresa) && Objects.equals(propagaraassinaturadigital, that.propagaraassinaturadigital) && Objects.equals(forcarutilizacaoplanoantigo, that.forcarutilizacaoplanoantigo) && Objects.equals(mascaratelefone, that.mascaratelefone) && Objects.equals(utilizarformatommddyyydtnascimento, that.utilizarformatommddyyydtnascimento) && Objects.equals(utilizarservicosesisc, that.utilizarservicosesisc);
    }

    @Override
    public int hashCode() {
        return Objects.hash(nrdiasavencer, qtdfaltapeso1, qtdfaltainiciopeso2, qtdfaltaterminopeso2, qtdfaltapeso3, carenciarenovacao, mascaramatricula, multa, juroparcela, questionariorematricula, questionarioretorno, questionarioprimeiravisita, nrdiasvigentequestionariovisita, nrdiasvigentequestionarioretorno, nrdiasvigentequestionariorematricula, toleranciapagamento, emailcontapagdigital, tokencontapagdigital, tokencontasms, codigo, carencia, nrdiasprorata, cpfvalidar, dataexpiracao, rodarsqlsbancoinicial, urlgoogleagenda, diasparabloqueio, vencimentocolaborador, usaecf, aliquotaservico, aliquotaproduto, alteracaodatabasecontrato, toleranciadiascontratovencido, urlrecorrencia, qtddiasexpirarsenha, qtddiasestornoautomaticocontrato, acessochamada, localacessochamada, coletorchamada, dataultimarepescagem, emailsfechamentoacessos, bloquearacessoseparcelaaberta, enviarsmsautomatico, nomedatanascvalidar, enviarremessasremotamente, validarcontatometa, forcarcodigoalternativoacesso, ecfporpagamento, itemvendaavulsaautomatico, questionarioprimeiracompra, nrdiasvigentequestionarioprimeiracompra, questionarioretornocompra, nrdiasvigentequestionarioretornocompra, numerocielo, validarcpfduplicado, marcarpresencapeloacesso, usarnomeresponsavelnota, qtddiaprimeiraparcelavencidaestornarcontrato, utilizarsistemaparaclube, imprimirrecibopagtomatricial, defaultenderecocorrespondecia, ecfapenasplano, habilitargestaoarmarios, diaprorataarmario, nomenclaturavendacredito, sequencialitem, sesc, controleacessomultiplasempresasporplano, priorizarvendarapida, barrardevedorvendarapida, usaaprovafacil, cancelarcontratonaunidadeorigemaotransferiraluno, sequencialarquivo, usarsistemainternacional, seqnotafiscalfamilia, utilizartipoplano, justfit, usardigitalcomoassinatura, validarcpfresponsaveis, nomearquivoremessapadraotivit, definirdatainicioplanosrecorrencia, transferirautorizacaocobranca, habilitarcanalcliente, versaocanalcliente, lancamentocontratosiguais, exibirmodalusuariosinativos, seqprocessoimportacao, usarverificadorremessasrejeitadas, agruparremessasgetnet, datasincronizacaooamd, datainiciodesconsideraracessorisco, datafimdesconsideraracessorisco, apresentarmarketplace, agruparremessascartaoedi, chavepublicasesc, chaveprivadasesc, ativarverificarcartao, permitirreplicarplanoredeempresa, propagaraassinaturadigital, forcarutilizacaoplanoantigo, mascaratelefone, utilizarformatommddyyydtnascimento, utilizarservicosesisc);
    }
}
