package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaosistemaCrmDiasPosVendasEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ConfiguracaosistemaCrmDiasPosVendasRepository extends PagingAndSortingRepository<ConfiguracaosistemaCrmDiasPosVendasEntity, Integer> {
    Optional<List<ConfiguracaosistemaCrmDiasPosVendasEntity>> findAllByOrderByCodigoAsc();

    @Query(value = "select count(codigo) > 0 as resultado from fecharmetadetalhado  where configuracaodiasposvenda  = :codigo limit 1",nativeQuery = true)
    boolean existeMetaConfiguracaoDiasPosVenda(@Param("codigo") Integer codigo);
}
