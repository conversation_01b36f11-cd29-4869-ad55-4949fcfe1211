package br.com.pacto.ms.contato.ia.service.contract;

import br.com.pacto.ms.comuns.data.pojo.output.ProdutoVO;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.ia.data.pojo.output.AlunoVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.*;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ContextoService {

    ContextoEmpresaVO consultarContextoEmpresa(Integer empresa);

    ContextosAlunoVO consultarContextosAluno(Integer empresa, Integer aluno);

    ContextosAlunoVO consultarContextosAluno(Integer empresa, Integer aluno, Boolean contextoTreino);

    ContextosAlunoVO consultarContextosAluno(Integer empresa, Integer aluno, Boolean contextoTreino, Integer codigoMetaExtra);

    List<ContextosAlunoVO> consultarContextosAlunosPorFase(Integer empresa, FasesCRMEnum fase, Pageable pageable);

    CodigosAlunoPaginaVO consultarCodigosAlunosPorFaseComPagina(Integer empresa, FasesCRMEnum fase, Integer codigoMeta ,Pageable pageable);

    ContextoPlanoVendaConsultorVO consultarContextoPlano(Integer plano);

    List<ContextoTurmaVO> consultarContextoTurmasPorEmpresa(Integer empresa);

    ContextosPlanosVO consultarContextosPlanos(Integer empresa);

    String identificadorEmpresa(Integer empresa);

    ResponsePactoConversasVO<ContextoEmpresaVO> atualizarContextoEmpresa(Integer empresa, Boolean rede);

    ResponsePactoConversasVO<ContextoEmpresaVO> atualizarContextoEmpresa(Integer empresa, Boolean matriz, Boolean rede, String chaveMatriz);

    List<ProdutoVO> consultarContextoProdutos(Integer empresa);

    ResponsePactoConversasVO<List<ContextoTurmaVO>> atualizarContextoTurmas(Integer empresa);

    ResponsePactoConversasVO<ContextosPlanosVO> atualizarContextoPlanos(Integer empresa);

    ResponsePactoConversasVO<List<ContextoFaseVO>> atualizarContextoFases(Integer empresa);

    List<ContextoFaseVO> consultarContextoFases(Integer empresa);

    ResponsePactoConversasVO<ResponseEnviarMensagemVO> atualizarContextoPersonalidade(Integer empresa);

    ResponsePactoConversasVO<List<ProdutoVO>> atualizarContextoProdutos(Integer empresa);

    ResponsePactoConversasVO<ContextosAlunoVO> atualizarContextoAluno(AlunoVO alunoVO);

    void atualizarContextosConversasIA(Integer codigoEmpresa, Boolean rede);
}
