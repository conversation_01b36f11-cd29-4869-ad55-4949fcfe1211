package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.ClienteEntity;
import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

public interface ClienteRepository extends PagingAndSortingRepository<ClienteEntity, Integer> {

    @Query("select c from ClienteEntity c where c.codigo = :codigo")
    @Transactional
    Optional<ClienteEntity> consultarPorCodigo(Integer codigo);


    @Query("select c from ClienteEntity c " +
            "inner join c.pessoa pes " +
            "inner join pes.emails em " +
            "WHERE em.email = :email " +
            "and c.empresa = :empresa " +
            "order by em.pessoa desc ")
        Optional<List<ClienteEntity>> consultarPorEmail(String email, Integer empresa);

    @Query("select c from ClienteEntity c " +
            "inner join c.pessoa pes " +
            "WHERE pes.cpf = :cpf " +
            "and c.empresa = :empresa " +
            "order by c.codigo desc ")
    Optional<List<ClienteEntity>> consultarPorCpf(String cpf, Integer empresa);
}
