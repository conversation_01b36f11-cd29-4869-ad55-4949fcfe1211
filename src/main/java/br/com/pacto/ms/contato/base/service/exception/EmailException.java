package br.com.pacto.ms.contato.base.service.exception;

import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.CustomException;

public class EmailException extends CustomException {
    private static final long serialVersionUID = -9192943455755903832L;

    public EmailException() {
    }

    public EmailException(ExceptionMessageVO... messagesParamVO) {
        super(messagesParamVO);
    }

    public EmailException(String message) {
        super(message);
    }

    public EmailException(String message, ExceptionMessageVO... messagesParamVO) {
        super(message, messagesParamVO);
    }

    public EmailException(Throwable cause) {
        super(cause);
    }

    public EmailException(Throwable cause, ExceptionMessageVO... messagesParamVO) {
        super(cause, messagesParamVO);
    }

    public EmailException(String message, Throwable cause) {
        super(message, cause);
    }

    public EmailException(String message, Throwable cause, ExceptionMessageVO... messagesParamVO) {
        super(message, cause, messagesParamVO);
    }

}
