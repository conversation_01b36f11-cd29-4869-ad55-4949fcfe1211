package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FecharmetadetalhadoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Integer acessocliente;

	private Integer codigoorigem;

	private Integer configuracaodiasposvenda;

	private Integer contrato;

	private Integer conviteaulaexperimental;

	private String descconfiguracaodiasmetas;

	private Integer diassemagendamento;

	private String motivoparaentraremcontatocomclienteposvenda;

	private String observacao;

	private Boolean obtevesucesso;

	private String origem;

	private Integer pesorisco;

	private Boolean repescagem;

	private Integer sessoesfinais;

	private Boolean tevecontato;

	private Integer vendaavulsa;

	private ClienteVO clienteBean;

	private FecharmetaVO fecharmetaBean;

	private HistoricoContatoVO historicocontatoBean;

	private IndicadoVO indicadoBean;

	private PassivoVO passivoBean;

}
