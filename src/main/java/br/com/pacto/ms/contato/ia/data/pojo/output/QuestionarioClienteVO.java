package br.com.pacto.ms.contato.ia.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class QuestionarioClienteVO {

    @Schema(description = "Código do questionário, não nulo.")
    private Integer codigo;

    @Schema(description = "Data do questionário, não nula.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date data;

    @Schema(description = "Código do cliente, não nulo.")
    private Integer cliente;

    @Schema(description = "Código do questionário, não nulo.")
    private Integer questionario;

    @Schema(description = "Observação adicional.")
    private String observacao;

    @Schema(description = "Tipo de BV, não nulo.")
    private Short tipobv;

    @Schema(description = "Data da última atualização, não nula.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date ultimaAtualizacao;

    @Schema(description = "Origem do sistema, não nula.")
    private Short origemsistema;

}
