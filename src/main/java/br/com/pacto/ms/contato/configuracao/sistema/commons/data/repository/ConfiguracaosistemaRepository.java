package br.com.pacto.ms.contato.configuracao.sistema.commons.data.repository;

import java.util.Optional;

import org.springframework.data.repository.PagingAndSortingRepository;

import br.com.pacto.ms.contato.configuracao.sistema.commons.data.domain.ConfiguracaosistemaEntity;

public interface ConfiguracaosistemaRepository extends PagingAndSortingRepository<ConfiguracaosistemaEntity, Integer> {

    Optional<ConfiguracaosistemaEntity> findByValidarcontatometaIsTrue();
}
