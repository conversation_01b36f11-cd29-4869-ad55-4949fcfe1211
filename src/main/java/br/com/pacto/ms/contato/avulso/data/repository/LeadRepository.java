package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.LeadEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

public interface LeadRepository extends PagingAndSortingRepository<LeadEntity, Integer> {

    @Query("select l from LeadEntity l where l.cliente.codigo = :codigo")
    LeadEntity consultarPorCliente(Integer codigo);
}
