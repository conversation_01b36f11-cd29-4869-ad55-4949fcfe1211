package br.com.pacto.ms.contato.log.data.pojo.output;

import br.com.pactosolucoes.commons.data.RepresentationModelData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogPorMinutoVO extends RepresentationModelData {
    @Schema(description = "Data da operação", example = "2021-01-01 10:01")
    private Date dataAlteracao;
    @Schema(description = "Identificador da operação realizada", example = "ALTERAÇÃO")
    private String operacao;
    @Schema(description = "Nome do responsável pela operação", example = "João <PERSON> Silva")
    private String responsavelAlteracao;
}
