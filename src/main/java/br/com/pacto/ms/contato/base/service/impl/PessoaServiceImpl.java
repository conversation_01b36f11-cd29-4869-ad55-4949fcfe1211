package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.pojo.output.PessoaVO;
import br.com.pacto.ms.contato.base.data.repository.PessoaRepository;
import br.com.pacto.ms.contato.base.service.contract.PessoaService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@AllArgsConstructor
public @Service  class PessoaServiceImpl<T> implements PessoaService {

    private PessoaRepository repository;

    @Override
    @LogExecution
    @ObjectMapper(PessoaVO.class)
    public T buscarPorMatricula(int matricula) {
        return (T) repository.buscarPorMatricula(matricula);
    }

    @Override
    @LogExecution
    @ObjectMapper(PessoaVO.class)
    public T buscarPorCodigoCliente(int codigoCliente) {
        return (T) repository.buscarPorCodigoCliente(codigoCliente);
    }
}
