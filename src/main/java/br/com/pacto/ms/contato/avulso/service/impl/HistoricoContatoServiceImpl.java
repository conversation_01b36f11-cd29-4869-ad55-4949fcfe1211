package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.*;
import br.com.pacto.ms.contato.avulso.data.pojo.input.*;
import br.com.pacto.ms.contato.avulso.data.pojo.output.AgendaVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.EmpresaVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.HistoricoContatoResumoVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.HistoricoContatoVO;
import br.com.pacto.ms.contato.avulso.service.contract.AgendaService;
import br.com.pacto.ms.contato.avulso.service.contract.ClienteService;
import br.com.pacto.ms.contato.avulso.service.contract.EmpresaService;
import br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage;
import br.com.pacto.ms.contato.base.data.pojo.input.MessageDTO;
import br.com.pacto.ms.contato.base.data.pojo.output.MalaDiretaVO;
import br.com.pacto.ms.contato.avulso.data.repository.HistoricoContatoRepository;
import br.com.pacto.ms.contato.avulso.service.contract.HistoricoContatoService;
import br.com.pacto.ms.contato.avulso.service.contract.ObjecaoService;
import br.com.pacto.ms.contato.avulso.service.contract.SituacaoclientesinteticodwService;
import br.com.pacto.ms.contato.avulso.service.contract.ValidacaoService;
import br.com.pacto.ms.contato.base.data.pojo.input.MailingAgendamentoDTO;
import br.com.pacto.ms.contato.base.data.pojo.input.MalaDiretaDTO;
import br.com.pacto.ms.contato.base.data.pojo.input.MalaDiretaEnviadaDTO;
import br.com.pacto.ms.contato.base.data.pojo.output.MailingAgendamentoVO;
import br.com.pacto.ms.contato.base.data.pojo.output.PessoaVO;
import br.com.pacto.ms.contato.base.service.contract.*;
import br.com.pacto.ms.contato.base.service.exception.LengthMensagemSmsExeption;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ChatMessageDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.IntegracaoCRMVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pacto.ms.contato.core.data.pojo.enums.*;
import br.com.pactosolucoes.commons.data.PagingAndSortingData;
import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.exception.GenericException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import br.com.pactosolucoes.utils.StringUtils;
import com.fasterxml.jackson.databind.node.ObjectNode;
import lombok.AllArgsConstructor;
import org.json.JSONObject;
import org.modelmapper.ModelMapper;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import static br.com.pacto.ms.contato.core.data.pojo.enums.TipoContatoCRM.*;
import static br.com.pactosolucoes.utils.DateUtils.dateInFullWithoutPlus;

@AllArgsConstructor
public @Service class HistoricoContatoServiceImpl<T> implements HistoricoContatoService<T> {

	private ModelMapper mapper;
	private HistoricoContatoRepository hcRepository;

	private MessageSource messageSource;
	private RequestService requestService;
	private ObjecaoService<?> objecaoService;
	private ValidacaoService validacaoService;
	private ApiService apiService;
	private SituacaoclientesinteticodwService<?> situacaoclientesinteticodwService;
	private TelefoneService  telefoneService;
	private MalaDiretaService<MalaDiretaVO> malaDiretaService;
	private MailingAgendamentoService<MailingAgendamentoVO> mailingAgendamentoService;
	private ClienteService clienteService;
	private PessoaService pessoaService;
    private ConfiguracaoSistemaCrmService<?, ?> dadosBasicoService;
	private EmailService emailService;
	private EmpresaService empresaService;
	private ModeloMensagemService modeloMensagemService;
	private AgendaService<AgendaVO> agendaService;


	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoResumoVO.class)
	public T buscarResumoPorCliente(Integer matricula) {
		Optional<List<HistoricoContatoResumo>> optHistoricoContatoResumo =  hcRepository
					.buscarPorIdAgrupado(matricula);

		if (optHistoricoContatoResumo.isPresent()) {
			AtomicLong total = new AtomicLong();
			List<HistoricoContatoResumo> historicoContatoResumoList = new ArrayList<>();
			Arrays.stream(values()).forEach(tipoContatoCRM -> {
				if (tipoContatoCRM.getSigla().equals(TODOS_CONTATOS.getSigla())) {
					return;
				}

				HistoricoContatoResumo historicoContatoResumo = new HistoricoContatoResumo();
				historicoContatoResumo.setId(tipoContatoCRM.getSigla());
				historicoContatoResumo.setDescricao(messageSource.getMessage(tipoContatoCRM.getDescricao(), null, requestService.getCurrentConfiguration().getDefaultLocale()));
				historicoContatoResumo.setTotal(0l);

				optHistoricoContatoResumo.get()
						.stream()
						.forEach(item -> {
							if (!Objects.isNull(item.getId()) && (item.getId().equals(historicoContatoResumo.getId())
									|| (item.getId().trim().isEmpty() && historicoContatoResumo.getId().equals(OUTROS.getSigla())))
							) {
								historicoContatoResumo.setTotal(item.getTotal());
								total.getAndAdd(item.getTotal());
							}
						});
				historicoContatoResumoList.add(historicoContatoResumo);
			});

			Arrays.stream(values()).forEach(tcc -> {
				for (int i = 0; i < historicoContatoResumoList.size(); i++) {
					// Remover menu outros se estiver zerado
					if (historicoContatoResumoList.get(i).getId().equals(OUTROS.getSigla()) && historicoContatoResumoList.get(i).getTotal().equals(0l)) {
						historicoContatoResumoList.remove(i);
					}
					// Remover menu Sem contato se estiver zerado
					if (historicoContatoResumoList.get(i).getId().equals(LIGACAO_SEM_CONTATO.getSigla()) && historicoContatoResumoList.get(i).getTotal().equals(0l)) {
						historicoContatoResumoList.remove(i);
					}
				}
			});

			if (historicoContatoResumoList.size() >= 1) {
				List<HistoricoContatoResumo> tmpObj = historicoContatoResumoList.stream().filter(item -> item.getId().equalsIgnoreCase(OBJECAO.getSigla())).collect(Collectors.toList());
				List<HistoricoContatoResumo> others = historicoContatoResumoList.stream().filter(item -> !item.getId().equalsIgnoreCase(OBJECAO.getSigla())).collect(Collectors.toList());
				
				historicoContatoResumoList.clear();
				historicoContatoResumoList.addAll(others);
				
				if (tmpObj.size() >= 1) {
					historicoContatoResumoList.addAll(tmpObj);
				}
					
				historicoContatoResumoList.add(0, new HistoricoContatoResumo(total.get(),
						TODOS_CONTATOS.getSigla(),
						messageSource.getMessage(TODOS_CONTATOS.getDescricao(), null, requestService.getCurrentConfiguration().getDefaultLocale())));
				Collections.sort(historicoContatoResumoList, (obj1, obj2) -> {
					if (obj1.getTotal() < obj2.getTotal()) {
						return 1;
					} else if(obj1.getTotal() > obj2.getTotal()) {
						return -1;
					} else {
						return 0;
					}
				});
			}

			return (T) historicoContatoResumoList;
		}

		throw new DataNotFoundException();
	}
	
	@LogExecution
	@Override
	@ObjectMapper(HistoricoContatoVO.class)
	public T bucarPorClienteETipoContato(Integer matricula, String tipoContato) {
		PagingAndSortingData p = requestService.getCurrentConfiguration().getPagingAndSortingData();

		Page<HistoricoContatoEntity> page;
		if (tipoContato.equals(OUTROS.getSigla())) {
			page = hcRepository
					.findByClienteByClienteCodigomatriculaAndTipocontatoIsNull(
							matricula, PageRequest.of(p.getPage(), p.getSize()))
					.orElseThrow(DataNotFoundException::new);
		} else {
			page = hcRepository
					.findByClienteByClienteCodigomatriculaAndTipocontatoContainingIgnoreCaseOrderByCodigoDesc(
							matricula, tipoContato, PageRequest.of(p.getPage(), p.getSize(), p.get().getSort()))
					.orElseThrow(DataNotFoundException::new);
		}

		page.get().forEach(item -> {
			ajustarHistoricoContatoParaExibir(item);
		});

		return (T) page;
	}

	@LogExecution
	@Override
	@ObjectMapper(HistoricoContatoVO.class)
	public T buscarPorCliente(Integer matricula) {
		PagingAndSortingData p = requestService.getCurrentConfiguration().getPagingAndSortingData();

		Page<HistoricoContatoEntity> page = hcRepository
				.findByClienteByClienteCodigomatriculaOrderByCodigoDesc(matricula, PageRequest.of(p.getPage(), p.getSize()))
				.orElseThrow(DataNotFoundException::new);

		page.get().forEach(item -> {
			ajustarHistoricoContatoParaExibir(item);
		});

		return (T) page;
	}

	private void ajustarHistoricoContatoParaExibir(HistoricoContatoEntity item) {
		ObjectNode tipoContato = !isNullOrEmpty(item.getTipocontato()) ? getContatoPorSiglaAsJson(item.getTipocontato()) : getContatoPorSiglaAsJson("OT");
		String descricao = messageSource.getMessage(tipoContato.get("descricao").asText(), null, requestService.getCurrentConfiguration().getDefaultLocale());
		if (!Objects.isNull(item.getTipocontato()) && item.getTipocontato().equals(OBJECAO.getSigla())) {
			descricao += " - " + item.getObjecaoByObjecao().getDescricao();
		}
		tipoContato.put("descricao", descricao);
		item.setDiaPorExtenso(dateInFullWithoutPlus(requestService.getCurrentConfiguration().getDefaultLocale(), item.getDia().toLocalDateTime()));
		item.setTipoContatoCRM(tipoContato);
        if (item.getTipocontato().equals(CONTATO_WHATSAPP.getSigla()))
            item.setObservacaoApresentarWA(ChatMessageDTO.transformarParaModelo(item.getObservacao()));
        else
            item.setObservacaoApresentar(item.getObservacao());
        item.setRespostaApresentar("");
		String faseDescricao = "";
		try {
			faseDescricao = FasesCRMEnum.getFasePorSigla(item.getFase()).getDescricao().trim();
		} catch (Exception e) {}
		item.setFaseDescricao(!faseDescricao.isEmpty() ? faseDescricao : "Contato Avulso");
		if (!Objects.isNull(item.getTipocontato()) && item.getTipocontato().equals(CONTATO_APP.getSigla()) && !Objects.isNull(item.getOpcoes()) && !item.getOpcoes().isEmpty()) {
			String perguntaApresentar = "";
			if (item.getOpcoes().toLowerCase().contains("texto")) {
				perguntaApresentar = "Pergunta dissertativa: " + item.getObservacao();
			} else {
				perguntaApresentar = "Pergunta objetiva: " + item.getObservacao();
				perguntaApresentar += " - (" + item.getOpcoes().replaceAll(";", ", ") + ")";
			}
			item.setObservacaoApresentar(perguntaApresentar);

			String respostaApresentar = messageSource.getMessage("historicocontatoc.ap.resposta", null, requestService.getCurrentConfiguration().getDefaultLocale()) + ": ";
			if (Objects.isNull(item.getResposta()) || item.getResposta().isEmpty()) {
				respostaApresentar += "---";
			} else {
				respostaApresentar += item.getResposta();
			}
			item.setRespostaApresentar(respostaApresentar);
		}
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	public T salvar(HistoricoContatoDTO hcDTO) {
		HistoricoContatoEntity historicoContatoEntity = mapper.map(hcDTO, HistoricoContatoEntity.class);
		return (T) hcRepository.save(historicoContatoEntity);
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	@Transactional(rollbackOn = Throwable.class)
	public T atualizarObjecao(HistoricoObjecaoDTO dto) {
		salvarDadosObjecaoComum(dto);
		return (T) salvarDW(hcRepository.save(mergeHistoricocontato(dto)));
	}

	private HistoricoContatoEntity mergeHistoricocontato(HistoricoContatoDTO dto) {
		HistoricoContatoEntity oldEntity = hcRepository
											.findById(dto.getCodigoHistorico())
											.orElseThrow(DataNotMatchException::new);

		HistoricoContatoEntity newEntity = mapper.map(dto, HistoricoContatoEntity.class);
		mapper.map(newEntity, oldEntity);
		return oldEntity;
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	@Transactional(rollbackOn = Throwable.class)
	public T salvarObjecao(HistoricoObjecaoDTO dto) {
		salvarDadosObjecaoComum(dto);

		HistoricoContatoEntity hcEntity = mapper.map(dto, HistoricoContatoEntity.class);
		hcEntity.setAgendaByAgenda(null);
		hcEntity.setClienteByCliente(ClienteEntity.builder().codigo(dto.getCliente()).build());
		hcEntity.setResponsavelcadastroByUsuario(UsuarioEntity.builder().codigo(dto.getResponsavelcadastro()).build());

		return (T) salvarDW(hcRepository.save(hcEntity));
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	@Transactional(rollbackOn = Throwable.class)
	public T atualizarHistoricoComSimplesRegistro(HistoricoContatoDTO dto) {
		dto.setResultado(objecaoService.buscarPorTipoSR(dto.getTipocontato()));
		return (T) salvarDW(hcRepository.save(mergeHistoricocontato(dto)));
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	@Transactional(rollbackOn = Throwable.class)
	public T criarHistoricoComSimplesRegistro(HistoricoContatoDTO dto) {
		dto.setResultado(objecaoService.buscarPorTipoSR(dto.getTiporesultado()));
		HistoricoContatoEntity hcEntity = mapper.map(dto, HistoricoContatoEntity.class);
		if (Objects.isNull(dto.getAgenda())) {
			hcEntity.setAgendaByAgenda(null);
		} else {
			hcEntity.setAgendaByAgenda(AgendaEntity.builder().codigo(dto.getAgenda()).build());
		}
		hcEntity.setClienteByCliente(ClienteEntity.builder().codigo(dto.getCliente()).build());
		hcEntity.setResponsavelcadastroByUsuario(UsuarioEntity.builder().codigo(dto.getResponsavelcadastro()).build());
		
		return (T) salvarDW(hcRepository.save(hcEntity));
	}

	@Override
	public T whatsApp(HistoricoContatoDTO dto, Integer matricula) {
		com.fasterxml.jackson.databind.ObjectMapper map = new com.fasterxml.jackson.databind.ObjectMapper();
		ObjectNode obj = map.createObjectNode();
		String urlWpp;
		String foneWpp;
		String ddi = "+55";

		if(dto.getCliente() == 0){
			urlWpp = "https://web.whatsapp.com";
			obj.put("url", urlWpp);
			return (T)obj;
		}else{

//			List<TelefoneVO> listfone = (List<TelefoneVO>)(telefoneService.buscarTelefonePorTipo(matricula, TipoTelefone.CELULAR.getCodigo()));
//			foneWpp =  listfone.get(0).getNumero().replaceAll("\\(\\)", "");

			foneWpp = dto.getTelefoneCelular().replaceAll("\\(\\)", "");

			if (!StringUtils.isBlank(foneWpp)) {
				urlWpp = "https://web.whatsapp.com/send?phone=" + ddi + foneWpp + "&text=" + dto.getObservacao();
			}
			else if (!StringUtils.isBlank(foneWpp)) {
				urlWpp = "https://web.whatsapp.com/send?phone=" + ddi + foneWpp + "&text=" + dto.getObservacao();
			}
			else{
				urlWpp = "https://web.whatsapp.com";
			}

			obj.put("url", urlWpp);
			criarHistoricoComSimplesRegistro(dto);
		}
		return (T)obj;
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	public T salvarApp(HistoricoServicosDTO dto) throws Exception {
		//validacaoService.verificarMetaaberta(dto.getDia().toLocalDateTime());
		MalaDiretaDTO mdDto =  (MalaDiretaDTO) this.getMalaDiretaServicos(dto);

		//maladireta
		MalaDiretaEnviadaDTO envDto = new MalaDiretaEnviadaDTO();
		envDto.setCliente(dto.getCliente());
		MalaDiretaVO mvo = malaDiretaService.salvar(mdDto);
	    //maladireta env
		envDto.setMaladireta(mvo.getCodigo());
		envDto.setCliente(dto.getCliente());
		envDto.setIndicado( dto.getIndicado());
		envDto.setPassivo(dto.getPassivo());
		malaDiretaService.salvaEnviada(envDto);
		//mailingagendamento
		MailingAgendamentoDTO dtomailing = new MailingAgendamentoDTO();
		dtomailing.setMaladireta(mvo.getCodigo());
		mailingAgendamentoService.salvar(dtomailing);


		String retornoGerarNotificacoes = apiService.enviarNotificacaoApp(requestService.getCurrentConfiguration().getCompanyKey(), dto.getCliente().toString(), dto.getTitulo(), dto.getObservacao(), dto.getOpcoes());
		if (retornoGerarNotificacoes.isEmpty() || retornoGerarNotificacoes.toUpperCase().contains("ERRO")) {
			throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.HISTORICO_CONTATO_APP_NOTIFICACAO_NAO_ENVIADA));
		}
		JSONObject jsonRetornoNotificacaoApp = new JSONObject(new JSONObject(retornoGerarNotificacoes).optString("return"));
		dto.setCodigonotificacao(jsonRetornoNotificacaoApp.optInt("cod"));

		return criarHistoricoComSimplesRegistro(dto);
	}

	@Override
	public T salvarEmail(HistoricoServicosDTO dto) {

		if (isNullOrEmpty(dto.getEmail())) {
			throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.HISTORICO_CONTATO_EMAIL_NAO_INFORMADO));
		}

		if(validacaoService.validaTermoSpamn())
			malaDiretaService.verificar(dto.getObservacao());

		//maladireta
		MalaDiretaDTO mdDto =  (MalaDiretaDTO) this.getMalaDiretaServicos(dto);
		mdDto.setMeiodeenvio((short) MeioEnvioEnum.EMAIL.getCodigo());
		mdDto.setEnviohabilitado(true);
		mdDto.setSmsmarketing(false);
		mdDto.setStatusentregabilidade(true);
		mdDto.setExcluida(false);
		MalaDiretaEnviadaDTO envDto = new MalaDiretaEnviadaDTO();
		envDto.setCliente(dto.getCliente());
		MalaDiretaVO mvo = malaDiretaService.salvar(mdDto);
		mdDto.setCodigo(mvo.getCodigo());
		envDto.setMaladireta(mvo.getCodigo());
		envDto.setIndicado( dto.getIndicado());
		envDto.setPassivo(dto.getPassivo());
		malaDiretaService.salvaEnviada(envDto);
		//mailingagendamento
		MailingAgendamentoDTO mailingAgendamentoDTO = new MailingAgendamentoDTO();
		mailingAgendamentoDTO.setMaladireta(mvo.getCodigo());
		mailingAgendamentoDTO.setOcorrencia(OcorrenciaEnum.INSTANTANEO.getCodigo());
		mailingAgendamentoDTO.setDatainicial(new Timestamp(new Date().getTime()));
		mailingAgendamentoDTO.setCron(mailingAgendamentoService.obterCron(new Date()));
		mailingAgendamentoService.salvar(mailingAgendamentoDTO);
		IntegracaoCRMVO integracaoVo = (IntegracaoCRMVO) dadosBasicoService.buscarDadosIntegracao();

		apiService.updateJenkinsInstantaneo(mdDto, integracaoVo, requestService.getCurrentConfiguration().getCompanyKey(), MeioEnvioEnum.EMAIL, TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO );

		dto.setResultado(objecaoService.buscarPorTipoSR(dto.getTipocontato()));

		return (T) criarHistoricoComSimplesRegistro(dto);

	}

	@Override
	public T salvarSms(HistoricoServicosDTO dto, int totalcaracteres) {
		String response = "";
		try {
			String tokenSMS = obterTokenSMS();
			if (tokenSMS.isEmpty()) {
				throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.HISTORICO_CONTATO_SMS_EMPRESA_NAO_POSSUI_SMS_EXCEPTION));
			}

			if (dto.getObservacao().length() > totalcaracteres) {
				throw new LengthMensagemSmsExeption();
			}

			PessoaVO pessoaVO = (PessoaVO) pessoaService.buscarPorCodigoCliente(dto.getCliente());

			if (isNullOrEmpty(dto.getTelefoneCelular())) {
				throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.HISTORICO_CONTATO_SMS_TELEFONE_NAO_INFORMADO));
			}

			String numero = dto.getTelefoneCelular();
			numero = numero.replaceAll("\\D", "");
			if (!numero.startsWith("55")) {
				numero = "55" + numero;
			}

			MessageDTO msg = new MessageDTO();
			msg.setNumero(numero);
			msg.setTipo("SMS");
			msg.setDataRegistro(dto.getDia().toString());
			msg.setCodigoExterno("");
			msg.setUrlAudio("");

			String mesg = (String) modeloMensagemService.personalizarTagNomePessoa(dto.getObservacao(), pessoaVO.getNome());
			msg.setMsg(retirarAcentuacaoRegex(mesg));

			MalaDiretaDTO mdDto = (MalaDiretaDTO) this.getMalaDiretaServicos(dto);
			//maladireta
			MalaDiretaEnviadaDTO envDto = new MalaDiretaEnviadaDTO();
			envDto.setCliente(dto.getCliente());
			MalaDiretaVO mvo = malaDiretaService.salvar(mdDto);
			mdDto.setCodigo(mvo.getCodigo());
			envDto.setMaladireta(mvo.getCodigo());
			envDto.setIndicado(dto.getIndicado());
			envDto.setPassivo(dto.getPassivo());
			malaDiretaService.salvaEnviada(envDto);
			//mailingagendamento
			MailingAgendamentoDTO dtomailing = new MailingAgendamentoDTO();
			dtomailing.setMaladireta(mvo.getCodigo());

			mailingAgendamentoService.salvar(dtomailing);
			response = apiService.sendSms(msg, requestService.getCurrentConfiguration().getCompanyKey(), tokenSMS);
		} catch (Exception e) {
			throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.HISTORICO_CONTATO_SMS_NAO_ENVIADO));
		}
		JSONObject jsonResponse = new JSONObject(response);
		if (jsonResponse.optString("status").equals("erro")) {
			throw new GenericException(new ExceptionMessageVO().message(ExceptionMessage.HISTORICO_CONTATO_SMS_NAO_ENVIADO));
		}
		if (!jsonResponse.optString("dados").isEmpty()) {
			JSONObject jsonDados = new JSONObject(jsonResponse.optString("dados"));
			if (jsonDados.optString("status").toLowerCase().contains("erro")) {
				ExceptionMessageVO exceptionMessageVO = new ExceptionMessageVO();
				exceptionMessageVO.setTextMessage(jsonDados.optString("status"));
				throw new GenericException(exceptionMessageVO);
			}
		}
		dto.setResultado(objecaoService.buscarPorTipoSR(dto.getTipocontato()));
		return (T) criarHistoricoComSimplesRegistro(dto);
	}

	public T possuiSms() {
		Boolean possuiSms = !obterTokenSMS().isEmpty();
		return (T) possuiSms;
	}

	private String obterTokenSMS() {
		try {
			EmpresaVO em = (EmpresaVO) empresaService.findByCodigo(requestService.getCurrentConfiguration().getCompanyId());
			if (!isNullOrEmpty(em.getTokenSms())) {
				return em.getTokenSms();
			} else if (!isNullOrEmpty(em.getTokenSmsShortCode())) {
				return em.getTokenSmsShortCode();
			}
			return "";
		} catch (Exception ex) {
			ex.printStackTrace();
			throw ex;
		}
	}

	private boolean isNullOrEmpty(String str) {
		return Objects.isNull(str) || str.isEmpty();
	}

	@Override
	@LogExecution
	@ObjectMapper(HistoricoContatoVO.class)
	public T salvarAgedamento(AgendaDTO dto) {
		defineResponsavelCadastroEColaboradorResponsavel(dto);

		AgendaVO agenda = agendaService.salvar(dto);

		HistoricoContatoDTO historicoContatoDTO = HistoricoContatoDTO.builder()
				.dia(dto.getDia())
				.cliente(dto.getCliente())
				.observacao(dto.getObservacao())
				.responsavelcadastro(dto.getResponsavelcadastro())
				.agenda(agenda.getCodigo())
				.fase(dto.getFase())
				.resultado(getResultado(dto))
				.tipocontato(dto.getTipocontato())
				.build();

		salvar(historicoContatoDTO);
		return (T) agenda;
	}

	@Override
	public T salvarAgendamentoLigacao(AgendaLigacaoDTO dto) {
		AgendaDTO agendamentoLigacao = new AgendaDTO();
		Integer codigoUsuarioZw = this.requestService.getCurrentConfiguration().getZwId();

		agendamentoLigacao.setTipocontato("TE");
		agendamentoLigacao.setTipoagendamento("LI");
		agendamentoLigacao.setContatoavulso(Boolean.TRUE);
		agendamentoLigacao.setDia(Timestamp.from(Instant.now()));
		agendamentoLigacao.setDatalancamento(Timestamp.from(Instant.now()));
		agendamentoLigacao.setDataagendamento(Timestamp.from(Instant.now()));

		agendamentoLigacao.setDia(dto.getDia());
		agendamentoLigacao.setCliente(dto.getCliente());
		agendamentoLigacao.setEmpresa(dto.getEmpresa());
		agendamentoLigacao.setHora(dto.getHora());
		agendamentoLigacao.setFase(dto.getFase());
		agendamentoLigacao.setMinuto(dto.getMinuto());
		agendamentoLigacao.setObservacao(dto.getObservacao());
		agendamentoLigacao.setResponsavelcadastro(codigoUsuarioZw);
		agendamentoLigacao.setColaboradorresponsavel(codigoUsuarioZw);
		agendamentoLigacao.setHoraformatada(dto.getHora().concat(":").concat(dto.getMinuto()));

		return salvarAgedamento(agendamentoLigacao);
	}

	private void defineResponsavelCadastroEColaboradorResponsavel(AgendaDTO dto) {
		Integer zwId = requestService.getCurrentConfiguration().getZwId();
		dto.setColaboradorresponsavel(Objects.isNull(dto.getColaboradorresponsavel()) ? zwId : dto.getColaboradorresponsavel());
		dto.setResponsavelcadastro(Objects.isNull(dto.getResponsavelcadastro()) ? zwId : dto.getResponsavelcadastro());
	}

	private String getResultado(AgendaDTO dto) {
		if(dto.getTipocontato().equals("TE")) {
			SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
			String dia = sdf.format(dto.getDia());
			return String.format("Ag.Ligação: %s %s", dia, dto.getHoraformatada());
		}

		return "";
	}

	private void salvarDadosObjecaoComum(HistoricoObjecaoDTO dto) {
		if (!dto.getContatoavulso()) {
			validacaoService.verificarMetaaberta(dto.getDia().toLocalDateTime());
		}

		dto.setResultado(objecaoService.buscarPorTipoOB(dto));

		objecaoService.salvarObjecaoDefinitiva(dto);
	}

	private HistoricoContatoEntity salvarDW(HistoricoContatoEntity entity) {
		Optional<HistoricoContatoEntity> optLastHC = hcRepository.findTop1ByClienteOrderByCodigoDesc(entity.getCliente());
		
		if (optLastHC.isPresent()) {
			situacaoclientesinteticodwService.atualizarPorCliente(optLastHC.get(), entity.getCliente());
		}
		
		return entity;
	}

	private T getMalaDiretaServicos(HistoricoServicosDTO dto) {

		MalaDiretaDTO mdDto = new MalaDiretaDTO();
		mdDto.setMensagem(dto.getObservacao());
		mdDto.setRemetente(dto.getResponsavelcadastro());
		mdDto.setContatoavulso(true);
		mdDto.setTitulo(dto.getTitulo());
		mdDto.setMeiodeenvio((short) dto.getMeio());
		mdDto.setTipoagendamento(TipoAgendamentoEnum.AGENDAMENTO_INSTANTANEO.getCodigo());
		mdDto.setDatacriacao(dto.getDia());
		mdDto.setDataenvio(dto.getDia());
		mdDto.setEmpresa(dto.getEmpresa());
		if (!Objects.isNull(dto.getModelo())) {
			if (dto.getModelo().equals(TipoPerguntaEnum.OBJETIVA.getCodigo())) {
				if (!StringUtils.isBlank(dto.getResposta1()) && !StringUtils.isBlank(dto.getResposta2())) {
					mdDto.setOpcoes(dto.getResposta1() + ";" + dto.getResposta2() + (!StringUtils.isBlank(dto.getResposta3()) ? ";" + dto.getResposta3() : ""));
				}
			} else if (dto.getModelo().equals(TipoPerguntaEnum.DISSERTATIVA.getCodigo())) {
				mdDto.setOpcoes("Confirmar;TEXTO");
			} else {
				mdDto.setOpcoes("");
			}
			dto.setOpcoes(mdDto.getOpcoes());
		}
		return (T) mdDto;
	}

	public static String retirarAcentuacaoRegex(String texto) {
		texto = texto.replaceAll("[ÂÀÁÄÃ]", "A");
		texto = texto.replaceAll("[âãàáä]", "a");
		texto = texto.replaceAll("[ÊÈÉË]", "E");
		texto = texto.replaceAll("[êèéë]", "e");
		texto = texto.replaceAll("[ÎÍÌÏ]", "I");
		texto = texto.replaceAll("[îíìï]", "i");
		texto = texto.replaceAll("[ÔÕÒÓÖ]", "O");
		texto = texto.replaceAll("[ôõòóö]", "o");
		texto = texto.replaceAll("[ÛÙÚÜ]", "U");
		texto = texto.replaceAll("[ûúùü]", "u");
		texto = texto.replaceAll("Ç", "C");
		texto = texto.replaceAll("ç", "c");
		texto = texto.replaceAll("[ýÿ]", "y");
		texto = texto.replaceAll("Ý", "Y");
		texto = texto.replaceAll("ñ", "n");
		texto = texto.replaceAll("Ñ", "N");
		texto = texto.replaceAll("['<>\\|]", " ");
		texto = texto.replaceAll("/", " ");
		return texto;
	}

}
