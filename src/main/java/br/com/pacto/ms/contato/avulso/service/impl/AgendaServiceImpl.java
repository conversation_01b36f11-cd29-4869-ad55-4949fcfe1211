package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.domain.AgendaEntity;
import br.com.pacto.ms.contato.avulso.data.pojo.input.AgendaDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.DataUtilDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.AgendaVO;
import br.com.pacto.ms.contato.avulso.data.repository.AgendaRepository;
import br.com.pacto.ms.contato.avulso.data.repository.ClienteRepository;
import br.com.pacto.ms.contato.avulso.service.contract.AberturametaService;
import br.com.pacto.ms.contato.avulso.service.contract.AgendaService;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.pojo.output.EmpresaDataUtilVO;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.service.contract.ConfiguracaoEmpresaService;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoAgendamentoApresentarEnum;
import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.DataValidateException;
import br.com.pactosolucoes.commons.exception.GenericException;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.*;
import static br.com.pactosolucoes.utils.DateUtils.maior;

@AllArgsConstructor
@Service
public class AgendaServiceImpl <T> implements AgendaService <T> {

    private ModelMapper mapper;
    private  AgendaRepository agendaRepository;
    private ConfiguracaoEmpresaService configuracaoEmpresaService;
    private RequestService requestService;
    private AberturametaService aberturametaService;
    private ModelMapper modelMapper;

    @Override
    public void validarHorario(AgendaDTO agendaDTO) {
            if(Integer.parseInt(agendaDTO.getHora()) > 23){
                throw  new GenericException(new ExceptionMessageVO().message(HORA_MAIOR_QUE_23_EXCEPTION));
            }
            if(Integer.parseInt(agendaDTO.getMinuto()) > 59){
                throw  new GenericException(new ExceptionMessageVO().message(MINUTO_MAIOR_QUE_59_EXCEPTION));
            }
    }

    @Override
    public void verificaExisteAgendamentoDiaHoraMinuto(AgendaDTO agendaDTO)  {
        if(agendaRepository.buscarAgendamentoPorDataCliente(agendaDTO.getDia(),
                agendaDTO.getCliente(), agendaDTO.getPassivo(), agendaDTO.getIndicado(),
                agendaDTO.getHora(), agendaDTO.getMinuto()).isPresent()){
            throw  new GenericException(new ExceptionMessageVO().message(AGENDAMENTO_EXISTENTE_EXCEPTION));
        }
    }

    public void validaDiasLimiteAgendamento(AgendaDTO agendaDTO) {
        EmpresaDataUtilVO empresaDataUtilVO = (EmpresaDataUtilVO) configuracaoEmpresaService
                .obterDataCalculadaDiasUteis(
                        agendaDTO.getDia(),
                        agendaDTO.isAnterior(),
                        agendaDTO.getNrDias(),
                        requestService.getCurrentConfiguration().getCompanyId()
                );
        DataUtilDTO dataUtilDTODto = modelMapper.map(empresaDataUtilVO, DataUtilDTO.class);

        if( maior(agendaDTO.getDia(), dataUtilDTODto.getDataUtil()) &&
                (agendaDTO.getTipoagendamento().equals(TipoAgendamentoApresentarEnum.AULAEXPERIMENTAL.getSigla()) ||
                        agendaDTO.getTipoagendamento().equals(TipoAgendamentoApresentarEnum.VISITNATE.getSigla()))) {
            throw new GenericException(new ExceptionMessageVO().message(DIAS_LIMITE_AGENDAMENTO_EXCEPTION));
        }
    }

    @Override
    public void validaAgendamento(AgendaDTO agendaDTO) {
        this.validaExisteCliente(agendaDTO.getCliente());
        this.validarHorario(agendaDTO);
        this.verificaExisteAgendamentoDiaHoraMinuto(agendaDTO);
        this.validaDiasLimiteAgendamento(agendaDTO);
        aberturametaService.verificarAberturaMetaColaboradorAgendamento(agendaDTO.getColaboradorresponsavel(),
                requestService.getCurrentConfiguration().getCompanyId(),
                agendaDTO.getDataagendamento().toLocalDateTime());
    }

    private ClienteRepository clienteRepository;

    private void validaExisteCliente(Integer cliente) {
        clienteRepository.findById(cliente).orElseThrow(() ->  new GenericException(new ExceptionMessageVO().message(CLIENTE_NAO_ENCONTRADO)));
    }

    @Override
    @LogExecution
    @ObjectMapper(AgendaVO.class)
    public T salvar(AgendaDTO agendaDTO) {
        this.validaAgendamento(agendaDTO);
        return (T) agendaRepository.save(mapper.map(agendaDTO, AgendaEntity.class));
    }

}
