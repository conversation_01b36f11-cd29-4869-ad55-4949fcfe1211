package br.com.pacto.ms.contato.base.data.domain;

import lombok.ToString;

import javax.persistence.*;

@Entity
@Table(name = "planomodalidade", schema = "public")
public class PlanoModalidadeEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "listavezessemana", length = 510)
    private String listavezessemana;

    @ManyToOne
    @JoinColumn(name = "modalidade")
    private ModalidadeEntity modalidade;

    @Column(name = "plano")
    private Integer plano;
}