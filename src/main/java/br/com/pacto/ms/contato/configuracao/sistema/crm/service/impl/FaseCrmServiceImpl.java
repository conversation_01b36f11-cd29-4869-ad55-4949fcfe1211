package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoSistemaCrmService;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.FaseCrmService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class FaseCrmServiceImpl<T, VO> extends Object implements FaseCrmService<T, VO> {

    private ConfiguracaoSistemaCrmService configuracaoSistemaCrmService;

    @Override
    @ObjectMapper(FaseVO.class)
    public T consultar(List<TipoFaseCRMEnum> tipoFaseCRMEnum) {
        return (T) Arrays.stream(FasesCRMEnum.values())
                .filter(f -> tipoFaseCRMEnum == null || tipoFaseCRMEnum.stream().anyMatch(t -> t.equals(f.getTipoFase())))
                .collect(Collectors.toList());
    }

    @Override
    public T consultarCodName(List<TipoFaseCRMEnum> tipoFaseCRMEnum, String descricao) {
        return (T) Arrays.stream(FasesCRMEnum.values())
                .filter(f -> StringUtils.isNotEmpty(f.getDescricao()) && f.getDescricao().contains(descricao))
                .filter(f -> tipoFaseCRMEnum == null || tipoFaseCRMEnum.stream().anyMatch(t -> t.equals(f.getTipoFase())))
                .collect(Collectors.toList())
                .stream().map(f -> new FaseVO(f.getName(),f.getDescricao(),f.getIdentificador()))
                .collect(Collectors.toList());
    }

    @Override
    @ObjectMapper(FaseVO.class)
    public T consultarOrdenacaoMetas() {
        List<FasesCRMEnum> fasesCrmOrdenadas = configuracaoSistemaCrmService.consultarOrdenacaoMetas();

        List<FasesCRMEnum> fasesCrmNaoOrdenadas = Arrays.stream(FasesCRMEnum.values())
                .filter(f -> !fasesCrmOrdenadas.contains(f))
                .collect(Collectors.toList());

        fasesCrmOrdenadas.addAll(fasesCrmNaoOrdenadas);
        return (T) fasesCrmOrdenadas;
    }

    @Override
    @ObjectMapper(FaseVO.class)
    public T consultarOrdenacaoMetasPadrao() {

        List<FasesCRMEnum> fasesCrmEnumEmOrdemPadrao = new ArrayList<>();
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.AGENDAMENTO);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.AGENDAMENTOS_LIGACOES);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.VINTE_QUATRO_HORAS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.RENOVACAO);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.DESISTENTES);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.VISITANTES_ANTIGOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.EX_ALUNOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.INDICACOES);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.GRUPO_RISCO);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.VENCIDOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.POS_VENDA);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.FALTOSOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.ANIVERSARIANTES);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.ULTIMAS_SESSOES);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.SEM_AGENDAMENTO);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CRM_EXTRA);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_INDICADOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_AGENDADOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_EX_ALUNOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_DESISTENTES);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_PASSIVO);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.INDICACOES_SEM_CONTATO);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.ALUNO_GYMPASS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.LEADS_HOJE);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.LEADS_ACUMULADAS);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.VISITA_RECORRENTE);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.CONVERSAO_LEAD);
        fasesCrmEnumEmOrdemPadrao.add(FasesCRMEnum.PASSIVO);

        List<FasesCRMEnum> fasesSemOrdenacao = Arrays.stream(FasesCRMEnum.values())
                .filter(f -> !fasesCrmEnumEmOrdemPadrao.contains(f))
                .collect(Collectors.toList());

        List<FasesCRMEnum> todasFases = new ArrayList<>();
        todasFases.addAll(fasesCrmEnumEmOrdemPadrao);
        todasFases.addAll(fasesSemOrdenacao);

        return (T) todasFases;
    }
}
