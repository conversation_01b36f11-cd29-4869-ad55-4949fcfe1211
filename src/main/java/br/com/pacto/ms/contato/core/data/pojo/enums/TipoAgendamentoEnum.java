package br.com.pacto.ms.contato.core.data.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum TipoAgendamentoEnum {

    AGENDAMENTO_PREVISTO(2,"Agendamento"),
    AGENDAMENTO_INSTANTANEO(1, "Instantâneo"),
    TODOS(3,"Todos");

    private Integer codigo;
    private String descricao;

    public static TipoAgendamentoEnum getTipo(Integer codigo){
        for(TipoAgendamentoEnum t : values()){
            if(t.getCodigo().equals(codigo)){
                return t;
            }
        }
        return null;
    }

}

