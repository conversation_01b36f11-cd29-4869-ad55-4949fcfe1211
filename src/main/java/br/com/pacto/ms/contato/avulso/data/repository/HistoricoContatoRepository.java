package br.com.pacto.ms.contato.avulso.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.HistoricoContatoEntity;
import br.com.pacto.ms.contato.avulso.data.domain.HistoricoContatoResumo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;


public interface HistoricoContatoRepository extends PagingAndSortingRepository<HistoricoContatoEntity, Integer>{

	@Query(   " select new br.com.pacto.ms.contato.avulso.data.domain.HistoricoContatoResumo(count(h) as total, h.tipocontato as id)"
			+ " from HistoricoContatoEntity h "
			+ "		join ClientEntity c ON h.cliente = c.codigo"
			+ " where c.codigomatricula = :matricula  "
			+ " group by h.tipocontato ORDER BY h.tipocontato")
	Optional<List<HistoricoContatoResumo>> buscarPorIdAgrupado(Integer matricula);

	@Query(" select hc from HistoricoContatoEntity hc "
			+ "	inner join ClientEntity c ON hc.cliente = c.codigo"
			+ " where c.codigomatricula = :matricula "
			+ " and hc.tipocontato like :tipocontato")
	Optional<Page<HistoricoContatoEntity>> findByClienteByClienteCodigomatriculaAndTipocontatoContainingIgnoreCaseOrderByCodigoDesc(Integer matricula, String tipocontato, Pageable pageable);

	@Query(" select hc from HistoricoContatoEntity hc "
			+ "	inner join ClientEntity c ON hc.cliente = c.codigo"
			+ " where c.codigomatricula = :matricula "
			+ " and (hc.tipocontato is null or hc.tipocontato = '')")
	Optional<Page<HistoricoContatoEntity>> findByClienteByClienteCodigomatriculaAndTipocontatoIsNull(Integer matricula, Pageable pageable);

	Optional<HistoricoContatoEntity> findTop1ByClienteOrderByCodigoDesc(Integer matricula);

	@Query(" select hc from HistoricoContatoEntity hc "
			+ "	inner join ClientEntity c ON hc.cliente = c.codigo"
			+ " where c.codigomatricula = :matricula "
			+ " order by hc.codigo desc ")
	Optional<Page<HistoricoContatoEntity>> findByClienteByClienteCodigomatriculaOrderByCodigoDesc(Integer matricula, Pageable pageable);

	@Query("select hc from HistoricoContatoEntity hc where hc.codigo = :cliente")
    List<HistoricoContatoEntity> consultarPorCliente(@Param("cliente") Integer cliente);

	@Query("select hc from HistoricoContatoEntity hc where hc.cliente = :cliente " +
			"and hc.resultado = :resultado and DATE(hc.dia) = DATE(:dia) order by hc.codigo desc")
	Optional<List<HistoricoContatoEntity>>  buscarPorConversa(@Param("cliente") Integer cliente, @Param("resultado") String resultado, @Param("dia") Timestamp dia);

}




