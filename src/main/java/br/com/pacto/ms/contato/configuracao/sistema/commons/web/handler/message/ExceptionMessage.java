package br.com.pacto.ms.contato.configuracao.sistema.commons.web.handler.message;

import br.com.pactosolucoes.utils.enums.APIMessages;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

import static org.springframework.http.HttpStatus.BAD_REQUEST;;

@Getter
@AllArgsConstructor
@RequiredArgsConstructor
public enum ExceptionMessage implements APIMessages {


	META_FECHADA_EXCEPTION(-100, "metafechada.exception", BAD_REQUEST),
	CONFIGURACAO_IA_EXCEPTION(-101, "configuracao.ia.exception", BAD_REQUEST);

	private Integer code;
	private String message;
	private HttpStatus statusCode;
	
	

}
