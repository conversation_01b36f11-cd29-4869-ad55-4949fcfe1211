package br.com.pacto.ms.contato.base.data.domain;

import lombok.Data;

import javax.persistence.*;

@Entity
@Table(name = "planoprodutosugerido", schema = "public")
@Data
public class PlanoProdutoSugeridoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long codigo;

    @Column(nullable = false)
    private Integer produto;

    @Column(nullable = false)
    private Integer plano;

    private Float valorproduto;

    private Boolean obrigatorio;

    @Column(name = "ativoplano", columnDefinition = "boolean default true")
    private Boolean ativoplano;

}
