package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.TermoSpamEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.TermoSpamDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.TermoSpamVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.TermoSpanRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.TermoSpanService;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@AllArgsConstructor
public class TermoSpanServiceImpl<T, DTO> extends Object implements TermoSpanService<T, DTO> {

    private ModelMapper mapper;
    private TermoSpanRepository repository;


    @Override
    @ObjectMapper(TermoSpamVO.class)
    public T consultar() {
        return (T) repository.findAll();
    }

    @Override
    public void excluir(String termo) {
        TermoSpamEntity termoSpamEntity = this.repository.findByTermo(termo)
                .orElseThrow(DataNotMatchException::new);

        this.repository.delete(termoSpamEntity);
    }

    @Override
    @ObjectMapper(TermoSpamVO.class)
    @Transactional(transactionManager = "clientsTransactionManager", rollbackFor = Exception.class)
    public T incluir(List<TermoSpamDTO> termos) {
        repository.deleteAll();
        List<TermoSpamEntity> termosEntity = termos.stream()
                .map(termo -> mapper.map(termo, TermoSpamEntity.class))
                .collect(java.util.stream.Collectors.toList());

        this.repository.saveAll(termosEntity);

        return (T) termosEntity;
    }
}
