package br.com.pacto.ms.contato.base.enums;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SituacaoClienteEnumConverter implements AttributeConverter<SituacaoClienteEnum, String> {

    @Override
    public String convertToDatabaseColumn(SituacaoClienteEnum situacaoClienteEnum) {
        if(situacaoClienteEnum == null){
            return null;
        }
        return situacaoClienteEnum.getCodigo();
    }

    @Override
    public SituacaoClienteEnum convertToEntityAttribute(String id) {
        return SituacaoClienteEnum.findByCodigo(id);
    }
}
