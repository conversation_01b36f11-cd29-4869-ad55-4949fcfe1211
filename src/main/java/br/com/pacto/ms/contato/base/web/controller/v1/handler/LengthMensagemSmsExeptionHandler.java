package br.com.pacto.ms.contato.base.web.controller.v1.handler;

import br.com.pacto.ms.contato.base.service.exception.LengthMensagemSmsExeption;
import br.com.pactosolucoes.commons.data.vo.ResponseImplLegacyVO;
import br.com.pactosolucoes.commons.data.vo.ResponseImplVO;
import br.com.pactosolucoes.commons.data.vo.ResponseVO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;

import static br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage.TOTAL_CARACTERES_EXCEPTION;
import static br.com.pactosolucoes.commons.enums.message.APIStructureMessages.PROCESS_FINISHED_ERROR;

public class LengthMensagemSmsExeptionHandler {

    private @Autowired
    RequestService requestService;

    @ExceptionHandler(value = {LengthMensagemSmsExeption.class})
    public ResponseEntity<?> handleArchetypeException(LengthMensagemSmsExeption exception) {
        ResponseVO responseVO = null;

        if (requestService.getCurrentConfiguration().getReponseLegacy()) {
            responseVO = ResponseImplLegacyVO.init()
                    .addMessage(TOTAL_CARACTERES_EXCEPTION);
        } else {
            responseVO = ResponseImplVO.init()
                    .addMessage(TOTAL_CARACTERES_EXCEPTION)
                    .addMessage(PROCESS_FINISHED_ERROR);
        }

        return new ResponseEntity<ResponseVO>(responseVO, null, TOTAL_CARACTERES_EXCEPTION.getStatusCode());

    }
}
