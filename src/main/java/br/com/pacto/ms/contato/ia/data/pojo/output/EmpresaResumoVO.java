package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pactosolucoes.commons.data.RepresentationModelData;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmpresaResumoVO extends RepresentationModelData {

    @Schema(description = "Código da empresa", example = "1")
    private Integer codigo;
    @Schema(description = "Nome da empresa", example = "Wp fitness")
    private String nome;
    @Schema(description = "Define se a empresa está ativa ou inativa", example = "true")
    private Boolean ativa;
}
