package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class EmpresaDTO {

        @Schema(description = "Código da empresa", example = "1")
        private Integer codigo;
        @Schema(description = "Nome da empresa", example = "EMPRESA 1")
        private String nome;
}
