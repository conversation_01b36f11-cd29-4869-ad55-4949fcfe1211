package br.com.pacto.ms.contato.avulso.data.domain;

import br.com.pacto.ms.contato.base.data.domain.EmailEntity;
import br.com.pacto.ms.contato.base.data.domain.EnderecoEntity;
import br.com.pacto.ms.contato.base.data.domain.TelefoneEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.util.Date;
import java.util.List;
import java.util.Set;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "pessoa", schema = "public")
public class PessoaEntity {

    @Id
    @Column(name = "codigo")
    private int codigo;

    @Column(name = "nome")
    private String nome;

    @Column(name = "cfp", nullable = false, length = 14)
    private String cpf;

    @Column(name = "nomemae", length = 50)
    private String nomeMae;

    @Column(name = "nomepai", length = 50)
    private String nomePai;

    @Column(name = "datanasc")
    private Date dataNasc;

    @Column(name = "datacadastro")
    private Date dataCadastro;

    @Column(name = "sexo", length = 2)
    private String sexo;

    @Column(name = "naturalidade")
    private String naturalidade;

    @Column(name = "nacionalidade", length = 20)
    private String nacionalidade;

    @Column(name = "estadocivil", length = 10)
    private String estadoCivil;

    @Column(name = "grauinstrucao")
    private Integer grauInstrucao;

    @Column(name = "profissao")
    private Integer profissao;

    @Column(name = "cpfpai", length = 30)
    private String cpfPai;

    @Column(name = "cpfmae", length = 30)
    private String cpfMae;

    @Column(name = "nomeconsulta", length = 80)
    private String nomeConsulta;

    @Column(name = "genero", length = 2)
    private String genero;

    @Column(name = "emailpai", columnDefinition = "TEXT")
    private String emailPai;

    @Column(name = "emailmae", columnDefinition = "TEXT")
    private String emailMae;

    @Column(name = "nomeregistro", length = 120)
    private String nomeRegistro;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "pessoa")
    private Set<EnderecoEntity> enderecos;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "pessoa")
    private List<EmailEntity> emails;

    @OneToMany(fetch = FetchType.EAGER)
    @JoinColumn(name = "pessoa")
    private Set<TelefoneEntity> telefones;

}
