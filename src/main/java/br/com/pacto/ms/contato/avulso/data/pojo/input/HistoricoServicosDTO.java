package br.com.pacto.ms.contato.avulso.data.pojo.input;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class HistoricoServicosDTO extends HistoricoContatoDTO {
    private String titulo;
    private String resposta1;
    private String resposta2;
    private String resposta3;
    private int meio;
    private String numero;
    private Integer modelo;
}
