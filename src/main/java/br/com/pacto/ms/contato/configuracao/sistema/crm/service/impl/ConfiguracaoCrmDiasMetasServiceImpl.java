package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmDiasMetasEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmDiasMetasDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmDiasMetasVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmDiasMetasRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ProdutoRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoCrmDiasMetasService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoFaseCRMEnum;
import br.com.pactosolucoes.commons.exception.DataNotFoundException;
import br.com.pactosolucoes.commons.exception.DataNotMatchException;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@AllArgsConstructor
public class ConfiguracaoCrmDiasMetasServiceImpl<T, DTO> extends Object implements ConfiguracaoCrmDiasMetasService<T, DTO> {

    private ModelMapper mapper;
    private ConfiguracaoCrmDiasMetasRepository repository;
    private ProdutoRepository produtoRepository;

    @Override
    public List<ConfiguracaoCrmDiasMetasVO> consultar(FasesCRMEnum fase) {
        return consultar(fase, null);
    }

    @Override
    public List<ConfiguracaoCrmDiasMetasVO> consultar(FasesCRMEnum fase, TipoFaseCRMEnum tipoFase) {
        List<ConfiguracaoCrmDiasMetasEntity> configuracaoCrmDiasMetasEntities = repository.consultarPorFaseTipoFase(fase)
                .orElseThrow(DataNotFoundException::new);
        if (tipoFase != null) {
            configuracaoCrmDiasMetasEntities.removeIf(c -> c.getFase() != null && !c.getFase().getTipoFase().equals(tipoFase));
        }

        List<ConfiguracaoCrmDiasMetasVO> configuracaoCrmDiasMetasVOS = configuracaoCrmDiasMetasEntities.stream().map(c -> {
            ConfiguracaoCrmDiasMetasVO vo = this.mapper.map(c, ConfiguracaoCrmDiasMetasVO.class);
            vo.setFaseVO(c.getFase() != null ? this.mapper.map(c.getFase(), FaseVO.class) : null);

            return vo;
        }).collect(Collectors.toList());

        return configuracaoCrmDiasMetasVOS;
    }

    @Override
    @ObjectMapper(ConfiguracaoCrmDiasMetasVO.class)
    public T incluir(ConfiguracaoCrmDiasMetasDTO configuracaoCrmDiasMetasDTO) {
        ConfiguracaoCrmDiasMetasEntity entity = mapper.map(configuracaoCrmDiasMetasDTO, ConfiguracaoCrmDiasMetasEntity.class);
        entity.setProduto(null);
        if(configuracaoCrmDiasMetasDTO.getProduto() != null){
            entity.setProduto(this.produtoRepository.findById(configuracaoCrmDiasMetasDTO.getProduto().getCodigo())
                    .orElseThrow(DataNotFoundException::new));
        }

        return  (T) this.repository.save(entity);
    }

    @Override
    public void excluir(Integer codigo) {
        ConfiguracaoCrmDiasMetasEntity configuracaoCrmDiasMetasEntity = this.repository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        this.repository.delete(configuracaoCrmDiasMetasEntity);
    }

    @Override
    @ObjectMapper(ConfiguracaoCrmDiasMetasVO.class)
    public T alterar(Integer codigo, ConfiguracaoCrmDiasMetasDTO configuracaoCrmDiasMetasDTO) {
        ConfiguracaoCrmDiasMetasEntity configuracaoCrmDiasMetasEntity = this.repository.findById(codigo)
                .orElseThrow(DataNotMatchException::new);
        this.mapper.getConfiguration().setAmbiguityIgnored(true);
        this.mapper.map(configuracaoCrmDiasMetasDTO, configuracaoCrmDiasMetasEntity);
        configuracaoCrmDiasMetasEntity.setCodigo(codigo);
        configuracaoCrmDiasMetasEntity.setProduto(null);

        if(configuracaoCrmDiasMetasDTO.getProduto() != null && configuracaoCrmDiasMetasDTO.getProduto().getCodigo() != null){
            configuracaoCrmDiasMetasEntity.setProduto(this.produtoRepository.findById(configuracaoCrmDiasMetasDTO.getProduto().getCodigo())
                    .orElseThrow(DataNotFoundException::new));
        }

        this.repository.save(configuracaoCrmDiasMetasEntity);
        return (T) configuracaoCrmDiasMetasEntity;
    }

}
