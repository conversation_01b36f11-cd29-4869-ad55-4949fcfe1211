package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ProativoDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.dadosbasicos.ScheduleNotificationsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoIAProativosService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@Tag(name = "Configurações Proativo", description = "Gestão de configurações para gerir os envios de proativos")
@RequestMapping(value = "/v1/configuracao/proativos")
@RestController
@RequiredArgsConstructor
public class ConfiguracaoCrmProativoController  extends BaseController {

    private final RequestService requestService;
    private final ConfiguracaoIAProativosService configuracaoIAProativosService;

    private void disableLegacyResponse() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
    }

    @Operation(summary = "Enviar notificacao para proativos")
    @PostMapping("/enviar-instrucao-notificacao")
    public ResponseEntity<?> enviarNotificacao(@RequestBody ProativoDTO proativoDTO) {
        disableLegacyResponse();
        return super.finish(configuracaoIAProativosService.enviarNotificacaco(proativoDTO));
    }

    @Operation(summary = "Enviar notificacao para proativos")
    @GetMapping("/obter-notificacoes")
    public ResponseEntity<?> obterNotificacoes(
            @Parameter(name = "codigoEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "codigoEmpresa", required = false) Integer codigoEmpresa
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoIAProativosService.obterNotificacoes(codigoEmpresa));
    }

}
