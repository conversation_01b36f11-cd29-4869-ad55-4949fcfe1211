package br.com.pacto.ms.contato.configuracao.sistema.empresa.web.controller.v1;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_EMPRESA;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.CONFIGURACAO_COMUM_EMPRESA;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import br.com.pacto.ms.contato.configuracao.sistema.empresa.service.contract.ConfiguracaoEmpresaService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Validated
@Tag(name = CONFIGURACAO_EMPRESA, description = CONFIGURACAO_COMUM_EMPRESA)

@RestController
@RequestMapping(value = "/v1/empresa")

public class ConfiguracaoEmpresaController extends BaseController {

    @Autowired
    private ConfiguracaoEmpresaService configuracaoEmpresaService;

    @Operation(summary = "Calcula dias úteis",
            description = "Verifica se a data passada é um dia útil considerando as configurações da empresa")
    @GetMapping("/obterDataCalculadaDiasUteis")
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<?> obterDataCalculadaDiasUteis(Date dataContrato, boolean anterior, int nrDias, int empresa) {
        return super.finish(configuracaoEmpresaService.obterDataCalculadaDiasUteis(dataContrato, anterior, nrDias, empresa));
    }
}
