package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaseIAVO;

import java.util.List;

public interface ConfiguracaoCrmFaseIAService<T, DTO> {

    List<ConfiguracaoCrmFaseIAVO> listarTodos();
//    List<ConfiguracaoCrmFaseIAVO> listarPorFases(List<String> fases, Integer codigoEmpresa);
    List<ConfiguracaoCrmFaseIAVO> listarAtivosPorEmpresa(Integer empresa);

    List<ConfiguracaoCrmFaseIAVO> listarPorFasesEMetaExtras(List<String> fases, Integer codigoEmpresa);

}
