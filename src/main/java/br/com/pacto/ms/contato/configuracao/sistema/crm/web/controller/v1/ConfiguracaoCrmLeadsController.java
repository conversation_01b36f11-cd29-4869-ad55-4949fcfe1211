package br.com.pacto.ms.contato.configuracao.sistema.crm.web.controller.v1;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.LeadsDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.ConfiguracaoIALeadsService;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Validated
@Tag(name = "Configurações Leads", description = "Gestão de configurações para gerir os envios de mensagens para leads")
@RequestMapping(value = "/v1/configuracao/leads")
@RestController
@RequiredArgsConstructor
public class ConfiguracaoCrmLeadsController extends BaseController {

    private final RequestService requestService;
    private final ConfiguracaoIALeadsService configuracaoIALeadsService;

    private void disableLegacyResponse() {
        requestService.getCurrentConfiguration().setReponseLegacy(false);
    }

    @Operation(summary = "Enviar notificacao para proativos")
    @PostMapping("/enviar-instrucao-notificacao")
    public ResponseEntity<?> enviarNotificacao(@RequestBody LeadsDTO leadsDTO) {
        disableLegacyResponse();
        return super.finish(configuracaoIALeadsService.enviarNotificacaco(leadsDTO));
    }

    @Operation(summary = "Enviar notificacao para proativos")
    @GetMapping("/obter-notificacoes")
    public ResponseEntity<?> obterNotificacoes(
            @Parameter(name = "codigoEmpresa", description = "Identificador da empresa", example = "1")
            @RequestParam(value = "codigoEmpresa", required = false) Integer codigoEmpresa
    ) {
        disableLegacyResponse();
        return super.finish(configuracaoIALeadsService.obterNotificacoes(codigoEmpresa));
    }

}
