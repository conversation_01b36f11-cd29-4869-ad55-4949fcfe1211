package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AlunoVO {

    @Schema(description = "Codigo da empresa.", example = "1")
    private Integer codigoEmpresa;

    @Schema(description = "Codigo cliente", example = "2")
    private Integer cliente;
}
