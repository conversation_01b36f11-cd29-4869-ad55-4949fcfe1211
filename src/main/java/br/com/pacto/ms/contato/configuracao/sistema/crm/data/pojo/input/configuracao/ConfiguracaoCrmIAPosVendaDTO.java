package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAPosVendaEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoCrmIAPosVendaDTO {

        private Integer codigo;
        private Integer codigoposvenda;
        private String instrucao;
        private Boolean habilitar;

        public static ConfiguracaoCrmIAPosVendaDTO toDto(ConfiguracaoCrmIAPosVendaEntity entity) {
            return ConfiguracaoCrmIAPosVendaDTO.builder()
                    .codigo(entity.getCodigo())
                    .codigoposvenda(entity.getCodigoposvenda())
                    .habilitar(entity.getHabilitar())
                    .instrucao(entity.getInstrucao())
                    .build();
        }
}
