package br.com.pacto.ms.contato.base.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.modelmapper.ModelMapper;
import org.springframework.stereotype.Service;

import br.com.pacto.ms.contato.avulso.service.exception.TermoBloqueadoException;
import br.com.pacto.ms.contato.base.data.domain.MaladiretaEntity;
import br.com.pacto.ms.contato.base.data.domain.MaladiretaenviadaEntity;
import br.com.pacto.ms.contato.base.data.pojo.input.MalaDiretaDTO;
import br.com.pacto.ms.contato.base.data.pojo.input.MalaDiretaEnviadaDTO;
import br.com.pacto.ms.contato.base.data.pojo.output.MalaDiretaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.TermoSpamVO;
import br.com.pacto.ms.contato.base.data.repository.MalaDiretaEnviadaRepository;
import br.com.pacto.ms.contato.base.data.repository.MalaDiretaRepository;
import br.com.pacto.ms.contato.base.data.repository.TermSspamRepository;
import br.com.pacto.ms.contato.base.service.contract.MalaDiretaService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;

import static br.com.pactosolucoes.utils.StringUtils.normalize;
import static br.com.pactosolucoes.utils.StringUtils.trocarAcentuacaoPorAcentuacaoHTML;

@AllArgsConstructor

public @Service
class MalaDiretaServicelmpl <T> implements MalaDiretaService<T> {

    private ModelMapper mapper;
    private MalaDiretaRepository hcRepository;
    private MalaDiretaEnviadaRepository hcEnvRepository;
    private TermSspamRepository hcTermoRepository;
    @Override
    @LogExecution
    @ObjectMapper(MalaDiretaVO.class)
    public T salvar(MalaDiretaDTO hcDTO) {
        return (T) hcRepository.save(mapper.map(hcDTO, MaladiretaEntity.class));
    }

    @Override
    @LogExecution
    @ObjectMapper(MalaDiretaVO.class)
    public T salvaEnviada(MalaDiretaEnviadaDTO hcDTO) {
        return (T) hcEnvRepository.save(mapper.map(hcDTO, MaladiretaenviadaEntity.class));
    }

    @Override
    @LogExecution
    public void verificar( String mensagem ) {
    	final String msg = mensagem.toUpperCase();
    	List<String> termosEncontrados = new ArrayList<>();
        hcTermoRepository.findAll().forEach(item ->{
        					String termo = item.getTermo().toUpperCase();
        					
                            if(msg.contains(normalize(termo)) || msg.contains(trocarAcentuacaoPorAcentuacaoHTML(termo))) {
                            	termosEncontrados.add(termo);
                            }
                       }
                );

         if(termosEncontrados.size() > 0){
        	 throw new TermoBloqueadoException();
             //throw new TermoBloqueadoException(new ExceptionMessageVO().message(ExceptionMessage.TERMO_BLOQUIEIO_EXCEPTION).params(termosEncontrados.toString()));
         };
    }

    @ObjectMapper(TermoSpamVO.class)
    public T buscarTermoSpam() {
        return (T) hcTermoRepository.findAll();
    }
}
