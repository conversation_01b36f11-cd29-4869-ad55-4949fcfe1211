package br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.MensagemEnviadaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDate;
import java.util.Date;
import java.util.Optional;

public interface MensagemEnviadaEntityRepository extends PagingAndSortingRepository<MensagemEnviadaEntity, Integer> {

    @Query("select m from MensagemEnviadaEntity m where m.codigoEmpresa = :codigoEmpresa and m.dataEnvio = :now and (m.nomeFase = :nomeFase or m.codigoMetaExtra = :condigoMetaExtra)")
    Optional<MensagemEnviadaEntity> buscarPorEmpresaOuChaveNoDiaDeHoje(Integer codigoEmpresa, Date now, String nomeFase, Integer condigoMetaExtra);

}
