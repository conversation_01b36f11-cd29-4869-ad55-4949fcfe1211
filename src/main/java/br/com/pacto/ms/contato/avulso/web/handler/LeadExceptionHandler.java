package br.com.pacto.ms.contato.avulso.web.handler;

import br.com.pacto.ms.contato.avulso.service.exception.CadastroLeadException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

@ControllerAdvice
public class LeadExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(CadastroLeadException.class)
    public ResponseEntity<Map<String, Object>> handleClienteException(CadastroLeadException ex) {
        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", LocalDateTime.now());
        body.put("status", HttpStatus.INTERNAL_SERVER_ERROR.value());
        body.put("error", "Not Found");
        body.put("exception", ex.getClass().getName());
        body.put("path", "/leads");
        body.put("cause", ex.getCause());
        body.put("message", ex.getMessage());
        return new ResponseEntity<>(body, HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
