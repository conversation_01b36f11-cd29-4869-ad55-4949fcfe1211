package br.com.pacto.ms.contato.avulso.data.pojo.input;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ObjecaoDTO {

    @JsonIgnore
    private Integer codigo;

    private String descricao;

    @Size(max = 50)
    private String grupo;

    private String comentario;

    @Size(max = 2)
    private String tipogrupo;

    private Boolean ativo;
}
