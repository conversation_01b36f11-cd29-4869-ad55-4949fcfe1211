package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoCrmFaseIADTO {

    @Schema(description = "Código do produto", example = "28")
    private Integer codigo;
    @Schema(description = "Código da empresa", example = "1")
    private Integer codigoEmpresa;
    @Schema(description = "Descrição da configuração", example = "Configuração de teste")
    private String descricao;
    @Schema(description = "Status da configuração", example = "true")
    private Boolean habilitar;
    @Schema(description = "Enum fase", example = "EX_ALUNOS")
    private FasesCRMEnum fase;
    @Schema(description = "Código meta extra (Metas configuradas manualmente em 'Meta Extra' )", example = "1")
    private Integer codigometaextra;
    @Schema(description = "Nome meta extra (Metas configuradas manualmente em 'Meta Extra' )", example = "Duas Parcelas Vendidas")
    private String nomemetaextra;
    @Schema(description = "Configuração para que possam ser configurados agendamentos específicos para fases", example = "Ex.: 1 Dia - Contato de boas-vindas;  15 Dias - Colete o feedback da primeira experiência; 20 - Dias - ....")
    private String mensagensextras;

    public static ConfiguracaoCrmFaseIADTO toDto(ConfiguracaoCrmFaseIAEntity entity) {
        return ConfiguracaoCrmFaseIADTO.builder()
                .codigo(entity.getCodigo())
                .codigoEmpresa(entity.getCodigoEmpresa())
                .descricao(entity.getDescricao())
                .habilitar(entity.getHabilitar())
                .fase(entity.getFase())
                .codigometaextra(entity.getCodigoMetaExtra())
                .nomemetaextra(entity.getNomeMetaExtra())
                .codigoEmpresa(entity.getCodigoEmpresa())
                .build();
    }

}
