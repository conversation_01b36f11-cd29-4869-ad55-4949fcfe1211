package br.com.pacto.ms.contato.base.data.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "estado", schema = "public")
public class EstadoEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "sigla", nullable = false, length = 2)
    private String sigla;

    @Column(name = "descricao", nullable = false, length = 50)
    private String descricao;

    @Column(name = "codigoibge")
    private String codigoibge;

    @OneToMany
    @JoinColumn(name = "pessoa")
    private List<EnderecoEntity> enderecos;
    @OneToMany
    @JoinColumn(name = "pessoa")
    private List<EmailEntity> emails;
}
