package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.PlanoEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface PlanoRepository extends PagingAndSortingRepository<PlanoEntity, Integer> {

    @Query("SELECT p FROM PlanoEntity p " +
            "WHERE :data BETWEEN p.vigenciade AND p.ingressoate AND p.empresa.codigo = :empresa " +
            "AND p.codigo NOT IN :codigoPlanosNaoConsultar")
    @Transactional
    List<PlanoEntity> consultarPlanosAtivos(@Param("data") Date data, @Param("empresa") Integer empresa, @Param("codigoPlanosNaoConsultar") List<Integer> codigoPlanosNaoConsultar);

    @Query("SELECT p FROM PlanoEntity p " +
            "WHERE :data BETWEEN p.vigenciade AND p.ingressoate AND p.empresa.codigo = :empresa ")
    @Transactional
    List<PlanoEntity> consultarPlanosAtivos(@Param("data") Date data, @Param("empresa") Integer empresa);

    @Query("SELECT p FROM PlanoEntity p " +
           " WHERE p.codigo = :codigo")
    Optional<PlanoEntity> consultarPorCodigo(@Param("codigo") Integer codigo);
}
