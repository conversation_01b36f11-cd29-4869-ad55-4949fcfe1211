package br.com.pacto.ms.contato.ia.data.pojo.output;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class ContextoPlanoVO {

    @Schema(description = "Código do plano.")
    private Integer codigo;

    @Schema(description = "Descrição do plano.")
    private String descricao;

    @Schema(description = "Indica se o plano possui bolsa.")
    private boolean bolsa;

    @Schema(description = "Indica se o plano é recorrente.")
    private boolean recorrencia;

    @Schema(description = "Indica se o plano é personal.")
    private Boolean planopersonal;
}