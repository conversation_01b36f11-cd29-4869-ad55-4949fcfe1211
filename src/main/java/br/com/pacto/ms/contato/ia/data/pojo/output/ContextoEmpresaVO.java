package br.com.pacto.ms.contato.ia.data.pojo.output;

import lombok.Data;

@Data
public class ContextoEmpresaVO {
    
     // Identificadores Fiscais e Registro
     private String cnpj;
     private String inscEstadual;
     private String inscMunicipal;
 
     // Endereço
     private String cep;
     private int estado;
     private String nomeEstado;
     private int cidade;
     private String nomeCidade;
     private String complemento;
     private String numero;
     private String setor;
     private String endereco;
 
     // Informações de Contato
     private String site;
     private String email;
     private String telComercial1;
     private String telComercial2;
     private String telComercial3;
 
     // Dados da Empresa
     private String razaoSocial;
     private String nomeFantasia;

 
     // Timezone e Localização
     private String timezoneDefault;
     private String latitude;
     private String longitude;
 
     // Configurações de Pagamento
     private boolean permiteContratosConcomitantes;
     private boolean permiteHorariosConcorrentesParaProfessor;
     private String urlLojaVendaOnline;
     private String urlPlanosLojaVendaOnline;
     private String urlProdutosLojaVendaOnline;
     private String urlAgendaAulasLojaVendaOnline;

     // Configurações da IA
     private String zapiToken;
     private String zapiIdInstancia;
     private String usuarioPactoLogin;
     private String usuarioPactoSenha;
     private String urlAgendaAulasOnline;
     private String proposito;
     private String emailResponsavelConversasAI;
     private String telefoneResponsavelConversasAI;
     private Boolean matriz;
     private Boolean rede;
     private String chaveMatriz;
     private Boolean desabilitarAgendamentoAulasExperimentais;
     private Boolean habilitarIntegracaoGymbot;

     //Configurações GymPass
     private boolean existeGymPass;
     private boolean existeTotalPass;

}
