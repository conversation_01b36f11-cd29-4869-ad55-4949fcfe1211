package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import br.com.pacto.ms.contato.base.data.domain.ClientEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface PessoaRepository extends PagingAndSortingRepository<ClientEntity, Integer> {


    @Query(  " select c  "
            + " from ClientEntity c "
            + " where c.codigomatricula = :matricula  ")
    Optional<List<ClientEntity>> buscarPorMatricula(int matricula);

    @Query(  " select p  "
            + " from PessoaEntity p "
            + " inner join ClienteEntity c ON c.pessoa = p.codigo "
            + " where c.codigo = :codigoCliente  ")
    Optional<PessoaEntity> buscarPorCodigoCliente(int codigoCliente);

}
