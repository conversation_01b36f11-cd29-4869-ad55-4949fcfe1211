package br.com.pacto.ms.contato.configuracao.sistema.commons.web.handler;

import static br.com.pacto.ms.contato.configuracao.sistema.commons.web.handler.message.ExceptionMessage.META_FECHADA_EXCEPTION;
import static br.com.pactosolucoes.commons.enums.message.APIStructureMessages.PROCESS_FINISHED_ERROR;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.MetaFechadaException;
import br.com.pactosolucoes.commons.data.vo.ResponseImplLegacyVO;
import br.com.pactosolucoes.commons.data.vo.ResponseImplVO;
import br.com.pactosolucoes.commons.data.vo.ResponseVO;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;

public @ControllerAdvice class MetaFechadaExceptionHandler {

	private @Autowired RequestService requestService;
	
	@ExceptionHandler(value = { MetaFechadaException.class })
	public ResponseEntity<?> handleArchetypeException(MetaFechadaException exception) {
		ResponseVO responseVO = null;

		if (requestService.getCurrentConfiguration().getReponseLegacy()) {
			responseVO = ResponseImplLegacyVO.init()
					.addMessage(META_FECHADA_EXCEPTION);
		} else {
			responseVO = ResponseImplVO.init()
					.addMessage(META_FECHADA_EXCEPTION)
					.addMessage(PROCESS_FINISHED_ERROR);
		}

		return new ResponseEntity<ResponseVO>(responseVO, null,META_FECHADA_EXCEPTION.getStatusCode());
	}
}
