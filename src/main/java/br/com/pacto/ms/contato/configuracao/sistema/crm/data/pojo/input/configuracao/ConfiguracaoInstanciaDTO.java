package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfiguracaoInstanciaDTO {

    @Schema(description = "Nome empresa", example = "Pacto soluções")
    private String nomeEmpresa;

    @Schema(description = "Whatsapp business ", example = "false")
    private Boolean whatsappBusiness;

    @Schema(description = "tipo dispositivo ", example = "false")
    private Boolean isDevice;

    @Schema(description = "flag nova instancia", example = "true")
    private Boolean flagNovaInstancia;

    @Schema(description = "codigo da empresa configurada na instancia", example = "1")
    private Integer codigoEmpresa;
}
