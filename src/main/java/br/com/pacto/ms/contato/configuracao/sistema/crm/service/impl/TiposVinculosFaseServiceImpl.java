package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;


import br.com.pacto.ms.contato.avulso.data.domain.PessoaEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.TiposVinculosFaseEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.VinculoTiposColaboradorPorFaseDTO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.FaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.VinculoFaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.VinculoTiposColaboradorPorFaseVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos.TipoColaboradorVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.TiposVinculosFaseRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract.TiposVinculosFaseService;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import br.com.pacto.ms.contato.log.data.domain.LogEntity;
import br.com.pacto.ms.contato.log.service.contract.LogService;
import br.com.pacto.ms.contato.log.service.contract.UsuarioService;
import br.com.pactosolucoes.commons.util.AnnotationUtils;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import lombok.AllArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@AllArgsConstructor
public class TiposVinculosFaseServiceImpl<T, DTO> extends Object implements TiposVinculosFaseService<T,DTO> {


    private ModelMapper mapper;
    @Autowired
    private TiposVinculosFaseRepository repository;
    @Autowired
    private UsuarioService usuarioService;
    @Autowired
    private RequestService requestService;
    @Autowired
    private LogService logService;

    @Override
    public List<VinculoFaseVO> consutlar() {
        return this.repository.findOrderByFase()
                .stream()
                .map(t -> this.mapper.map(t, VinculoFaseVO.class))
                .collect(Collectors.toList());
    }

    @Override
    public List<VinculoTiposColaboradorPorFaseVO> consultarPorFases() {
        return consultarPorFases(null);
    }

    @Override
    public VinculoTiposColaboradorPorFaseVO consultarPorFase(FasesCRMEnum fase) {
        List<VinculoTiposColaboradorPorFaseVO> vinculoTiposColaboradorPorFaseVOS = consultarPorFases(Arrays.asList(fase));
        return vinculoTiposColaboradorPorFaseVOS.size() > 0 ? vinculoTiposColaboradorPorFaseVOS.get(0) : null;
    }

    @Override
    public List<VinculoTiposColaboradorPorFaseVO> consultarPorFases(List<FasesCRMEnum> fasesCRMEnums) {

        List<TiposVinculosFaseEntity> tiposVinculosFaseEntities;
        if(fasesCRMEnums == null){
            tiposVinculosFaseEntities = this.repository.findOrderByFase();
        }else{
            tiposVinculosFaseEntities = this.repository.findByFasesCRMEnums(fasesCRMEnums);
        }

        List<VinculoTiposColaboradorPorFaseVO> vinculoTiposColaboradorPorFaseVOS = Arrays.stream(FasesCRMEnum.values()).map(fase -> {
            VinculoTiposColaboradorPorFaseVO vo = new VinculoTiposColaboradorPorFaseVO();
            vo.setFase(this.mapper.map(fase, FaseVO.class));
            vo.setTiposColaboradores(tiposVinculosFaseEntities.stream()
                    .filter(vinculosFaseEntity -> vinculosFaseEntity.getFase() != null && vinculosFaseEntity.getFase().equals(fase))
                    .map(vinculosFaseEntity -> this.mapper.map(vinculosFaseEntity.getTipoColaborador(), TipoColaboradorVO.class))
                    .collect(Collectors.toList())
            );
            return vo;
        }).collect(Collectors.toList());

        return vinculoTiposColaboradorPorFaseVOS;
    }

    @Override
    @Transactional
    public VinculoTiposColaboradorPorFaseVO alterarTodos(FasesCRMEnum fase, List<TipoColaboradorEnum> tiposColaboradores) {
        List<TiposVinculosFaseEntity> vinculosAtuais = this.repository.findByFase(fase);
        List<TipoColaboradorEnum> vinculosRemover = vinculosAtuais.stream()
                .filter(tiposVinculosFaseEntity -> !tiposColaboradores.contains(tiposVinculosFaseEntity.getTipoColaborador()))
                .map(TiposVinculosFaseEntity::getTipoColaborador).collect(Collectors.toList());

        List<TipoColaboradorEnum> vinculosAdicionar = tiposColaboradores.stream()
                .filter(tipoColaboradorEnum -> !vinculosAtuais.stream().map(TiposVinculosFaseEntity::getTipoColaborador).collect(Collectors.toList()).contains(tipoColaboradorEnum))
                .collect(Collectors.toList());
        List<TiposVinculosFaseEntity> tiposVinculosFaseEntitiesRemovidas = new ArrayList<>();


        vinculosRemover.stream().forEach(tipoColaboradorEnum -> {
            Optional<List<TiposVinculosFaseEntity>> byFaseAndTipoColaborador = this.repository.findByFaseAndTipoColaborador(fase, tipoColaboradorEnum);
            if(byFaseAndTipoColaborador.isPresent()){
                tiposVinculosFaseEntitiesRemovidas.addAll(byFaseAndTipoColaborador.get());
                byFaseAndTipoColaborador.get().forEach(this.repository::delete);
            }
        });

        List<TiposVinculosFaseEntity> vinculosFaseEntitiesAdicionadas = vinculosAdicionar.stream().map(tipoColaboradorEnum -> {
            TiposVinculosFaseEntity tiposVinculosFaseEntity = new TiposVinculosFaseEntity();
            tiposVinculosFaseEntity.setFase(fase);
            tiposVinculosFaseEntity.setTipoColaborador(tipoColaboradorEnum);
            return tiposVinculosFaseEntity;
        }).collect(Collectors.toList());
        vinculosFaseEntitiesAdicionadas.forEach(this.repository::save);

        if(vinculosFaseEntitiesAdicionadas.size() > 0 || vinculosRemover.size() > 0){
            PessoaEntity pessoa = usuarioService.consultarPessoa(requestService.getCurrentConfiguration().getZwId());
            String valorCampoAnterior = vinculosAtuais.stream().map(TiposVinculosFaseEntity::getTipoColaborador).map(TipoColaboradorEnum::getDescricao).collect(Collectors.joining(","));
            List<TiposVinculosFaseEntity> vinculosAlterados = vinculosAtuais.stream()
                    .filter(tiposVinculosFaseEntity -> !tiposVinculosFaseEntitiesRemovidas.contains(tiposVinculosFaseEntity))
                    .collect(Collectors.toList());
            vinculosAlterados.addAll(vinculosFaseEntitiesAdicionadas);
            String valorCampoAlterado = vinculosAlterados.stream()
                    .map(TiposVinculosFaseEntity::getTipoColaborador)
                    .map(TipoColaboradorEnum::getDescricao)
                    .collect(Collectors.joining(","));

            LogEntity logEntity = new LogEntity().builder()
                    .dataAlteracao(new java.util.Date())
                    .nomeEntidade(AnnotationUtils.getTableName(TiposVinculosFaseEntity.class))
                    .nomeEntidadeDescricao(AnnotationUtils.getEntityDescription(TiposVinculosFaseEntity.class))
                    .responsavelAlteracao(pessoa.getNome())
                    .pessoa(pessoa.getCodigo())
                    .nomeCampo("tipoColaborador")
                    .valorCampoAnterior(valorCampoAnterior)
                    .valorCampoAlterado(valorCampoAlterado)
                    .tags(fase.getName()+",Responsáveis pelas fases")
                    .operacao("ALTERACAO")
                    .build();
            this.logService.incluir(logEntity);
        }

        return this.consultarPorFase(fase);
    }

    @Override
    public List<VinculoTiposColaboradorPorFaseVO> alterarTodos(List<VinculoTiposColaboradorPorFaseDTO> vinculoTiposColaboradorPorFase) {
        vinculoTiposColaboradorPorFase.forEach(vinculo -> this.alterarTodos(vinculo.getFase(), vinculo.getTiposColaboradores()));
        return this.consultarPorFases(vinculoTiposColaboradorPorFase.stream().map(VinculoTiposColaboradorPorFaseDTO::getFase).collect(Collectors.toList()));
    }

    @Override
    public List<VinculoTiposColaboradorPorFaseVO> consutlarPadrao() {
        List<VinculoTiposColaboradorPorFaseVO> vos = new ArrayList<>();

        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.GRUPO_RISCO, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.ANIVERSARIANTES, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PROFESSOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.FALTOSOS, TipoColaboradorEnum.PROFESSOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.AGENDAMENTO, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.DESISTENTES, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.RENOVACAO, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PROFESSOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.VINTE_QUATRO_HORAS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.VISITA_RECORRENTE, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.INDICACOES, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.GRUPO_RISCO, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.POS_VENDA, TipoColaboradorEnum.CONSULTOR, TipoColaboradorEnum.PROFESSOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.EX_ALUNOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.LIGACAO_AGENDADOS_AMANHA, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.VISITANTES_ANTIGOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CONVERSAO_INDICADOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CONVERSAO_AGENDADOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CONVERSAO_EX_ALUNOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CONVERSAO_VISITANTES_ANTIGOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CONVERSAO_DESISTENTES, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CONVERSAO_PASSIVO, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.VENCIDOS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.AGENDAMENTOS_LIGACOES, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.INDICACOES_SEM_CONTATO, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.PASSIVO, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.ALUNO_GYMPASS, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.CRM_EXTRA, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.LEADS_HOJE, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.VISITA_RECORRENTE, TipoColaboradorEnum.CONSULTOR));
        vos.add(new VinculoTiposColaboradorPorFaseVO(FasesCRMEnum.ALUNO_ULTIMO_ACESSO_GYMPASS, TipoColaboradorEnum.CONSULTOR));

        return vos;
    }
}
