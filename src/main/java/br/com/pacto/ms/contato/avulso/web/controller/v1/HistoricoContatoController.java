package br.com.pacto.ms.contato.avulso.web.controller.v1;

import br.com.pacto.ms.contato.avulso.data.pojo.input.*;
import br.com.pacto.ms.contato.avulso.data.pojo.output.HistoricoContatoVO;
import br.com.pacto.ms.contato.avulso.service.contract.HistoricoContatoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.web.controller.BaseController;
import br.com.pactosolucoes.commons.web.security.contract.RequestService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.json.JSONObject;
import org.modelmapper.ModelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Positive;
import javax.validation.constraints.Size;
import java.util.Objects;

import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.HISTORICO_CONTATO_PESSOAL;
import static br.com.pacto.ms.contato.config.web.swagger.SwaggerConstant.HISTORICO_CONTATO_PESSOAL_DESCRICAO;
import static br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum.getListFaseCRM;
import static br.com.pacto.ms.contato.core.data.pojo.enums.TipoAgendamentoApresentarEnum.getListTipoAgendamentoApresentarEnum;
import static br.com.pacto.ms.contato.core.data.pojo.enums.TipoContatoCRM.TODOS_CONTATOS;
import static br.com.pacto.ms.contato.core.data.pojo.enums.TipoContatoCRM.getContatoPorSigla;

@Tag(name = HISTORICO_CONTATO_PESSOAL, description = HISTORICO_CONTATO_PESSOAL_DESCRICAO)

@Validated
@RestController
@RequestMapping("/v1/avulso/historico")
public class HistoricoContatoController extends BaseController {

	private ModelMapper mapper;

	@Autowired
	private HistoricoContatoService<HistoricoContatoVO> hcService;

	@Autowired
	private RequestService requestService;

	@Operation(summary = "Busca os históricos agrupados", 
			   description = "Busca os históricos agrupados por tipo de contato através de um cliente específico.")
	@LogExecution
	@GetMapping("/agrupadoPorTipoContato/matricula-cliente/{matricula}")
	public ResponseEntity<?> buscarPorGrupo(@Positive @PathVariable Integer matricula) {
		return super.finish(hcService.buscarResumoPorCliente(matricula));
	}
	
	@Operation(summary = "Busca os históricos de forma simples", 
			   description = "Busca os históricos como POJO através da Matricula do cliente e Tipo Contato. Exemplo: EM, TE, PE...")
	@LogExecution
	@GetMapping("/cliente-matricula/{matricula}/tipoContato/{tipoContato}")
	public ResponseEntity<?> buscarPorMatriculaTipoContato(@Positive @PathVariable Integer matricula,
													@NotEmpty @Size(min = 1, max = 2)
													@PathVariable String tipoContato) {
		return super.finish(hcService.bucarPorClienteETipoContato(matricula, tipoContato));
	}

	@Operation(summary = "Busca os históricos de forma simples do cliente",
			description = "Busca os históricos como POJO através da Matricula do cliente")
	@LogExecution
	@GetMapping("/cliente-matricula/{matricula}")
	public ResponseEntity<?> buscarPorMatricula(@Positive @PathVariable Integer matricula) {
		JSONObject filter = requestService.getCurrentConfiguration().getFilters();
		ResponseEntity<?> response = null;

		if (Objects.nonNull(filter) && filter.has("quicksearchValue") &&
				getContatoPorSigla(filter.getString("quicksearchValue")) != null &&
				!getContatoPorSigla(filter.getString("quicksearchValue")).equals(TODOS_CONTATOS)) {
			response = buscarPorMatriculaTipoContato(matricula, filter.getString("quicksearchValue"));
		} else {
			response = super.finish(hcService.buscarPorCliente(matricula));
		}
		return response;
	}


	@Operation(summary = "Criar um histórico de contato",
			description = "Cria um histórico de contato no avulso.")
	@LogExecution
	@PostMapping
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> salvar(@Valid @RequestBody HistoricoContatoDTO dto) {
		return super.finish(hcService.salvar(dto));
	}

	@Operation(summary = "Criar objeção no Histórico",
			description = "Cria uma objeção no histórico de contato.")
	@LogExecution
	@PostMapping("/objecao")
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> criarObjecao(@Valid @RequestBody HistoricoObjecaoDTO dto) {
		return super.finish(hcService.salvarObjecao(dto));
	}

	@Operation(summary = "Atualizar objeção no Histórico",
			description = "Atualiza a objeção no histórico de contato.")
	@LogExecution
	@PutMapping("/id/{id}/objecao")
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> atualizarObjecao(@Positive @PathVariable Integer id,
											  @Valid @RequestBody HistoricoObjecaoDTO dto) {
		dto.setCodigoHistorico(id);
		return super.finish(hcService.atualizarObjecao(dto));
	}

	@Operation(summary = "Criar objeção no Histórico",
			description = "Cria uma objeção no histórico de contato.")
	@LogExecution
	@PostMapping(value = "/simplesRegistro", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> criarHistoricoComSimplesRegistro(@Valid @RequestBody HistoricoContatoDTO dto) {
		return super.finish(hcService.criarHistoricoComSimplesRegistro(dto));
	}

	@Operation(summary = "Atualizar simples registro no Histórico",
			description = "Atualiza um simples registro no histórico de contato.")
	@LogExecution
	@PutMapping("/id/{id}/simplesRegistro")
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> atualizarHistoricoComSimplesRegistro(@Positive @PathVariable Integer id,
											  @Valid @RequestBody HistoricoContatoDTO dto) {
		dto.setCodigoHistorico(id);
		return super.finish(hcService.atualizarHistoricoComSimplesRegistro(dto));
	}

	@Operation(summary = "Criar um contato  do tipo WhatsApp",
			description = "Cria um contato do tipo WhatsApp e retorna a url para comunicação com o cliente via WhatsApp.")
	@LogExecution
	@PostMapping(value = "/whatsApp/{matricula}", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> whatsApp (@Valid @RequestBody HistoricoContatoDTO dto, @PathVariable Integer matricula) {
		return super.finish(hcService.whatsApp(dto, matricula));
	}

	@Operation(summary = "Criar um contato  do tipo app e dispara para o aplicativo",
			description = "Dispara uma mensagem avulsa para o aluno e salva o historico de contato.")
	@LogExecution
	@PostMapping(value = "/app", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> app (@Valid @RequestBody HistoricoServicosDTO dto) throws Exception {
		return super.finish(hcService.salvarApp(dto));
	}

	@Operation(summary = "Criar um contato  do tipo email e dispara para o cliente",
			description = "Dispara uma mensagem avulsa de email para o aluno e salva o historico de contato.")
	@LogExecution
	@PostMapping(value = "/email", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> email (@Valid @RequestBody HistoricoServicosDTO dto) {
		return super.finish(hcService.salvarEmail(dto));
	}

	@Value("${propriedades.tamanho_msg_sms}")
	private int tamanhomsg;

	@Operation(summary = "Criar um contato  do tipo sms e dispara para o cliente",
			description = "Dispara uma mensagem avulsa de sms para o aluno e salva o historico de contato.")
	@LogExecution
	@PostMapping(value = "/sms", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> sms (@Valid @RequestBody HistoricoServicosDTO dto) {
				return super.finish(hcService.salvarSms(dto,  tamanhomsg));

	}

	@Operation(summary = "Verificar se a empresa possui token SMS configurado",
			description = "Verifica se a empresa possui o token sms configurado no cadastro da empresa")
	@LogExecution
	@GetMapping(value = "/pacote-sms")
	public ResponseEntity<?> possuiSms () {
		return super.finish(hcService.possuiSms());
	}

	@Operation(summary = "Criar um contato  do tipo agendamento ",
			description = "Agenda um contato de ligação ou presencial e salva o historico de contato.")
	@LogExecution
	@PostMapping(value = "/agendamento", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	@ResponseStatus(HttpStatus.CREATED)
	public ResponseEntity<?> agenda (@Valid @RequestBody AgendaDTO dto) {
		return super.finish(hcService.salvarAgedamento(dto));
	}


	@LogExecution
	@ResponseStatus(HttpStatus.CREATED)
	@Operation(summary = "Criar um agendamento do tipo ligaçao ", description = "Agenda um contato de ligação.")
	@PostMapping(value = "/agendamento/ligacao", consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
	public ResponseEntity<HistoricoContatoVO> agendaLigacao (@Valid @RequestBody AgendaLigacaoDTO dto) {
		return (ResponseEntity<HistoricoContatoVO>) super.finish(hcService.salvarAgendamentoLigacao(dto));
	}

	// Enuns temporariamente nessa class
	@Operation(summary = "Busca as fases do crm",
			description = "Busca as fases do crm ")
	@LogExecution
	@GetMapping("/fase-crm")
	public ResponseEntity<?> buscarPorFase() {
		JSONObject filter = requestService.getCurrentConfiguration().getFilters();

		if (Objects.nonNull(filter) && filter.has("quicksearchValue") && !filter.get("quicksearchValue").toString().equals("null")){
			return super.finish(getListFaseCRM( filter.getString("quicksearchValue")));
		}

		return super.finish(getListFaseCRM(""));
	}

	@Operation(summary = "Busca os tipos de agendamentos",
			description = "Busca tipos de agendamento do crm ")
	@LogExecution
	@GetMapping("/tipo-agendamento")
	public ResponseEntity<?> buscarPorTipoAgendamento() {
		JSONObject filter = requestService.getCurrentConfiguration().getFilters();

		if (Objects.nonNull(filter) && filter.has("quicksearchValue") && !filter.get("quicksearchValue").toString().equals("null")){
			return super.finish(getListTipoAgendamentoApresentarEnum( filter.getString("quicksearchValue")));
		}

		return super.finish(getListTipoAgendamentoApresentarEnum(""));
	}
}
