package br.com.pacto.ms.contato.avulso.data.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;
import java.io.Serializable;

@DynamicUpdate
@DynamicInsert
@Data
@NoArgsConstructor
@Entity
@Table(name = "configtotalpass", schema = "public")
public class ConfigTotalPassEntity  implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    private String nome;

    @Column(name = "empresa_codigo")
    private Integer empresaCodigo;

    private Boolean inativo;

    private String codigoTotalPass;

    private String apikey;
}
