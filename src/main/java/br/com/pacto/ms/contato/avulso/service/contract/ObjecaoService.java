package br.com.pacto.ms.contato.avulso.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.input.HistoricoObjecaoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.HistoricoContatoDTO;
import br.com.pacto.ms.contato.avulso.data.pojo.input.ObjecaoDTO;
import br.com.pactosolucoes.commons.data.PagingAndSortingData;

import java.util.List;

public interface ObjecaoService<T> {

    T salvar(ObjecaoDTO objecao);

    T atualizar(Integer id, ObjecaoDTO objecao);

    void excluir(Integer id);

    void excluir(List<Integer> id);

    T buscarPorId(Integer id);

    T buscarTodos(PagingAndSortingData pageAndSortingData);

    T buscarPorTipo(PagingAndSortingData pageAndSortingData, String tipo);

    T buscarSomenteAtivosPorTipo(PagingAndSortingData pageAndSortingData, String tipo);

    T buscarPorGrupo(PagingAndSortingData pageAndSortingData, String grupo);

    T buscarPorDescricao(PagingAndSortingData pageAndSortingData, String descricao);

    String buscarPorTipoReagendamento();

    String buscarPorTipoAgendamento();

    String buscarPorTipoOB(HistoricoContatoDTO historicocontato);

    String buscarPorTipoSR(String tipoContato);

    void salvarObjecaoDefinitiva(HistoricoObjecaoDTO dto);
}
