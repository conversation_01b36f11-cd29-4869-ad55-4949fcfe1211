package br.com.pacto.ms.contato.ia.service.impl.exceptions;

import br.com.pacto.ms.contato.avulso.service.exception.CadastroLeadException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;


@ControllerAdvice
public class ContextosExceptionHandler extends ResponseEntityExceptionHandler {

    @ExceptionHandler(ContextoException.class)
    public ResponseEntity<Map<String, Object>> handleClienteException(ContextoException ex) {
        Map<String, Object> body = new HashMap<>();
        body.put("timestamp", LocalDateTime.now());
        body.put("status", HttpStatus.INTERNAL_SERVER_ERROR.value());
        body.put("error", "Not Found");
        body.put("exception", ex.getClass().getName());
        body.put("path", "/contextos");
        body.put("cause", ex.getCause());
        body.put("message", ex.getMessage());
        return new ResponseEntity<>(body, HttpStatus.INTERNAL_SERVER_ERROR);
    }

}
