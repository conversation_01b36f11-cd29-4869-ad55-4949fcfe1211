package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

import br.com.pactosolucoes.commons.data.ModelData;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ClienteVO implements Serializable, ModelData {
	private static final long serialVersionUID = 1L;

	@JsonIgnore
	private Long size;

	private Integer codigo;

	private String agencia;

	private Integer agenciaamigofit;

	private String agenciadigito;

	private String anexo;

	private String banco;

	private Integer bancoamigofit;

	private Integer categoria;

	private String codacesso;

	private String codacessoalternativo;

	private Integer codamigofit;

	private Integer codigomatricula;

	private String conta;

	private Integer contaamigofit;

	private Boolean contacorrenteamigofit;

	private String contadigito;

	private Boolean contapoupancaamigofit;

	private Date datacadastroanexo;

	private Date datavalidadecarteirinha;

	private Integer digitoagenciaamigofit;

	private Integer digitocontaamigofit;

	private Integer freepass;

	private String gympasstypenumber;

	private String gympassuniquetoken;

	private String identificadorparacobranca;

	private String matricula;

	private Long matriculaexterna;

	private String matriculasesc;

	private String nivelmgb;

	private String nomeanexo;

	private String nomesocial;

	private Boolean parqpositivo;

	private Integer pessoa;

	private Integer pessoaresponsavel;

	private BigDecimal porcentagemdescontoboletopagantecipado;

	private String publicidmgb;

	private double renda;

	private String senhausuarioamigofit;

	private Boolean sesc;

	private String situacao;

	private Integer situacaoclube;

	private Boolean temmaisdeumadigital;

	private Integer uacodigo;

	private String usernameamigofit;

	private Integer usuarioverificacao;

	private Boolean utilizarresponsavelpagamento;

	private Timestamp validadecartaosesc;

	private Timestamp verificadoem;

	private Boolean verificarcliente;

	private List<AgendaVO> agendas;

	private ClienteVO cliente;

	private List<ClienteVO> clientes;

	private EmpresaVO empresaBean;

	private ObjecaoVO objecaoBean;

	private UsuarioVO usuario;

	private List<FecharmetadetalhadoVO> fecharmetadetalhados;

	private List<HistoricoContatoVO> historicocontatos;

	private List<IndicadoVO> indicados;

	private List<PassivoVO> passivos;

	private List<UsuarioVO> usuarios;

	private List<VinculoVO> vinculos;
}
