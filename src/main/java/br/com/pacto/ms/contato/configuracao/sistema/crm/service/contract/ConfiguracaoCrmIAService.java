package br.com.pacto.ms.contato.configuracao.sistema.crm.service.contract;

import br.com.pacto.ms.contato.avulso.data.pojo.output.MaladiretaVO;
import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.*;
import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.ia.data.pojo.input.InserirPdfRequestVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.InserirPdfResponseVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.StatusInstanceVO;

import java.util.List;

public interface ConfiguracaoCrmIAService<T, DTO> {

    List<ConfiguracaoCrmFaseIADTO> consultar(FasesCRMEnum fase, Integer idEmpresa);

    List<ConfiguracaoCrmFaseIADTO> consultar(Integer codigoEmpresa);

    List<ConfiguracaoCrmFaseIADTO> consultarFases(Integer idEmpresa);

    ConfiguracaoCrmIADTO consultarConfiguracao(Integer idEmpresa);

    List<ConfiguracaoCrmIAPosVendaDTO> consultarConfigPosVenda();

    T incluirFases(List<ConfiguracaoCrmFaseIADTO> configuracoesCrmFaseIADTO, Integer codigoEmpresa);

    T incluirConfiguracaoDeFases(List<ConfiguracaoCrmFaseIADTO> fasesRecebidas, Integer codigoEmpresa);

    void excluir(Integer codigo);

    T alterar(Integer codigo, ConfiguracaoCrmFaseIADTO configuracaoCrmFaseIADTO);

    T incluirConfiguracao(ConfiguracaoCrmIADTO configuracaoCrmIADTO);

    T deletarPDF(Integer empresa);

    T incluirPDFConversas(InserirPdfRequestVO configuracaoCrmIADTO);

    T consultarPDFConversas(Integer empresa);

    T incluirConfiguracaoRede(ConfiguracaoRedeIADTO configuracaoCrmIADTO);

    T incluirPosVenda(List<ConfiguracaoCrmIAPosVendaDTO> ConfiguracaoCrmIAPosVendaDTO);

    T obterQrCode(String idInstancia, String token);

    T desconectarInstancia(Integer codigoEmpresa);

    T criarInstancia(ConfiguracaoInstanciaDTO configuracaoInstanciaDTO);

    T subscreverInstancia(String instancia, String token);

    Boolean verificarStatusInstancia(String instanciaId, String token);

    T cancelarInstancia(String instancia, String token, Integer codigoEmpresa);

    T obterInstanciaEmpresa(String chaveEmpresa);

    List<MaladiretaVO> consultarFaseExtrasIA(Integer codigoEmpresa);

    StatusInstanceVO obterInformacoesDispositivoConectado(String instanceId, String token);

    List<ResumoEmpresaVO> buscarUnidadesDaEmpresaPorBanco(String empresa);
}
