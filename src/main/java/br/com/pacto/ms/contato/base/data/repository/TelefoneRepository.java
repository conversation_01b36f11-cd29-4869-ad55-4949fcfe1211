package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.TelefoneDetalhe;
import br.com.pacto.ms.contato.base.data.domain.TelefoneEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import java.util.List;
import java.util.Optional;

public interface TelefoneRepository extends PagingAndSortingRepository<TelefoneEntity, Integer> {

    @Query(  " select t  "
            + " from TelefoneEntity t "
            + "		join ClientEntity c ON t.pessoa = c.pessoa"
            + " where c.codigomatricula = :matricula and t.tipotelefone = :tipoTelefone  ")
    Optional<List<TelefoneEntity>> buscarPorMatriculaTipo(int matricula, String tipoTelefone);

    @Query(  " select t  "
            + " from TelefoneEntity t "
            + "		join ClientEntity c ON t.pessoa = c.pessoa"
            + " where c.codigomatricula = :matricula  ")
    Optional<List<TelefoneEntity>> buscarPorMatricula(int matricula);

    @Query(  " select new br.com.pacto.ms.contato.base.data.domain.TelefoneDetalhe (p.nome, t.numero)  "
            + " from TelefoneEntity t "
            + "		join ClientEntity c ON t.pessoa = c.pessoa"
            + "     join PessoaEntity p on t.pessoa = p.codigo "
            + " where c.codigo = :cliente and t.tipotelefone = :tipoTelefone  and t.recebersms = true")
    Optional<List<TelefoneDetalhe>> buscarPorClienteAndRecebersms(int cliente, String tipoTelefone);

    @Query(  " select new br.com.pacto.ms.contato.base.data.domain.TelefoneDetalhe (p.nome, t.numero)  "
            + " from TelefoneEntity t "
            + "		join ClientEntity c ON t.pessoa = c.pessoa"
            + "     join PessoaEntity p on t.pessoa = p.codigo "
            + " where c.codigo = :cliente and t.tipotelefone = :tipoTelefone")
    Optional<List<TelefoneDetalhe>> buscarPorClienteAndTipoTelefone(int cliente, String tipoTelefone);

    @Query(value = "select true", nativeQuery = true)
    boolean healthCheckConnection();

    @Query("SELECT t FROM TelefoneEntity t WHERE t.pessoa = :codigo order by t.codigo ")
    List<TelefoneEntity> consultarTelefones(Integer codigo);
}
