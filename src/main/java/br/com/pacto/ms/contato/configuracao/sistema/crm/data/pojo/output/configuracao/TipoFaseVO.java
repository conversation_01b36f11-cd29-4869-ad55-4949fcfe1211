package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TipoFaseVO {
    @Schema(description = "Código do tipo de fase", example = "1")
    private int codigo;
    @Schema(description = "Nome do tipo de fase", example = "VENDAS")
    private String descricao;
    @Schema(description = "Relacionamento de Fidelização", example = "Fidelização")
    private String descricaoCurta;
}
