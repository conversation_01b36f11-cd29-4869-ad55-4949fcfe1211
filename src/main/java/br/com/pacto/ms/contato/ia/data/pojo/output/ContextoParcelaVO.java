package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.pojo.output.SituacaoParcelaVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Digits;
import java.time.LocalDate;
import java.util.Date;

@Data
public class ContextoParcelaVO {

    @Schema(description = "Código da parcela, não nulo.")
    private Long codigo;

    @Schema(description = "Descrição da parcela.")
    private String descricao;

    @Schema(description = "Valor da parcela, não nulo.")
    private Double valorParcela;

    @Schema(description = "Situação da parcela, não nula.")
    private SituacaoParcelaVO situacao;

    @Schema(description = "Data de vencimento da parcela, não nula.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Date datavencimento;
}
