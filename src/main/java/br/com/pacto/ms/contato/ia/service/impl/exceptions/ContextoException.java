package br.com.pacto.ms.contato.ia.service.impl.exceptions;

import br.com.pactosolucoes.commons.exception.CustomException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
public class ContextoException extends CustomException {

    public ContextoException(String message) {
        super(message);
    }

}
