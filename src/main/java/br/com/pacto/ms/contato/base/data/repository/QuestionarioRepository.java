package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.QuestionarioEntity;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.List;
import java.util.Optional;

public interface QuestionarioRepository extends PagingAndSortingRepository<QuestionarioEntity, Integer> {

    Optional<List<QuestionarioEntity>> findQuestionarioEntitiesByTipoquestionarioAndNomeinterno(String tipo, String nome);
    Optional<List<QuestionarioEntity>> findQuestionarioEntitiesByTipoquestionario(String tipo);
}
