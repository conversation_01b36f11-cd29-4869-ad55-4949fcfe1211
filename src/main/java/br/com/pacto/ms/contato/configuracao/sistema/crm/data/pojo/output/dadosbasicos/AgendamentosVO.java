package br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.dadosbasicos;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Range;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgendamentosVO {

    @Range(min = 1, max = 30)
    private Integer nrdiaslimiteagendamentofuturo;
}
