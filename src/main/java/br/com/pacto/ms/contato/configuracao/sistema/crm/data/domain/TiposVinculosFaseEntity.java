package br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain;

import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoColaboradorEnum;
import br.com.pacto.ms.contato.log.data.listener.LogListener;
import br.com.pactosolucoes.commons.util.annotation.LogTagTransform;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;

import javax.persistence.*;

@DynamicUpdate
@DynamicInsert
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "tiposvinculosfase", schema = "public")
public class TiposVinculosFaseEntity {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Id
    @Column(name = "codigo", nullable = false)
    private Integer codigo;
    @Column(name = "fase", nullable = true)
    private FasesCRMEnum fase;
    private TipoColaboradorEnum tipoColaborador;

}
