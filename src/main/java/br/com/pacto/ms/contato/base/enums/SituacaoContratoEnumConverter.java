package br.com.pacto.ms.contato.base.enums;

import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class SituacaoContratoEnumConverter implements AttributeConverter<SituacaoContratoEnum, String> {

    @Override
    public String convertToDatabaseColumn(SituacaoContratoEnum situacaoContratoEnum) {
        if(situacaoContratoEnum == null){
            return null;
        }
        return situacaoContratoEnum.getCodigo();
    }

    @Override
    public SituacaoContratoEnum convertToEntityAttribute(String id) {
        if (!StringUtils.hasText(id)) {
            return null;
        }
        return SituacaoContratoEnum.findByCodigo(id);
    }
}
