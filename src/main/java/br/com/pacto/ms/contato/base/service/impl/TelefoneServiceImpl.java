package br.com.pacto.ms.contato.base.service.impl;

import br.com.pacto.ms.contato.base.data.domain.TelefoneDetalhe;
import br.com.pacto.ms.contato.base.data.domain.TelefoneEntity;
import br.com.pacto.ms.contato.base.data.pojo.output.TelefoneVO;
import br.com.pacto.ms.contato.base.data.repository.TelefoneRepository;
import br.com.pacto.ms.contato.base.service.contract.TelefoneService;
import br.com.pacto.ms.contato.base.service.exception.TelefoneException;
import br.com.pacto.ms.contato.core.data.pojo.enums.TipoTelefone;
import br.com.pactosolucoes.commons.util.OptionalUtils;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@AllArgsConstructor
public @Service class TelefoneServiceImpl <T> implements TelefoneService {
    private TelefoneRepository repository;

    @Override
    @LogExecution
    @ObjectMapper(TelefoneVO.class)
    public T buscarTelefonePorTipo(int matricula, String tipoContato) {
        return (T) new OptionalUtils<List<TelefoneEntity>>().orElseThrow(repository.buscarPorMatriculaTipo(matricula, tipoContato), TelefoneException::new);
    }


    @Override
    @LogExecution
    @ObjectMapper(TelefoneDetalhe.class)
    public T buscarPorClienteTipoAndRecebersms(int cliente, String tipoContato) {
        return (T) new OptionalUtils<List<TelefoneDetalhe>>().orElseThrow(repository.buscarPorClienteAndRecebersms(cliente, tipoContato), TelefoneException::new);
    }

    @Override
    @LogExecution
    @ObjectMapper(TelefoneDetalhe.class)
    public T buscarPorClienteAndTipoTelefone(int cliente, String tipoContato) {
        return (T) new OptionalUtils<List<TelefoneDetalhe>>().orElseThrow(repository.buscarPorClienteAndTipoTelefone(cliente, tipoContato), TelefoneException::new);
    }



    @Override
    @LogExecution
    @ObjectMapper(TelefoneVO.class)
    public T buscarTelefoneFixo(int matricula) {
        Optional<List<TelefoneEntity>> opt = repository.buscarPorMatriculaTipo(matricula, TipoTelefone.RESIDENCIAL.getCodigo());
        if (opt.isPresent()) {
            if (opt.get().size() > 0) {
                return (T) opt;
            }
        }
        opt =repository.buscarPorMatriculaTipo(matricula,TipoTelefone.RECADO.getCodigo());
        if(opt.isPresent()){
            if(opt.get().size()>0){
                return (T) opt;
            }
        }
        opt =repository.buscarPorMatriculaTipo(matricula,TipoTelefone.COMERCIAL.getCodigo());
        if(opt.isPresent()){
            if(opt.get().size()>0){
                return (T) opt;
            }
        }
        opt =repository.buscarPorMatriculaTipo(matricula, TipoTelefone.RECADO.getCodigo());
        if(opt.isPresent()){
            if(opt.get().size()>0){
                return (T) opt;
            }
        }

        return (T) opt;
    }

    public void persistirTelefonesClienteLead(Integer codigoPessoa,
                                              String celular,
                                              String teleFoneResidencial,
                                              String teleFoneTrabalho) {
        List<TelefoneEntity> existentes = repository.consultarTelefones(codigoPessoa);
        Set<String> telefonesExistentes = existentes.stream()
                .map(TelefoneEntity::getNumero)
                .collect(Collectors.toSet());

        salvarTelefones(codigoPessoa, celular, "CE", telefonesExistentes);
        salvarTelefones(codigoPessoa, teleFoneResidencial, "RE", telefonesExistentes);
        salvarTelefones(codigoPessoa, teleFoneTrabalho, "RE", telefonesExistentes);
    }

    private void salvarTelefones(Integer codigoPessoa, String numero, String tipoTelefone, Set<String> telefonesExistentes) {
        if (numero != null && !numero.isEmpty() && !telefonesExistentes.contains(numero)) {
            TelefoneEntity telefone = new TelefoneEntity();
            telefone.setNumero(numero);
            telefone.setPessoa(codigoPessoa);
            telefone.setTipotelefone(tipoTelefone);
            repository.save(telefone);
        }
    }
}
