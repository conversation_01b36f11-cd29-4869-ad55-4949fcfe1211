package br.com.pacto.ms.contato.ia.data.pojo.output;

import br.com.pacto.ms.contato.base.data.pojo.output.SituacaoContratoVO;
import br.com.pacto.ms.contato.base.enums.SituacaoContratoEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.sql.Timestamp;

@Data
public class ContextoContratoVO {
    @Schema(description = "Código do contrato.")
    private Integer codigo;

    @Schema(description = "Indica se o contrato possui bolsa.")
    private Boolean bolsa;

    @Schema(description = "Nome das modalidades do contrato.")
    private String nomemodalidades;

    @Schema(description = "Data de matrícula.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp datamatricula;

    @Schema(description = "Data prevista para renovação.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp dataprevistarenovar;

    @Schema(description = "Data de renovação realizada.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp datarenovarrealizada;

    @Schema(description = "Data prevista para rematrícula.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp dataprevistarematricula;

    @Schema(description = "Data de rematrícula realizada.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp datarematricularealizada;

    @Schema(description = "Observações sobre o contrato.")
    private String observacao;

    @Schema(description = "Valor final do contrato.")
    private Float valorfinal;

    @Schema(description = "Data de vigência até.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp vigenciaate;

    @Schema(description = "Data ajustada de vigência até.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp vigenciaateajustada;

    @Schema(description = "Data de vigência de.")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy HH:mm:ss")
    private Timestamp vigenciade;

    @Schema(description = "Situação do contrato.")
    private SituacaoContratoVO situacaocontrato;

    @Schema(description = "Situação")
    private SituacaoContratoVO situacao;

    @Schema(description = "Plano relacionado ao contrato.")
    private ContextoPlanoVO plano;

    @Schema(description = "Indica se o contrato é renovável automaticamente.")
    private Boolean renovavelautomaticamente;

    @Schema(description = "Indica se há venda de crédito treino.")
    private Boolean vendacreditotreino;
}