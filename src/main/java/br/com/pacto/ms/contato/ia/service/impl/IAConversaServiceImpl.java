package br.com.pacto.ms.contato.ia.service.impl;

import br.com.pacto.ms.contato.avulso.data.pojo.output.ResumoEmpresaVO;
import br.com.pacto.ms.contato.avulso.data.repository.EmpresaRepository;
import br.com.pacto.ms.contato.configuracao.sistema.commons.service.exception.ConfiguracaoIAException;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.MensagemEnviadaEntity;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.output.configuracao.ConfiguracaoCrmFaseIAVO;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.MensagemEnviadaEntityRepository;
import br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl.ConfiguracaoCrmFaseIAServiceImp;
import br.com.pacto.ms.contato.ia.data.pojo.output.CodigosAlunoPaginaVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.IAConversaServiceVO;
import br.com.pacto.ms.contato.ia.data.pojo.output.ResponsePactoConversasVO;
import br.com.pacto.ms.contato.ia.service.contract.ContextoService;
import br.com.pacto.ms.contato.ia.service.contract.ConversaService;
import br.com.pacto.ms.contato.ia.service.contract.IAConversaService;
import br.com.pacto.ms.contato.ia.service.impl.exceptions.ContextoException;
import br.com.pactosolucoes.commons.data.PagingAndSortingData;
import br.com.pactosolucoes.commons.data.domain.OAMDCompany;
import br.com.pactosolucoes.commons.data.repository.OAMDCompanyRepository;
import br.com.pactosolucoes.commons.exception.DataValidateException;
import org.jetbrains.annotations.Nullable;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.ExceptionHandler;

import java.sql.Date;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class IAConversaServiceImpl implements IAConversaService {

    @Autowired
    private ContextoService contextoService;
    @Autowired
    private ConversaService conversaService;
    @Autowired
    OAMDCompanyRepository companyRepository;
    @Autowired
    private ConfiguracaoCrmIARepository configuracaoCrmIARepository;
    @Autowired
    private EmpresaRepository empresaRepository;
    @Autowired
    private ConfiguracaoCrmFaseIAServiceImp configuracaoCrmFaseIAServiceImp;
    @Autowired
    private MensagemEnviadaEntityRepository mensagemEnviadaEntityRepository;

    private static final Logger log = LoggerFactory.getLogger(IAConversaServiceImpl.class);

    @Override
    @ExceptionHandler(ContextoException.class)
    public ResponsePactoConversasVO<IAConversaServiceVO> inciarConversaPorFase(String chave, List<String> fasesFiltradas, boolean forcarEnvio, Integer empresa) {
        IAConversaServiceVO iaConversaServiceVO = IAConversaServiceVO.builder().configuracaoIaHabilitada(true).moduloCrmHabilitado(true).moduloZwHabilitado(true).configuracaoIaHabilitada(true).build();
        ResponsePactoConversasVO<IAConversaServiceVO> responsePactoConversasVO = new ResponsePactoConversasVO<>("O processo foi iniciado.", iaConversaServiceVO);

        Instant startTime = Instant.now();
        log.info("Início do processamento para envio de mensagens Pacto Conversa");

        Optional<OAMDCompany> oamdCompany = this.companyRepository.findById(chave);
        if (!oamdCompany.isPresent())
            throw new DataValidateException("Cliente com chave: " + chave + " não encontrado");

        log.info("Início para empresa:  " + chave);

        if (configuracaoCrmIARepository.findAllActivatedOptional().get().isEmpty()) {
            return new ResponsePactoConversasVO<>("Não foi encontrada Configuracao Crm IA ativa para a chave: " + chave + ". Para e empresa: " + oamdCompany.get().getIdentificadorempresa(), IAConversaServiceVO.builder().build());
        }

        if (isModulosObrigatorios(oamdCompany)) {
            log.warn("A empresa: " + oamdCompany.get().getIdentificadorempresa() + " não possui os módulos necessários para o processamento (ADM, CRM, IA). Pulando para a próxima empresa.");
            throw new DataValidateException("A empresa: " + oamdCompany.get().getIdentificadorempresa() + " não possui os módulos necessários para o processamento (ADM, CRM, IA). Pulando para a próxima empresa.");
        }

        if (fasesFiltradas == null || fasesFiltradas.isEmpty()) {
            throw new DataValidateException("Nenhuma fase para iniciar a conversa");
        }
        List<ResumoEmpresaVO> empresaVOList = empresaRepository.consultarEmpresasAtivas();
        if(empresa != null) {
            empresaVOList.removeIf(empresaVO -> !empresa.equals(empresaVO.getCodigo()));
        }
        empresaVOList.forEach(unidadesDaEmpresa -> {
            try {
                List<ConfiguracaoCrmFaseIAVO> configuracaoCrmFasedIAEntities =
                        this.configuracaoCrmFaseIAServiceImp.listarPorFasesEMetaExtras(fasesFiltradas, unidadesDaEmpresa.getCodigo());

                Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities = this.configuracaoCrmIARepository.obterPorCodigoEmpresa(unidadesDaEmpresa.getCodigo());
                if (existeConfiguracaoIA(configuracaoCrmIAEntities)) {
                    log.info("A empresa de código " + unidadesDaEmpresa.getCodigo() + " não possui configuração de CRM IA. Pulando para a próxima empresa.");
                    return;
                }

                log.info("Processando envio de contextos da empresa de código: " + unidadesDaEmpresa.getCodigo());
                this.contextoService.atualizarContextoPlanos(unidadesDaEmpresa.getCodigo());
                this.contextoService.atualizarContextoTurmas(unidadesDaEmpresa.getCodigo());
                this.contextoService.atualizarContextoFases(unidadesDaEmpresa.getCodigo());
                this.contextoService.atualizarContextoPersonalidade(unidadesDaEmpresa.getCodigo());
                this.contextoService.atualizarContextoProdutos(unidadesDaEmpresa.getCodigo());

                log.info("Finalizado envio de contextos da empresa de código: " + unidadesDaEmpresa.getCodigo());
                log.info("Processando envio de mensagens por fase do CRM da empresa" + unidadesDaEmpresa.getCodigo() +
                        ". " + configuracaoCrmFasedIAEntities.size() + " estão configuradas para envio de mensagens.");

                configuracaoCrmFasedIAEntities.forEach(configuracaoCrmFasedIAEntity -> {
                    mandarMensagem(configuracaoCrmFasedIAEntity.toEntity(), unidadesDaEmpresa, forcarEnvio);
                });
            } catch (Exception e) {
                log.error("Erro ao enviar mensagem para clientes da empresa com código " + unidadesDaEmpresa.getCodigo() + ". Erro: " + e.getMessage(), e);
                throw new ConfiguracaoIAException("Erro ao enviar mensagem " + e.getMessage());
            }
        });

        Instant endTime = Instant.now();
        Duration tempoProcessamentoTotal = Duration.between(startTime, endTime);
        log.debug("Fim do processamento envio para envio de mensagens Pacto Conversa. Tempo de processamento: {} ms", tempoProcessamentoTotal.toMillis());

        return responsePactoConversasVO;
    }

    private static boolean existeConfiguracaoIA(Optional<List<ConfiguracaoCrmIAEntity>> configuracaoCrmIAEntities) {
        return !configuracaoCrmIAEntities.isPresent() || configuracaoCrmIAEntities.get().isEmpty() || configuracaoCrmIAEntities.get().size() < 1;
    }

    private static boolean isModulosObrigatorios(Optional<OAMDCompany> oamdCompany) {
        return !oamdCompany.get().getModulos().contains("ZW") ||
                !oamdCompany.get().getModulos().contains("CRM") ||
                !oamdCompany.get().getModulos().contains("IA");
    }

    public void mandarMensagem(ConfiguracaoCrmFaseIAEntity configuracaoCrmFaseIAEntity, ResumoEmpresaVO resumoEmpresaVO, boolean forcarEnvio) {
        Instant startTimeEmpresa = Instant.now();
        log.info("Processamento de conversa via whats para empresa: " + resumoEmpresaVO.getNome());

        // Inicializa a página e o tamanho da paginação
        int page = 0;
        int size = 10;
        int totalRegistros = 0;

        do {
            // Faz a chamada para obter os registros da página atual
            PagingAndSortingData pagingAndSortingData = PagingAndSortingData.builder()
                    .page(page)
                    .size(size)
                    .orderBy("codigo".split(","))
                    .build();

            CodigosAlunoPaginaVO codigosAlunoPaginaVO = this.contextoService.consultarCodigosAlunosPorFaseComPagina(resumoEmpresaVO.getCodigo(), configuracaoCrmFaseIAEntity.getFase(), configuracaoCrmFaseIAEntity.getCodigoMetaExtra(), pagingAndSortingData.get());

            log.info("Total de alunos na fase " + (configuracaoCrmFaseIAEntity.getFase() != null ? configuracaoCrmFaseIAEntity.getFase().getName() : "") + ":  " + codigosAlunoPaginaVO.getTotalRegistros());

            Optional<MensagemEnviadaEntity> mensagemEnviadaEntity = mensagemEnviadaEntityRepository
                    .buscarPorEmpresaOuChaveNoDiaDeHoje(
                            resumoEmpresaVO.getCodigo(),
                            Date.valueOf(LocalDate.now()),
                            configuracaoCrmFaseIAEntity.getFase() != null ? configuracaoCrmFaseIAEntity.getFase().getName() : null,
                            configuracaoCrmFaseIAEntity.getCodigoMetaExtra()
                    );

            List<Integer> codigosCliente = obterCodigosDeClientesParaEnvio(forcarEnvio, mensagemEnviadaEntity, codigosAlunoPaginaVO);
            if (codigosCliente == null) break;

            // Processa os códigos de alunos obtidos
            iniciarConversaPorAluno(
                    codigosCliente,
                    configuracaoCrmFaseIAEntity,
                    resumoEmpresaVO
            );

            totalRegistros = codigosAlunoPaginaVO.getTotalRegistros();
            page++;
        } while (page * size < totalRegistros);
        Instant endTimeEmpresa = Instant.now();
        Duration tempoProcessamentoEmpresa = Duration.between(startTimeEmpresa, endTimeEmpresa);
        log.info("Processamento de conversa via WhatsApp para empresa: " + resumoEmpresaVO.getNome() + ". Tempo de processamento: {} ms", tempoProcessamentoEmpresa.toMillis());
    }

    @Nullable
    private static List<Integer> obterCodigosDeClientesParaEnvio(boolean forcarEnvio, Optional<MensagemEnviadaEntity> mensagemEnviadaEntity, CodigosAlunoPaginaVO codigosAlunoPaginaVO) {
        List<Integer> codigosCliente;
        if (mensagemEnviadaEntity.isPresent() && !forcarEnvio) {
            codigosCliente = codigosAlunoPaginaVO.getListaCodigoClientes()
                    .stream()
                    .filter(codigoAluno -> !mensagemEnviadaEntity.get().getCodigosCliente().contains(codigoAluno))
                    .collect(Collectors.toList());
            Set<Integer> uniqueCodes = new HashSet<>(mensagemEnviadaEntity.get().getCodigosCliente());
            String uniqueCodesString = uniqueCodes.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            log.info("Mensagens já foram enviadas para os clientes com este códigos, e por isso não serão enviadas novamente hoje: " + uniqueCodesString);
        } else {
            codigosCliente = codigosAlunoPaginaVO.getListaCodigoClientes();
        }

        if (codigosCliente.isEmpty()) {
            return null;
        }
        return codigosCliente;
    }

    private void iniciarConversaPorAluno(
            List<Integer> codigosAlunos,
            ConfiguracaoCrmFaseIAEntity configuracaoFase,
            ResumoEmpresaVO resumoEmpresaVO) {

        String nomeMetaExtra = configuracaoFase.getNomeMetaExtra();
        Integer codigoMetaExtra = configuracaoFase.getCodigoMetaExtra();
        String fase = configuracaoFase.getFase() != null ? configuracaoFase.getFase().getName() : null;

        for (Integer codigoAluno : codigosAlunos) {
            log.info("Iniciando conversa para o cliente de código " + codigoAluno);
            conversaService.inciarConversa(
                    codigoAluno,
                    resumoEmpresaVO,
                    fase,
                    nomeMetaExtra,
                    codigoMetaExtra
            );
        }
    }

}
