package br.com.pacto.ms.contato.base.data.pojo.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MailingAgendamentoDTO {
    private int codigo;
    private Integer ocorrencia;
    private String cron;
    private Timestamp ultimaexecucao;
    private Timestamp datainicial;
    private int maladireta;
}
