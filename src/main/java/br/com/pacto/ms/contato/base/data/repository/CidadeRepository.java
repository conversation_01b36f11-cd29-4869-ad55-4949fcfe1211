package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.CidadeEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.util.Optional;

public interface CidadeRepository extends PagingAndSortingRepository<CidadeEntity, Integer> {

    @Query("select c.nome,c.estado.descricao from CidadeEntity c where c.codigo = :codigo")
    Optional<String> buscaNomeCidadeEEstadoPorCodigo(Integer codigo);

}
