package br.com.pacto.ms.contato.avulso.service.impl;

import br.com.pacto.ms.contato.avulso.data.pojo.output.EmpresaVO;
import br.com.pacto.ms.contato.avulso.data.repository.EmpresaRepository;
import br.com.pacto.ms.contato.avulso.service.contract.EmpresaService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import br.com.pactosolucoes.commons.util.annotation.ObjectMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;


@AllArgsConstructor
public @Service class EmpresaServiceImpl <T> implements EmpresaService {

    private EmpresaRepository repository;


    @Override
    @LogExecution
    @ObjectMapper(EmpresaVO.class)
    public T findByCodigo(Integer codigo) {
        return (T) repository.findById(codigo);
    }
}
