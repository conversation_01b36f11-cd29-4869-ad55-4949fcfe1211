package br.com.pacto.ms.contato.base.data.domain;


import lombok.Data;

import javax.persistence.*;
import java.util.List;

@Entity
@Data
@Table(name = "composicao", schema = "public")
public class ComposicaoEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "codigo")
    private Integer codigo;

    @Column(name = "composicaodefault")
    private Boolean composicaodefault;

    @Column(name = "composicaoadicional")
    private Boolean composicaoadicional;

    @Column(name = "precocomposicao")
    private Float precocomposicao;

    @Column(name = "descricao", length = 45, nullable = false)
    private String descricao;

    @Column(name = "empresa")
    private Integer empresa;

    @Column(name = "modalidadesespecificas", columnDefinition = "boolean default true")
    private Boolean modalidadesespecificas;

    @Column(name = "qtdemodalidades")
    private Short qtdemodalidades;

    @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
    @JoinColumn(name = "composicao")
    private List<ComposicaoModalidadeEntity> modalidades;

}
