package br.com.pacto.ms.contato.configuracao.sistema.empresa.service.impl;


import java.sql.Timestamp;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Service;

import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.domain.FeriadoEntity;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.pojo.input.EmpresaDTO;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.data.repository.FeriadoRepository;
import br.com.pacto.ms.contato.configuracao.sistema.empresa.service.contract.FeriadoService;
import br.com.pactosolucoes.commons.util.annotation.LogExecution;
import lombok.AllArgsConstructor;

import static br.com.pactosolucoes.utils.DateUtils.compareTo;

@Service
@AllArgsConstructor
public  class FeriadoServiceImpl<T> implements FeriadoService<T> {

    private FeriadoRepository feriadoRepository;

    @Override
    @LogExecution
    public boolean validarFeriadoPorEmpresaParaCalculoAberturaMeta(EmpresaDTO empresaDTO, Date dia) {
        Optional<List<FeriadoEntity>> ls =  feriadoRepository.findByDia(new Timestamp(dia.getTime()));
        if(ls.isPresent()) {
            Iterator e = ls.get().iterator();
            while (e.hasNext()) {
                FeriadoEntity feriado = (FeriadoEntity) e.next();
                if(validarFeriadoEmpresa(feriado, empresaDTO, dia)){
                    return true;
                }
            }
        }
        return false;
    }
    private boolean validarFeriadoEmpresa(FeriadoEntity feriado, EmpresaDTO empresa, Date dia) {
        if (feriado.getNacional()
                && feriado.getPais().equals(empresa.getPais())
                && !feriado.getNaorecorrente()) {
            return true;

        } else if (feriado.getNacional()
                && feriado.getPais().equals(empresa.getPais())
                && feriado.getNaorecorrente()
                && compareTo(feriado.getDia(), dia) == 0) {
            return true;

        } else if (feriado.getEstadual()
                && feriado.getEstado().equals(empresa.getEstado())
                && !feriado.getNaorecorrente()) {
            return true;

        } else if (feriado.getEstadual()
                && feriado.getEstado().equals(empresa.getEstado())
                && feriado.getNaorecorrente()
                && compareTo(feriado.getDia(), dia) == 0) {
            return true;

        } else if (!feriado.getNacional()
                && !feriado.getEstadual()
                && feriado.getCidade().equals(empresa.getCidade())
                && !feriado.getNaorecorrente()) {
            return true;

        } else if (!feriado.getNacional()
                && !feriado.getEstadual()
                && feriado.getCidade().equals(empresa.getCidade())
                && feriado.getNaorecorrente() && compareTo(feriado.getDia(), dia) == 0) {
            return true;
        }
        return false;
    }
}
