package br.com.pacto.ms.contato.base.data.repository;

import br.com.pacto.ms.contato.base.data.domain.TurmaEntity;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TurmaRepository extends PagingAndSortingRepository<TurmaEntity, Integer> {

    @Query("select t from TurmaEntity t where t.empresa = :empresa")
    Optional<List<TurmaEntity>> consultarPorEmpresa(@Param("empresa") Integer empresa);

    @Query("select t from TurmaEntity t where t.empresa = :empresa and DATE(current_date) between DATE(t.datainicialvigencia) and DATE(t.datafinalvigencia)")
    Optional<List<TurmaEntity>> consultarTurmasAtivasPorEmpresa(@Param("empresa") Integer empresa);
}