package br.com.pacto.ms.contato.avulso.data.pojo.output;

import java.io.Serializable;
import java.sql.Timestamp;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FeriadoVO implements Serializable {
	private static final long serialVersionUID = 1L;

	private Integer codigo;

	private Integer cidade;

	private String descricao;

	private Timestamp dia;

	private Integer estado;

	private Boolean estadual;

	private String mes;

	private Boolean nacional;

	private Boolean naorecorrente;

	private Integer pais;

}