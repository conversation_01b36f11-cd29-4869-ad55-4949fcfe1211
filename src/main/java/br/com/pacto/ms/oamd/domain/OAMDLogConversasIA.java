package br.com.pacto.ms.oamd.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "logconversasia", schema = "public")
public class OAMDLogConversasIA implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "chaveempresa", nullable = false)
    private String chaveEmpresa;

    @Column(name = "dia", nullable = false)
    private String dia;

    @Column(name = "hora", nullable = false)
    private LocalTime hora;

    @Column(name = "log", nullable = false)
    private String log;
}