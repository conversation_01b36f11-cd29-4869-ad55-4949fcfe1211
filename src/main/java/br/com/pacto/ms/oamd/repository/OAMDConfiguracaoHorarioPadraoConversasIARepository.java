package br.com.pacto.ms.oamd.repository;


import br.com.pacto.ms.oamd.domain.OAMDConfiguracaoHorarioPadraoConversasIA;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalTime;
import java.util.Optional;

public @Repository interface OAMDConfiguracaoHorarioPadraoConversasIARepository extends JpaRepository<OAMDConfiguracaoHorarioPadraoConversasIA, Integer> {

    @Query(value = "SELECT * FROM configuracaohorariopadraoconversasia WHERE hora = ?1", nativeQuery = true)
    Optional<OAMDConfiguracaoHorarioPadraoConversasIA> buscarPorHora(LocalTime hora);

}
