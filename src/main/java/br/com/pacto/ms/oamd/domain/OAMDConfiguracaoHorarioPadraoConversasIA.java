package br.com.pacto.ms.oamd.domain;


import br.com.pacto.ms.comuns.StringListConverter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "configuracaohorariopadraoconversasia", schema = "public")
public class OAMDConfiguracaoHorarioPadraoConversasIA implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer codigo;

    @Column(name = "hora", nullable = false)
    private LocalTime hora;

    @Column(name = "chaves", nullable = false, columnDefinition = "text")
    @Convert(converter = StringListConverter.class)
    private List<String> chaves;

}
