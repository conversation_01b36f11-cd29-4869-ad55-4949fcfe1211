package br.com.pacto.ms.comuns;

import br.com.pacto.ms.contato.avulso.web.handler.message.ExceptionMessage;
import br.com.pactosolucoes.commons.data.vo.ExceptionMessageVO;
import br.com.pactosolucoes.commons.exception.GenericException;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
 import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.util.Random;

public class Util {
    public static void validarHoraMinutos(String hora, ExceptionMessage mensagem) throws GenericException {

        if(hora.isEmpty()){
            throw new GenericException(new ExceptionMessageVO().message(mensagem));
        }
        int minutos = Integer.parseInt(hora.substring(3, 5));
        int horas = Integer.parseInt(hora.substring(0, 2));
        if (minutos > 60) {
            throw new GenericException(new ExceptionMessageVO().message(mensagem));
        }
        if (horas > 23) {
            throw new GenericException(new ExceptionMessageVO().message(mensagem));
        }
    }

    public static boolean emptyString(String s) {
        if (s == null) {
            return true;
        }
        if (s.equals("")) {
            return true;
        }
        return false;
    }


    public static String enviarSolicitacaoEncurtarLink(JSONObject json, String oAuthToken) throws Exception {

        HttpClient httpClient = HttpFactory.createConnector();
        HttpPost post = new HttpPost("https://api-ssl.bitly.com/v4/shorten");
        post.setHeader("Authorization", oAuthToken);
        post.setHeader("Content-Type", "application/json; charset=UTF-8");
        post.setHeader("Accept", "application/json");
        StringEntity entity = new StringEntity(json.toString(), "UTF-8");
        post.setEntity(entity);

         HttpResponse response = httpClient.execute(post);
         String responseString = EntityUtils.toString(response.getEntity(), "UTF-8");

        int statusCode = response.getStatusLine().getStatusCode();
        if (statusCode >= 200 && statusCode < 300) {
            JSONObject jsonObject = new JSONObject(responseString);
            if (jsonObject.has("link")) {
                return jsonObject.getString("link");
            } else {
                throw new Exception("Erro ao encurtar link: " + responseString);
            }
        } else {
             throw new Exception("Erro HTTP " + statusCode + ": " + responseString);
        }
    }
}
