package br.com.pacto.ms.comuns.data.pojo.output;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ProdutoVO {
    private Integer codigo;
    private Boolean desativado;
    private String tipoproduto;
    private Float valorbasecalculo;
    private Float valorfinal;
    private Integer nrdiasvigencia;
    private Date datafinalvigenciafixa;
    private Date datainiciovigencia;
    private String tipovigencia;
    private String descricao;
    private String urlVendaOnline;


    public ProdutoVO(Integer codigo, Float valorfinal, String descricao) {
        this.codigo = codigo;
        this.valorfinal = valorfinal;
        this.descricao = descricao;
    }
}
