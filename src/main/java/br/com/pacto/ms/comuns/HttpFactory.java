package br.com.pacto.ms.comuns;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.HttpClients;

import javax.net.ssl.*;
import java.security.SecureRandom;
import java.security.cert.CertificateException;

public class HttpFactory {

    public static CloseableHttpClient createConnector() {
        return createConnector(null);
    }

    public static CloseableHttpClient createConnector(RequestConfig requestConfig) {

        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                    }

                    @Override
                    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) throws CertificateException {
                    }

                    @Override
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return null;
                    }
                }
        };

        SSLContext sc = null;
        try {
            sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return HttpClients.custom().setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE).setDefaultRequestConfig(
                        requestConfig != null ? requestConfig : RequestConfig.DEFAULT).
                setSslcontext(sc).build();

    }
}
