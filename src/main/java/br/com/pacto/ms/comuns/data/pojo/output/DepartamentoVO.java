package br.com.pacto.ms.comuns.data.pojo.output;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.UUID;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class DepartamentoVO {
    private UUID id;
    private String name;
    private String descricao;

    public DepartamentoVO(String id, String name) {
        this.id = UUID.fromString(id);
        this.name = name;
    }
}
