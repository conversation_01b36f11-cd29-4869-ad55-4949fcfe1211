package br.com.pacto.ms.comuns.memcached;

import br.com.pacto.ms.comuns.Util;
import br.com.pacto.ms.comuns.memcached.interfaces.CachedManagerInterfaceFacade;
import br.com.pacto.ms.comuns.memcached.service.MemCachedService;
import net.spy.memcached.*;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;
import java.util.logging.Logger;


public final class MemCachedManager implements CachedManagerInterfaceFacade {

    private static MemCachedManager INSTANCE;
    private MemcachedClient memcachedClient;
    private final Set<String> pactoConversaAuthKeys = ConcurrentHashMap.newKeySet();

    private static final org.slf4j.Logger log = LoggerFactory.getLogger(MemCachedManager.class);

    public MemCachedManager() {
        instanciarMemcachedClient();
    }

    private void instanciarMemcachedClient() {
        try {
            String ipServidorMemcached = SpringContext.getBean(MemCachedService.class).getIpServidorMemcached();
            if (isServidorMemCachedNaoConfigurado(ipServidorMemcached)) {
                log.debug("SERVIDOR MEMCACHED NÃO CONFIGURADO. FAÇA A CONFIGURAÇÃO NO ARQUIVO DE CONFIGURAÇÃO.");
            } else {
                memcachedClient = new MemcachedClient(
                        new ConnectionFactoryBuilder().setDaemon(true).
                                setOpTimeout(5000).
                                setFailureMode(FailureMode.Retry).build(),
                        AddrUtil.getAddresses(ipServidorMemcached));
            }
        } catch (Exception e) {
            System.out.println(e);
            Logger.getLogger(MemCachedManager.class.getName()).log(Level.SEVERE, "Não foi possível carregar a configuração dos servidores menCached. Erro:" + e.getMessage(), e);
        }
    }

    public void remover(String classe, String identificador, String key) {
        try {
            if (this.memcachedClient != null) {
                String fullKey = getChaveComIdentificador(classe, identificador, key);
                this.memcachedClient.delete(fullKey);
                
                if ("PACTO_CONVERSA_AUTH_SERVICE_IMPL".equals(classe)) {
                    pactoConversaAuthKeys.remove(fullKey);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao remover objeto da MemCached", ex);
        }
    }

    public void gravar(String classe, int tempoExpirar, String identificador, String key, String valor) {
        try {
            if (this.memcachedClient != null && valor != null) {
                String fullKey = getChaveComIdentificador(classe, identificador, key);
                this.memcachedClient.set(fullKey, tempoExpirar, valor);
                
                if ("PACTO_CONVERSA_AUTH_SERVICE_IMPL".equals(classe)) {
                    pactoConversaAuthKeys.add(fullKey);
                }
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao gravar objeto na MemCached", ex);
        }
    }

    public <T> T ler(String classe, String identificador, String key) {
        try {
            if (this.memcachedClient != null) {
                return (T) this.memcachedClient.get(getChaveComIdentificador(classe, identificador, key));
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao ler objeto da MemCached", ex);
        }
        return null;
    }

    private String getChaveComIdentificador(String classe, String identificador, String key) {
        return "[" + classe + "][" + identificador + "][" + key + "]";
    }

    private boolean isServidorMemCachedNaoConfigurado(String ipServidorMemcached) {
        return Util.emptyString(ipServidorMemcached) || ipServidorMemcached.equalsIgnoreCase("DISABLED");
    }

    public void flush() {
        try {
            if (this.memcachedClient != null) {
                this.memcachedClient.flush();
                log.info("Cache memcached limpo com sucesso");
            } else {
                log.warn("MemcachedClient não está configurado, não foi possível limpar o cache");
            }
        } catch (Exception ex) {
            Logger.getLogger(MemCachedManager.class.getName())
                    .log(Level.WARNING, "Falha ao limpar cache do MemCached", ex);
        }
    }

    public void clearPactoConversaAuthKeys() {
        try {
            if (this.memcachedClient == null) {
                log.warn("MemcachedClient não está configurado, não foi possível limpar as chaves do PACTO_CONVERSA_AUTH_SERVICE_IMPL");
                return;
            }

            log.info("Total de chaves a limpar: {}", pactoConversaAuthKeys.size());
            int clearedCount = 0;
            for (String key : new ArrayList<>(pactoConversaAuthKeys)) {
                log.debug("Tentando deletar chave: {}", key);
                boolean deleted = memcachedClient.delete(key).get();
                if (deleted) {
                    clearedCount++;
                } else {
                    log.warn("Falha ao deletar a chave: {}", key);
                }
            }
            pactoConversaAuthKeys.clear();
            log.info("Cache limpo para {} chaves do contexto PACTO_CONVERSA_AUTH_SERVICE_IMPL", clearedCount);
        } catch (OperationTimeoutException e) {
            log.error("Timeout ao tentar limpar chaves do Memcached", e);
        } catch (Exception e) {
            log.error("Erro inesperado ao limpar chaves do Memcached", e);
        }
    }

    public static MemCachedManager getInstance() {
        if (INSTANCE == null)
            INSTANCE = new MemCachedManager();
        return INSTANCE;
    }

}
