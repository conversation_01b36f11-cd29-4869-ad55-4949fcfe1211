app:
  config:
    security:
      allow-origin-list: '*'
  proxy:
    api: classpath:config/proxy/dev/api.yml
    gateway: classpath:config/proxy/dev/gateway.yml
    microsservice: classpath:config/proxy/dev/microsservice.yml
    outside: classpath:config/proxy/dev/outside.yml


logging:
  level:
    ROOT: INFO
    org.hibernate.type: ERROR
    org.springframework.data.r2dbc: ERROR

spring:

  datasource:
    hikari:
      connectionTimeout: 10000
      maximumPoolSize: 10
      idleTimeout: 10000
    oamd-custom:
      driver-class-name: org.postgresql.Driver
      jdbc-url: ************************************************
      username: postgres
      password: pactodb
  devtools:
    restart:
      enabled: false

  output:
    ansi:
      enabled: DETECT

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    platform: postgres

  r2dbc:
    url: r2dbc:pool:postgres://host.docker.internal:5432/OAMD
    username: postgres
    password: pactobr
    properties:
      sslMode: REQUIRE

queues:
  schedule:
    main: teste
    sagas: teste-saga

resilience4j:
  bulkhead:
    instances:
      foo-bar:
        max-concurrent-call: 10

  ratelimiter:
    instances:
      foo-bar:
        limit-for-period: 2
        limit-refresh-period: 10s

  retry:
    instances:
      verificarCadastroContatoMeta:
        max-attempts: 5
        wait-duration: 3000ms
        enable-exponential-backoff: true

secret:
  key:
    value: 29d44e9241da61da882fc62477f7dd73
    path:

discovery:
  url: http://localhost:8101
  vendas-online-url:
  # TODO: Provisorio. É para resolver o problema de não ter essa url por empresa no discovery, e precisamos fazer uma apresentação

propriedades:
  tamanho_msg_sms: 140

autenticacao:
  id: id-dev-localhost

conversas:
  ia:
    key: c301631fbbcaaf87da69bb5193b92d7f9bece486b7f296789298cc4ce2a29aa9

mock:
  companyId: 1
  companyKey: teste
  zwId: 2
  zwProfile: 1
  username: pactobr

memcached:
  servers: "host.docker.internal:11211"

thread:
  pool:
    size: 2

custom:
  async:
    thread-pool-size: 20
