app:
  config:
    security: 
      allow-origin-list: '*'   
  proxy:
    api: classpath:config/proxy/hml/api.yml
    gateway: classpath:config/proxy/hml/gateway.yml
    microsservice: classpath:config/proxy/hml/microsservice.yml
    outside: classpath:config/proxy/hml/outside.yml
    
logging:
  level:
    ROOT: INFO
    org.hibernate.type: TRACE
    org.springframework.data.r2dbc: TRACE


spring:

  datasource:
    hikari:
      connectionTimeout: 20000
      maximumPoolSize: 5
    oamd-custom:
      driver-class-name: org.postgresql.Driver
      jdbc-url: ************************************
      username: postgres
      password: pactodb
    
  output:
    ansi:
      enabled: DETECT  

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: true    
    platform: postgres
    
  r2dbc: 
    url: r2dbc:pool:postgres://swarm3.pactosolucoes.com.br:8002/OAMD
    username: postgres
    password: pactobr
    properties: 
      sslMode: REQUIRE    

resilience4j:
  bulkhead:
    instances:
      foo-bar: 
        max-concurrent-call: 10
        
  ratelimiter:
    instances:
      foo-bar:
        limit-for-period: 1
        limit-refresh-period: 10s
        
  retry: 
    instances:
      foo-bar:
        max-attempts: 3
        wait-duration: 1000ms
        enable-exponential-backoff: true

secret:
  key: 
    value: 29d44e9241da61da882fc62477f7dd73
    path: 
    
discovery:
  url: http://swarm3.pactosolucoes.com.br:8013
  vendas-online-url:
  # TODO: Provisorio. É para resolver o problema de não ter essa url por empresa no discovery, e precisamos fazer uma apresentação

propriedades:
   tamanho_msg_sms: 140

autenticacao:
 id: id-dev-localhost

conversas:
  ia:
    api-url-hml: https://orion-hml.pactosolucoes.com.br
    api-url-prod: https://orion.pactosolucoes.com.br
    key: 328b1f127b6417369ebffc5137f2ad2dda2270ab35971f9eb2759212f29f75fe
    #    Na 300 tava assim
    #    key: ${CONVERSAS_IA_KEY:c301631fbbcaaf87da69bb5193b92d7f9bece486b7f296789298cc4ce2a29aa9}

mock:
  companyId: 1
  companyKey: teste
  zwId: 1
  zwProfile: 1
  username: teste
  apontaSwarm:

memcached:
    servers: "memcached:11211"

thread:
  pool:
    size: 2

custom:
  async:
    thread-pool-size: 20
