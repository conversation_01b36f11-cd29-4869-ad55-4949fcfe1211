version: '@project.version@'

pacto:
  commons:
    white-list-url: v1/health,actuator,v1/ia/conversa/pos-venda

app:
  messages:
    i18n: classpath:i18n/messages/structure/structureMessages, classpath:i18n/messages/business/businessMessages, classpath:i18n/messages/swagger/swaggerMessages 
    validation: classpath:i18n/validation/structure/structureValidations, classpath:i18n/validation/business/businessValidations 
    encoding: UTF-8   
  config:    
    root-package: br.com.pacto.ms.contato
    root-package-oamd-custom:
      active: true
      repository: br.com.pacto.ms.oamd.repository
      entity: br.com.pacto.ms.oamd.domain
    default-return-legacy: true
    security:
      url-free: /v1/health,/actuator,/v1/ia/conversa/pos-venda
          
info:
  app:
    name: '@project.name@'

lombok:
  addSuppressWarnings: false

management:
  health:
    diskspace:
      enabled: true
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      exposure:
        include: '*'

server:
  port: ${SERVER_PORT:8110}
  servlet:
    contextPath: /@project.artifactId@
  tomcat:
    relaxed-query-chars: [ "|", "{", "}", "[", "]" ]
  
spring:
  jackson:
    time-zone: America/Sao_Paulo
  main:
    allow-bean-definition-overriding: true
  output:
    ansi:
      enabled: ALWAYS
      
  banner:
    location: classpath:config/banner/pactosolucoes.txt

  profiles:
    active: ${PROFILE:@profile.active@}

  application:
    name: '@project.name@'
    
    
  jpa:
    show-sql: false
    database-platform: org.hibernate.dialect.PostgreSQLDialect
    hibernate:
      ddl-auto: none
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    properties:
      hibernate:
        format_sql: true
        temp:
          use_jdbc_metadata_defaults: false    

    
springdoc:
  writer-with-default-pretty-printer: true
   
  api-docs:
    path: /<EMAIL>@-docs
    
  swagger-ui:
    layout: BaseLayout
    path: /swagger
    operationsSorter: alpha
    tagsSorter: alpha
    displayRequestDuration: true

# SWAGGER CONFIGURATION

swagger:
  config:
    info: 
      title: '@project.artifactId@'
      description: List of Methods for @project.artifactId@'s project. <br /> @project.description@  
      version: v1
      license-name: Apache 2.0
      license-url: http://springdoc.org

    methods:
      response:    
        GET:
          codes: 200, 400, 401, 403, 404, 417, 500, 501, 504
        POST:
          codes: 201, 400, 401, 403, 404, 417, 500, 501, 504       
        PUT:
          codes: 200, 400, 401, 403, 404, 417, 500, 501, 504
        PATCH:
          codes: 200, 400, 401, 403, 404, 417, 500, 501, 504
        DELETE:
          codes: 200, 400, 401, 403, 404, 417, 500, 501, 504   

conversas:
  ia:
    url: @conversas.ai.url@
    api-url-hml: https://orion-hml.pactosolucoes.com.br
    api-url-prod: https://orion.pactosolucoes.com.br
    token-by-company: false

mock:
  companyId: 1
  companyKey: e0aa2e5c7ed462647540be7a58465a26
  zwId: 1
  zwProfile: 1
  username: teste

feign:
  okhttp:
    enabled: true
  client:
    config:
      default:
        loggerLevel: FULL

memcached:
  servers: "host.docker.internal:11211"
