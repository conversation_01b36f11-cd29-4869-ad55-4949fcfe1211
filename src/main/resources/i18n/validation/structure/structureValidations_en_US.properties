#####################################################################################################################################
# Structure Internationalization of Spring's validation.    																		#
#####################################################################################################################################

javax.validation.constraints.AssertTrue.message=Field $field must be true.
javax.validation.constraints.AssertFalse.message=Field $field must be false.
javax.validation.constraints.DecimalMax.message=Field $field must be less than {value}, but the value ${validatedValue} was informed.
javax.validation.constraints.DecimalMin.message=Field $field should be more than {value}, but the value ${validatedValue} was informed.
javax.validation.constraints.Digits.message=Field $field must have {integer} integer an {fraction} fraction.
javax.validation.constraints.Email.message=Field $field must be an valid e-mail, but the value ${validatedValue} was informed.
javax.validation.constraints.Future.message=Field $field must be a date in the future, but the value ${validatedValue} was informed.
javax.validation.constraints.FutureOrPresent.message=Field $field must be a date in the present or future, but the value ${validatedValue} was informed.
javax.validation.constraints.Max.message=Field $field must be less or equal than {value}, but the value ${validatedValue} was informed.
javax.validation.constraints.Min.message=Field $field must be more than {value}, but the value ${validatedValue} was informed.
javax.validation.constraints.Negative.message=Field $field must be negative.
javax.validation.constraints.NegativeOrZero.message=Field $field must be less or equals zero.
javax.validation.constraints.NotBlank.message=Field $field cannot be empty.
javax.validation.constraints.NotEmpty.message=Field $field cannot be empty or null.
javax.validation.constraints.NotNull.message=Field $field cannot be null.
javax.validation.constraints.Past.message=Field $field must be date in the past, but the value ${validatedValue} was informed .
javax.validation.constraints.PastOrPresent.message=Field $field must be a future or past date, but the value ${validatedValue} was informed .
javax.validation.constraints.Pattern.message=The field's value of $field has an invalid value ${validatedValue}, but the value must be like this ({regexp}).
javax.validation.constraints.Positive.message=Field $field must be positive.
javax.validation.constraints.PositiveOrZero.message=Field $field must be more or equals zero.
javax.validation.constraints.Size.message=The field's lenght of $field should be between {min} and {max} character.
org.hibernate.validator.constraints.Length.message=The field's length of $field cannot be more than {max} character.
org.hibernate.validator.constraints.Range.message=Field $field should be between {min} and {max}, but the value ${validatedValue} was informed.
br.com.pacto.validation.Cpfcnpj.message=Field $field must be a CPF/CNPJ valid.
br.com.pacto.validation.ListValues.message=Field $field value ${validatedValue} is not a present value in the list value {value}
br.com.pacto.validation.Email.message=Field $field must be an valid e-mail, but the value ${validatedValue} was informed.
br.com.pacto.validation.Phone.message=Field $field value ${validatedValue} is not an Brazilian phone.