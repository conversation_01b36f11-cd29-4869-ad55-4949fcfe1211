#####################################################################################################################################
# Structure Internationalization of Spring's validation.    																		#
#####################################################################################################################################

javax.validation.constraints.AssertTrue.message=分野 $field 真実でなければならない
javax.validation.constraints.AssertFalse.message=分野 $field falseである必要があります。
javax.validation.constraints.DecimalMax.message=分野 $field 未満である必要があります {value}, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.DecimalMin.message=分野 $field 以上である必要があります {value}, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.Digits.message=分野 $field 持つ必要があります {integer} 整数 {fraction} 分数.
javax.validation.constraints.Email.message=分野 $field 有効な電子メールである必要があります, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.Future.message=分野 $field 将来の日付である必要があります, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.FutureOrPresent.message=分野 $field 現在または将来の日付である必要があります, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.Max.message=分野 $field 以下でなければなりません {value}, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.Min.message=分野 $field 以上でなければなりません {value}, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.Negative.message=分野 $field 負である必要があります。
javax.validation.constraints.NegativeOrZero.message=分野 $field ゼロ以下である必要があります。
javax.validation.constraints.NotBlank.message=分野 $field 空にすることはできません。
javax.validation.constraints.NotEmpty.message=分野 $field 空またはnullにすることはできません。
javax.validation.constraints.NotNull.message=分野 $field nullにすることはできません。
javax.validation.constraints.Past.message=分野 $field 過去の日付である必要があります, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.PastOrPresent.message=分野 $field 将来または過去の日付である必要があります, しかし、${validatedValue} 値は通知されました。.
javax.validation.constraints.Pattern.message=分野 $field の値に無効な値 ${validatedValue} がありますが、値は次のようになっている必要があります ({regexp}).
javax.validation.constraints.Positive.message=分野 $field 正でなければなりません
javax.validation.constraints.PositiveOrZero.message=分野 $field ゼロ以上である必要があります。
javax.validation.constraints.Size.message=分野 の長さ $field 間にある必要があります {min} と {max} キャラクター..
org.hibernate.validator.constraints.Length.message=分野 の長さ $field を超えることはできません {max} キャラクター.
org.hibernate.validator.constraints.Range.message=分野 $field 間にある必要があります {min} と {max}, しかし、${validatedValue} 値は通知されました。.
br.com.pacto.validation.Cpfcnpj.message=分野 $field 有効なCPF/CNPJである必要があります。
br.com.pacto.validation.ListValues.message=分野 $field 価値 ${validatedValue} リスト値の現在価値ではありません {value}
br.com.pacto.validation.Email.message=分野 $field 有効な電子メールである必要がありますが、値は ${validatedValue} 知らされた。
br.com.pacto.validation.Phone.message=分野 $field 価値 ${validatedValue} ブラジルの電話ではありません。