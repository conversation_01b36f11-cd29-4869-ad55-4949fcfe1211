#####################################################################################################################################
# Structure Internationalization of Spring's validation.    																		#
#####################################################################################################################################

javax.validation.constraints.AssertTrue.message=O campo $field deve ser verdadeiro.
javax.validation.constraints.AssertFalse.message=O campo $field deve ser falso.
javax.validation.constraints.DecimalMax.message=O campo $field deveria ser menor que {value}, mas foi passado o valor ${validatedValue}.
javax.validation.constraints.DecimalMin.message=O campo $field deveria ser maior que {value}, mas foi passado o valor ${validatedValue}.
javax.validation.constraints.Digits.message=O campo $field deve conter {integer} inteiro(s) e {fraction} casas decimal(is).
javax.validation.constraints.Email.message=O campo $field com o e-mail ${validatedValue} não é válido.
javax.validation.constraints.Future.message=O campo $field deveria ser uma data no futuro, mas veio com a data ${validatedValue}.
javax.validation.constraints.FutureOrPresent.message=O campo $field deveria ser uma data no presente ou futuro, mas veio com a data ${validatedValue}.
javax.validation.constraints.Max.message=O campo $field deveria ser menor ou igual a {value}, mas foi passado o valor ${validatedValue}.
javax.validation.constraints.Min.message=O campo $field deveria ser maior ou igual a {value}, mas foi passado o valor ${validatedValue}.
javax.validation.constraints.Negative.message=O campo $field deve ser negativo.
javax.validation.constraints.NegativeOrZero.message=O campo $field dever ser menor ou igual a zero.
javax.validation.constraints.NotBlank.message=O campo $field não pode ser vazio.
javax.validation.constraints.NotEmpty.message=O campo $field não pode estar vazio ou null.
javax.validation.constraints.NotNull.message=O campo $field não pode ser null.
javax.validation.constraints.Past.message=O campo $field deveria ser uma data no passado, mas veio com a data ${validatedValue}.
javax.validation.constraints.PastOrPresent.message=O campo $field deveria ser uma data no presente ou no passado, mas veio com a data ${validatedValue}.
javax.validation.constraints.Pattern.message=O campo $field com o valor ${validatedValue} não respeita as regras estruturais {regexp}.
javax.validation.constraints.Positive.message=O Campo $field deve ser positivo.
javax.validation.constraints.PositiveOrZero.message=O campo $field deve ser maior ou igual a zero.
javax.validation.constraints.Size.message=O campo $field deve ter o comprimento entre {min} e {max} caracteres.
org.hibernate.validator.constraints.Length.message=O comprimento do campo $field não pode ser superior a {max} caracter(es).
org.hibernate.validator.constraints.Range.message=O campo $field deveria estar entre {min} e {max}, mas foi passado o valor ${validatedValue}.
br.com.pacto.validation.Cpfcnpj.message=O campo $field do tipo CPF/CNPJ deve conter um valor válido.
br.com.pacto.validation.ListValues.message=O campo $field com o valor ${validatedValue} não está na lista de valores aceitos {value}
br.com.pacto.validation.Email.message=O campo $field deve conter um valor válido.
br.com.pacto.validation.Phone.message=O campo $field com o valor ${validatedValue} não é um telefone válido.