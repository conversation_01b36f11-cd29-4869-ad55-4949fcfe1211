swagger.status.200=OK
swagger.status.201=Data was created successfully.
swagger.status.204=Action was performed successfully, but there is no content to display.
swagger.status.400=Error occurred due to some incorrect user information.
swagger.status.401=User not recognized or expired.
swagger.status.403=No permission to access the method.
swagger.status.404=Page or method not found.
swagger.status.417=Some information in the header was expected, but not found.
swagger.status.500=An internal error occurred and was not dealt with. You will need to check the logs on the ELK.
swagger.status.501=Feature or method not implemented.
swagger.status.502=The external resource sought (API, Micro Services or Getways) is unable to return data.
swagger.status.504=Request timed out.