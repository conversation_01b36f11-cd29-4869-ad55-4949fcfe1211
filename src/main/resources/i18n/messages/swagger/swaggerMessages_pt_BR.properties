swagger.status.200=OK
swagger.status.201=Os dados foram criados com sucesso.
swagger.status.204=Ação foi realizada com sucesso, porém não há conteúdo a ser exibido.
swagger.status.400=Erro ocorrido devido há alguma informação incorreta do usuário.
swagger.status.401=Usuário não reconhecido ou expirado.
swagger.status.403=Sem permissão para acessar o método.
swagger.status.404=Não foi possível encontrar a informação pesquisada.
swagger.status.417=Estavam sendo esperadas algumas informações no cabeçalho, mas não foram econtradas.
swagger.status.500=Um erro interno aconteceu e não foi trato. Será necessário verificar os logs no ELK.
swagger.status.501=Recurso/método não implementado.
swagger.status.502=O recurso externo procurado (API, Micros Serviços ou Getways) estão impossibilitados de retornar os dados.
swagger.status.504=Excedido o tempo limite da requisição.