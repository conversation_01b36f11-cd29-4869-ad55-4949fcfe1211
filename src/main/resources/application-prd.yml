app:
  config:
    security:
      allow-origin-list: '*'
  proxy:
    api: classpath:config/proxy/prd/api.yml
    gateway: classpath:config/proxy/prd/gateway.yml
    microsservice: classpath:config/proxy/prd/microsservice.yml
    outside: classpath:config/proxy/prd/outside.yml

server:
  port: @server.port@
  servlet:
    contextPath: /@project.artifactId@

logging:
  level:
    ROOT: INFO
    org.hibernate.type: ERROR
    org.springframework.data.r2dbc: ERROR

spring:

  datasource:
    hikari:
      connectionTimeout: 10000
      idleTimeout: 10000
      leak-detection-threshold: 10000
      minimum-idle: 1
    oamd-custom:
      driver-class-name: org.postgresql.Driver
      jdbc-url: @spring.datasource.oamd.jdbc-url@?ApplicationName=oamd_crm-ms
      username: postgres
      password: pactodb

  output:
    ansi:
      enabled: DETECT

  jpa:
    hibernate:
      ddl-auto: none
    show-sql: false
    platform: postgres

  r2dbc:
    url: @spring.datasource.oamd.jdbc-url@
    username: postgres
    password: pactobr
    properties:
      sslMode: REQUIRE

resilience4j:
  bulkhead:
    instances:
      foo-bar:
        max-concurrent-call: 10

  ratelimiter:
    instances:
      foo-bar:
        limit-for-period: 2
        limit-refresh-period: 10s

#  retry:
#    instances:
#      xxxxxxxxxx:
#        max-attempts: XXX
#        wait-duration: XXXXms
#        enable-exponential-backoff: true

secret:
  key:
    value: '@secret.key.value@'
    path:

discovery:
  url: https://discovery.ms.pactosolucoes.com.br
  vendas-online-url: @discovery.vendas-online-url@
  # TODO: Provisorio. É para resolver o problema de não ter essa url por empresa no discovery, e precisamos fazer uma apresentação

propriedades:
  tamanho_msg_sms: 140

conversas:
  ia:
    api-url-hml: https://orion-hml.pactosolucoes.com.br
    api-url-prod: https://orion.pactosolucoes.com.br
    key: @conversas.ia.key@
    url: @conversas.ai.url@

mock:
  companyId: 1
  companyKey: teste
  zwId: 1
  zwProfile: 1
  username: pactobr

autenticacao:
  id: '@persona-secret@'

memcached:
  servers: "localhost:11211"

thread:
  pool:
    size: 2

custom:
  async:
    thread-pool-size: 20
