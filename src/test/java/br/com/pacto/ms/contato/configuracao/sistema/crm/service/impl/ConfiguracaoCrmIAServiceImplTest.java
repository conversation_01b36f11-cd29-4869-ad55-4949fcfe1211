//package br.com.pacto.ms.contato.configuracao.sistema.crm.service.impl;
//
//import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmFaseIAEntity;
//import br.com.pacto.ms.contato.configuracao.sistema.crm.data.domain.ConfiguracaoCrmIAEntity;
//import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoCrmFaseIADTO;
//import br.com.pacto.ms.contato.configuracao.sistema.crm.data.pojo.input.configuracao.ConfiguracaoInstanciaDTO;
//import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmFaseIARepository;
//import br.com.pacto.ms.contato.configuracao.sistema.crm.data.repository.ConfiguracaoCrmIARepository;
//import br.com.pacto.ms.contato.core.data.pojo.enums.AmbienteEnum;
//import br.com.pacto.ms.contato.core.data.pojo.enums.FasesCRMEnum;
//import br.com.pacto.ms.contato.ia.data.pojo.output.InstanciaVO;
//import br.com.pacto.ms.contato.ia.data.proxy.proxy.PactoConversasIAProxy;
//import br.com.pacto.ms.contato.ia.service.contract.ContextoService;
//import br.com.pacto.ms.contato.ia.utils.Util;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.ArgumentMatchers;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import java.util.*;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
//@SuppressWarnings("unchecked")
//@ExtendWith(MockitoExtension.class)
//class ConfiguracaoCrmIAServiceImplTest {
//
//    @InjectMocks
//    private ConfiguracaoCrmIAServiceImpl configuracaoCrmIAService;
//
//    @Mock
//    private ConfiguracaoCrmFaseIARepository crmFaseIARepository;
//
//    @Mock
//    private ContextoService contextoService;
//
//    @Mock
//    private ConfiguracaoCrmIARepository configuracaoCrmIARepository;
//
//    @Mock
//    private PactoConversasIAProxy pactoConversasIAProxy;
//
//
//    @Mock
//    private Util util;
//
//    private ConfiguracaoInstanciaDTO configuracaoInstanciaDTO;
//
//    @BeforeEach
//    void setup() {
//        configuracaoInstanciaDTO = new ConfiguracaoInstanciaDTO();
//        configuracaoInstanciaDTO.setNomeEmpresa("Empresa Teste");
//        configuracaoInstanciaDTO.setWhatsappBusiness(true);
//        configuracaoInstanciaDTO.setIsDevice(true);
//        configuracaoInstanciaDTO.setCodigoEmpresa(1);
//        configuracaoInstanciaDTO.setFlagNovaInstancia(false);
//    }
//
//    @Test
//    void incluirConfiguracaoDeFases() {
//        this.incluirFases_DeveSalvarEAtualizarConfiguracoesCorretamente();
//    }
//
//    void incluirFases_DeveSalvarEAtualizarConfiguracoesCorretamente() {
//        Integer codigoEmpresa = 1;
//
//        List<ConfiguracaoCrmFaseIAEntity> existentesNoBanco = Arrays.asList(
//                ConfiguracaoCrmFaseIAEntity.builder()
//                        .codigo(10)
//                        .fase(FasesCRMEnum.VISITA_RECORRENTE)
//                        .habilitar(true)
//                        .codigoEmpresa(codigoEmpresa)
//                        .build(),
//                ConfiguracaoCrmFaseIAEntity.builder()
//                        .codigo(20)
//                        .codigoMetaExtra(1001)
//                        .nomeMetaExtra("Custom Existente")
//                        .habilitar(true)
//                        .codigoEmpresa(codigoEmpresa)
//                        .build()
//        );
//
//        when(crmFaseIARepository.findAllAndCompany(codigoEmpresa)).thenReturn(existentesNoBanco);
//
//        List<ConfiguracaoCrmFaseIADTO> fasesRecebidas = Arrays.asList(
//                ConfiguracaoCrmFaseIADTO.builder()
//                        .fase(FasesCRMEnum.VISITA_RECORRENTE)
//                        .descricao("Descrição atualizada fase padrão")
//                        .habilitar(false)
//                        .build(),
//                ConfiguracaoCrmFaseIADTO.builder()
//                        .codigometaextra(1002)
//                        .nomemetaextra("Custom Nova")
//                        .descricao("Descrição customizada nova")
//                        .habilitar(true)
//                        .build()
//        );
//
//        List<ConfiguracaoCrmFaseIADTO> resultado = (List<ConfiguracaoCrmFaseIADTO>)
//                configuracaoCrmIAService.incluirConfiguracaoDeFases(fasesRecebidas, codigoEmpresa, AmbienteEnum.prod);
//
//        verify(crmFaseIARepository, times(2)).save(any(ConfiguracaoCrmFaseIAEntity.class));
//        verify(contextoService).atualizarContextoFases(codigoEmpresa, AmbienteEnum.prod);
//
//        assertNotNull(resultado);
//        assertEquals(FasesCRMEnum.values().length + 1, resultado.size()); //add mais 1
//        assertTrue(resultado.stream().anyMatch(f ->
//                f.getCodigo() != null && f.getCodigo().equals(10) &&
//                        "Descrição atualizada fase padrão".equals(f.getDescricao())
//        ));
//    }
//
//    @Test
//    void criarInstancia() throws Exception {
//        deveCriarNovaInstancia_quandoNaoExistir();
//        deveRetornarInstanciaExistente_quandoJaExistirEValida();
//        verificarStatusInstancia_DeveRetornarTrue_quandoInstanciaConectada();
//        verificarStatusInstancia_DeveRetornarFalse_quandoInstanciaNaoConectada();
//    }
//
//    @Test
//    void deveCriarNovaInstancia_quandoNaoExistir() throws Exception {
//        AmbienteEnum ambiente = AmbienteEnum.prod;
//        when(configuracaoCrmIARepository.findByCodigoEmpresa(1)).thenReturn(Optional.empty());
//
//        when(configuracaoCrmIARepository.save(any(ConfiguracaoCrmIAEntity.class)))
//                .thenAnswer(invocation -> invocation.getArgument(0));
//
//        HashMap<String, Object> mockResponse = new HashMap<>();
//        mockResponse.put("id", "instanciaId");
//        mockResponse.put("token", "instanciaToken");
//
//        ObjectMapper mapper = new ObjectMapper();
//        when(pactoConversasIAProxy.criarInstancia(any(), (HashMap<String, Object>) ArgumentMatchers.<String, Object>anyMap()))
//                .thenReturn(mapper.writeValueAsString(mockResponse));
//
//        InstanciaVO instanciaVO = (InstanciaVO) configuracaoCrmIAService.criarInstancia(ambiente, configuracaoInstanciaDTO);
//
//        assertNotNull(instanciaVO);
//        assertEquals("instanciaId", instanciaVO.getId());
//        assertEquals("instanciaToken", instanciaVO.getToken());
//        verify(configuracaoCrmIARepository).save(any(ConfiguracaoCrmIAEntity.class));
//    }
//
//    @Test
//    void deveRetornarInstanciaExistente_quandoJaExistirEValida() {
//        AmbienteEnum ambiente = AmbienteEnum.prod;
//
//        ConfiguracaoCrmIAEntity entity = new ConfiguracaoCrmIAEntity();
//        entity.setIdInstanciaZApi("idExistente");
//        entity.setTokenZApi("tokenExistente");
//        entity.setWhatsappBusiness(true);
//
//        entity.setWhatsappBusiness(true);
//        when(configuracaoCrmIARepository.findByCodigoEmpresa(1))
//                .thenReturn(Optional.of(Collections.singletonList(entity)));
//        String jsonMock = "{\"phone\":\"556299999999\"}";
//        when(pactoConversasIAProxy.checkarStatusInstancia(util.getBaseUrl(ambiente), "idExistente", "tokenExistente"))
//                .thenReturn(jsonMock);
//        Boolean status = configuracaoCrmIAService
//                .verificarStatusInstancia(ambiente, "idExistente", "tokenExistente");
//
//        InstanciaVO instanciaVO = (InstanciaVO) configuracaoCrmIAService.criarInstancia(ambiente, configuracaoInstanciaDTO);
//        assertTrue(status);
//        assertNotNull(instanciaVO);
//        assertEquals("idExistente", instanciaVO.getId());
//        assertEquals("tokenExistente", instanciaVO.getToken());
//    }
//
//    @Test
//    void verificarStatusInstancia_DeveRetornarTrue_quandoInstanciaConectada() throws Exception {
//        AmbienteEnum ambiente = AmbienteEnum.prod;
//        String jsonMock = "{\"phone\":\"556299999999\"}";
//
//        when(pactoConversasIAProxy.checkarStatusInstancia(util.getBaseUrl(ambiente), "idTeste", "tokenTeste"))
//                .thenReturn(jsonMock);
//
//        Boolean status = configuracaoCrmIAService.verificarStatusInstancia(ambiente, "idTeste", "tokenTeste");
//
//        assertTrue(status);
//    }
//
//    @Test
//    void verificarStatusInstancia_DeveRetornarFalse_quandoInstanciaNaoConectada() throws Exception {
//        AmbienteEnum ambiente = AmbienteEnum.prod;
//        String jsonMock = "{\"status\":\"not_connected\"}";
//
//        when(pactoConversasIAProxy.checkarStatusInstancia(util.getBaseUrl(ambiente), "idTeste", "tokenTeste"))
//                .thenReturn(jsonMock);
//
//        Boolean status = configuracaoCrmIAService.verificarStatusInstancia(ambiente, "idTeste", "tokenTeste");
//
//        assertFalse(status);
//    }
//}
